/* Modern Design System Tokens 2024 */

:root {
  /* Brand Colors - Modern Blue Palette inspired by Apple & Microsoft */
  --color-primary: #007AFF;
  --color-primary-50: #F0F9FF;
  --color-primary-100: #E0F2FE;
  --color-primary-200: #BAE6FD;
  --color-primary-300: #7DD3FC;
  --color-primary-400: #38BDF8;
  --color-primary-500: #007AFF;
  --color-primary-600: #0284C7;
  --color-primary-700: #0369A1;
  --color-primary-800: #075985;
  --color-primary-900: #0C4A6E;
  --color-primary-950: #082F49;

  /* Semantic Colors - Modern palette */
  --color-success: #34C759;
  --color-success-50: #F0FDF4;
  --color-success-100: #DCFCE7;
  --color-success-500: #34C759;
  --color-success-600: #16A34A;
  --color-success-700: #15803D;

  --color-warning: #FF9500;
  --color-warning-50: #FFFBEB;
  --color-warning-100: #FEF3C7;
  --color-warning-500: #FF9500;
  --color-warning-600: #D97706;
  --color-warning-700: #B45309;

  --color-danger: #FF3B30;
  --color-danger-50: #FEF2F2;
  --color-danger-100: #FEE2E2;
  --color-danger-500: #FF3B30;
  --color-danger-600: #DC2626;
  --color-danger-700: #B91C1C;

  --color-info: #8E8E93;
  --color-info-50: #F8FAFC;
  --color-info-100: #F1F5F9;
  --color-info-500: #8E8E93;

  /* Legacy aliases for backward compatibility */
  --color-primary-dark: var(--color-primary-700);
  --color-primary-light: var(--color-primary-100);
  --color-success-dark: var(--color-success-700);
  --color-success-light: var(--color-success-50);
  --color-warning-dark: var(--color-warning-700);
  --color-warning-light: var(--color-warning-50);
  --color-danger-dark: var(--color-danger-700);
  --color-danger-light: var(--color-danger-50);
  --color-info-light: var(--color-info-50);

  /* Neutral Scale - Modern grayscale based on Apple Human Interface Guidelines */
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F4F4F5;
  --color-gray-200: #E4E4E7;
  --color-gray-300: #D4D4D8;
  --color-gray-400: #A1A1AA;
  --color-gray-500: #71717A;
  --color-gray-600: #52525B;
  --color-gray-700: #3F3F46;
  --color-gray-800: #27272A;
  --color-gray-900: #18181B;
  --color-gray-950: #09090B;

  /* Text Colors - Enhanced contrast ratios */
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-700);
  --color-text-tertiary: var(--color-gray-500);
  --color-text-quaternary: var(--color-gray-400);
  --color-text-inverse: #FFFFFF;
  
  /* Legacy aliases */
  --color-text-1: var(--color-text-primary);
  --color-text-2: var(--color-text-secondary);
  --color-text-3: var(--color-text-tertiary);

  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  --bg-quaternary: var(--color-gray-200);
  
  /* Legacy aliases */
  --bg-1: var(--bg-primary);
  --bg-2: var(--bg-secondary);
  --bg-3: var(--bg-tertiary);

  /* Border Colors */
  --border-primary: var(--color-gray-200);
  --border-secondary: var(--color-gray-300);
  --border-tertiary: var(--color-gray-400);
  
  /* Legacy aliases */
  --border-color: var(--border-primary);
  --border-1: var(--border-primary);
  --border-2: var(--border-secondary);

  /* Modern Gradients & Glass Effects */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-400) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-success-500) 0%, #22C55E 100%);
  --gradient-brand: var(--gradient-primary);
  
  /* Glassmorphism Effects - iOS inspired */
  --glass-bg: rgba(255, 255, 255, 0.75);
  --glass-bg-strong: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(255, 255, 255, 0.25);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-shadow-strong: 0 16px 64px rgba(0, 0, 0, 0.15);

  /* Surface Effects */
  --surface-glass: backdrop-filter: blur(16px); -webkit-backdrop-filter: blur(16px);
  --surface-elevated: 0 2px 8px rgba(0, 0, 0, 0.08);
  --surface-floating: 0 8px 32px rgba(0, 0, 0, 0.12);

  /* Typography - SF Pro & Inter inspired system */
  --font-family-display: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", 
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", 
    "Helvetica Neue", sans-serif;
  --font-family-text: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", 
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", 
    "Helvetica Neue", sans-serif;
  --font-family-mono: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", 
    "Consolas", "Courier New", monospace;
    
  /* Modern type scale - 1.25 major third */
  --font-size-2xs: 11px;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;
  --font-size-5xl: 48px;
  --font-size-6xl: 64px;
  --font-size-7xl: 80px;
  --font-size-8xl: 96px;
  
  /* Font weights - Apple system */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-heavy: 800;
  --font-weight-black: 900;
  
  /* Line heights - optimized for readability */
  --line-height-none: 1;
  --line-height-tight: 1.2;
  --line-height-snug: 1.35;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 2;
  
  /* Legacy aliases */
  --font-sans: var(--font-family-text);
  --line-height-base: var(--line-height-normal);

  /* Spacing Scale - 8pt grid system */
  --spacing-0: 0px;
  --spacing-0_5: 2px;
  --spacing-1: 4px;
  --spacing-1_5: 6px;
  --spacing-2: 8px;
  --spacing-2_5: 10px;
  --spacing-3: 12px;
  --spacing-3_5: 14px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-7: 28px;
  --spacing-8: 32px;
  --spacing-9: 36px;
  --spacing-10: 40px;
  --spacing-11: 44px;
  --spacing-12: 48px;
  --spacing-14: 56px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;
  --spacing-28: 112px;
  --spacing-32: 128px;
  --spacing-36: 144px;
  --spacing-40: 160px;
  --spacing-44: 176px;
  --spacing-48: 192px;
  --spacing-52: 208px;
  --spacing-56: 224px;
  --spacing-60: 240px;
  --spacing-64: 256px;
  --spacing-72: 288px;
  --spacing-80: 320px;
  --spacing-96: 384px;
  
  /* Legacy aliases */
  --space-1: var(--spacing-1);
  --space-2: var(--spacing-2);
  --space-3: var(--spacing-3);
  --space-4: var(--spacing-4);
  --space-5: var(--spacing-5);
  --space-6: var(--spacing-6);

  /* Border Radius - Modern approach */
  --radius-none: 0px;
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-base: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-4xl: 32px;
  --radius-full: 9999px;
  
  /* Legacy alias */
  --radius-round: var(--radius-full);

  /* Modern Shadow System - Elevation based */
  --shadow-none: none;
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 50px 100px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  
  /* Focus rings */
  --focus-ring: 0 0 0 3px var(--color-primary-100);
  --focus-ring-danger: 0 0 0 3px var(--color-danger-100);

  /* Layout & Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1400px;
  --container-max-width: var(--container-xl);

  /* Breakpoints - Mobile first approach */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Legacy aliases */
  --bp-sm: var(--breakpoint-sm);
  --bp-md: var(--breakpoint-md);
  --bp-lg: var(--breakpoint-lg);
  --bp-xl: var(--breakpoint-xl);

  /* Z-index Scale */
  --z-hide: -1;
  --z-base: 0;
  --z-docked: 10;
  --z-dropdown: 1000;
  --z-sticky: 1100;
  --z-banner: 1200;
  --z-overlay: 1300;
  --z-modal: 1400;
  --z-popover: 1500;
  --z-skiplink: 1600;
  --z-toast: 1700;
  --z-tooltip: 1800;
  
  /* Legacy aliases */
  --z-nav: var(--z-sticky);

  /* Animation & Motion */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 750ms;
  
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Transition Presets */
  --transition-fast: var(--duration-fast) var(--ease-out);
  --transition-base: var(--duration-normal) var(--ease-out);
  --transition-slow: var(--duration-slow) var(--ease-out);
  --transition-all: all var(--transition-base);
  --transition-colors: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
  --transition-shadow: box-shadow var(--transition-fast);
  --transition-transform: transform var(--transition-fast);
}

/* Dark Theme - Modern dark color palette */
[data-theme='dark'] {
  /* Override neutral colors for dark theme */
  --color-gray-50: #18181B;
  --color-gray-100: #27272A;
  --color-gray-200: #3F3F46;
  --color-gray-300: #52525B;
  --color-gray-400: #71717A;
  --color-gray-500: #A1A1AA;
  --color-gray-600: #D4D4D8;
  --color-gray-700: #E4E4E7;
  --color-gray-800: #F4F4F5;
  --color-gray-900: #FAFAFA;

  /* Text Colors */
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-700);
  --color-text-tertiary: var(--color-gray-500);
  --color-text-quaternary: var(--color-gray-400);
  --color-text-inverse: var(--color-gray-900);
  
  /* Legacy aliases */
  --color-text-1: var(--color-text-primary);
  --color-text-2: var(--color-text-secondary);
  --color-text-3: var(--color-text-tertiary);

  /* Background Colors */
  --bg-primary: var(--color-gray-50);
  --bg-secondary: var(--color-gray-100);
  --bg-tertiary: var(--color-gray-200);
  --bg-quaternary: var(--color-gray-300);
  
  /* Legacy aliases */
  --bg-1: var(--bg-primary);
  --bg-2: var(--bg-secondary);
  --bg-3: var(--bg-tertiary);

  /* Border Colors */
  --border-primary: var(--color-gray-200);
  --border-secondary: var(--color-gray-300);
  --border-tertiary: var(--color-gray-400);
  
  /* Legacy aliases */
  --border-color: var(--border-primary);
  --border-1: var(--border-primary);
  --border-2: var(--border-secondary);

  /* Glass Effects for Dark Mode */
  --glass-bg: rgba(39, 39, 42, 0.8);
  --glass-bg-strong: rgba(39, 39, 42, 0.9);
  --glass-border: rgba(113, 113, 122, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-shadow-strong: 0 16px 64px rgba(0, 0, 0, 0.4);

  /* Update shadows for dark theme */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.15);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.15);
  --shadow-base: 0 4px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
  --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.2), 0 4px 6px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.35), 0 10px 20px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 50px 100px rgba(0, 0, 0, 0.5);
}



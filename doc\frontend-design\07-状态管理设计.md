# 状态管理设计

## 🏪 状态管理架构

### 1. 状态管理原则

#### 1.1 设计理念
- **单一数据源**: 每个状态都有唯一的数据源
- **状态不可变**: 通过创建新状态而非修改现有状态
- **可预测性**: 相同的输入产生相同的输出
- **可追踪性**: 状态变化可以被监控和调试

#### 1.2 状态分层
```
状态管理分层
├── 全局状态 (Global State)
│   ├── 用户认证状态
│   ├── 应用配置状态
│   ├── 全局错误状态
│   └── 全局加载状态
├── 功能状态 (Feature State)
│   ├── 项目管理状态
│   ├── 版本管理状态
│   ├── 文件上传状态
│   └── 搜索状态
├── 页面状态 (Page State)
│   ├── 页面数据状态
│   ├── 页面UI状态
│   └── 页面缓存状态
└── 组件状态 (Component State)
    ├── 组件内部状态
    ├── 表单状态
    └── 临时UI状态
```

### 2. Pinia Store 架构

#### 2.1 Store 模块划分
```typescript
// Store 模块结构
interface StoreModules {
  auth: AuthStore          // 认证状态管理
  app: AppStore           // 应用全局状态
  projects: ProjectsStore  // 项目管理状态
  releases: ReleasesStore  // 版本发布状态
  uploads: UploadsStore    // 文件上传状态
  search: SearchStore      // 搜索状态
  users: UsersStore        // 用户管理状态（Admin）
  permissions: PermissionsStore // 权限管理状态
}
```

#### 2.2 Store 基础结构
```typescript
// Store 基础接口
interface BaseStore<T> {
  // 状态
  state: T
  loading: boolean
  error: string | null
  
  // 计算属性
  readonly isLoading: boolean
  readonly hasError: boolean
  readonly isEmpty: boolean
  
  // 基础操作
  setLoading(loading: boolean): void
  setError(error: string | null): void
  clearError(): void
  reset(): void
}
```

## 🔐 认证状态管理

### 1. Auth Store 设计

#### 1.1 状态定义
```typescript
interface AuthState {
  // 用户信息
  user: User | null
  token: string | null
  refreshToken: string | null
  
  // 权限信息
  permissions: string[]
  projects: number[]
  
  // 状态标识
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // 登录会话
  sessionExpiry: number | null
  lastActivity: number
}

interface User {
  id: number
  username: string
  email: string
  role: 'admin' | 'developer'
  status: 'active' | 'disabled'
  createdAt: string
  lastLoginAt: string
}
```

#### 1.2 Actions 设计
```typescript
export const useAuthStore = defineStore('auth', () => {
  // 状态
  const state = reactive<AuthState>({
    user: null,
    token: storage.get('token'),
    refreshToken: storage.get('refreshToken'),
    permissions: [],
    projects: [],
    isAuthenticated: false,
    isLoading: false,
    error: null,
    sessionExpiry: null,
    lastActivity: Date.now()
  })

  // 计算属性
  const isAdmin = computed(() => state.user?.role === 'admin')
  const isDeveloper = computed(() => state.user?.role === 'developer')
  const hasProjectAccess = computed(() => (projectId: number) => {
    return isAdmin.value || state.projects.includes(projectId)
  })

  // 登录操作
  const login = async (credentials: LoginCredentials) => {
    state.isLoading = true
    state.error = null

    try {
      const response = await authAPI.login(credentials)
      const { user, token, refreshToken, permissions, projects } = response.data

      // 更新状态
      state.user = user
      state.token = token
      state.refreshToken = refreshToken
      state.permissions = permissions
      state.projects = projects
      state.isAuthenticated = true
      state.sessionExpiry = Date.now() + 24 * 60 * 60 * 1000 // 24小时

      // 持久化存储
      storage.set('token', token)
      storage.set('refreshToken', refreshToken)
      storage.set('user', user)

      // 记录活动时间
      updateActivity()
      
    } catch (error) {
      state.error = error.message
      throw error
    } finally {
      state.isLoading = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.warn('Logout API failed:', error)
    } finally {
      // 清理状态
      state.user = null
      state.token = null
      state.refreshToken = null
      state.permissions = []
      state.projects = []
      state.isAuthenticated = false
      state.sessionExpiry = null

      // 清理存储
      storage.remove('token')
      storage.remove('refreshToken')
      storage.remove('user')
    }
  }

  // 刷新Token
  const refreshAuth = async () => {
    if (!state.refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await authAPI.refresh(state.refreshToken)
      const { token, refreshToken } = response.data

      state.token = token
      state.refreshToken = refreshToken
      state.sessionExpiry = Date.now() + 24 * 60 * 60 * 1000

      storage.set('token', token)
      storage.set('refreshToken', refreshToken)

    } catch (error) {
      // 刷新失败，需要重新登录
      await logout()
      throw error
    }
  }

  // 更新活动时间
  const updateActivity = () => {
    state.lastActivity = Date.now()
  }

  // 检查会话是否过期
  const checkSession = () => {
    if (state.sessionExpiry && Date.now() > state.sessionExpiry) {
      logout()
      return false
    }
    return true
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (state.token) {
      try {
        const response = await authAPI.verify()
        state.user = response.data.user
        state.permissions = response.data.permissions
        state.projects = response.data.projects
        state.isAuthenticated = true
      } catch (error) {
        await logout()
      }
    }
  }

  return {
    // 状态
    ...toRefs(state),
    
    // 计算属性
    isAdmin,
    isDeveloper,
    hasProjectAccess,
    
    // 操作
    login,
    logout,
    refreshAuth,
    updateActivity,
    checkSession,
    initAuth
  }
})
```

## 📊 项目管理状态

### 1. Projects Store 设计

#### 1.1 状态定义
```typescript
interface ProjectsState {
  // 项目数据
  projects: Project[]
  currentProject: Project | null
  
  // 筛选和搜索
  filters: ProjectFilters
  searchQuery: string
  
  // 分页
  pagination: PaginationState
  
  // 缓存
  cache: Map<number, Project>
  cacheExpiry: Map<number, number>
  
  // 状态
  isLoading: boolean
  error: string | null
}

interface ProjectFilters {
  category: 'all' | 'firmware' | 'app'
  status: 'all' | 'active' | 'paused' | 'deprecated'
  deviceType: string
  sortBy: 'name' | 'updateTime' | 'downloadCount'
  sortOrder: 'asc' | 'desc'
}

interface PaginationState {
  current: number
  pageSize: number
  total: number
}
```

#### 1.2 Actions 设计
```typescript
export const useProjectsStore = defineStore('projects', () => {
  const state = reactive<ProjectsState>({
    projects: [],
    currentProject: null,
    filters: {
      category: 'all',
      status: 'all',
      deviceType: '',
      sortBy: 'updateTime',
      sortOrder: 'desc'
    },
    searchQuery: '',
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    },
    cache: new Map(),
    cacheExpiry: new Map(),
    isLoading: false,
    error: null
  })

  // 计算属性
  const filteredProjects = computed(() => {
    let result = state.projects

    // 分类筛选
    if (state.filters.category !== 'all') {
      result = result.filter(p => p.category === state.filters.category)
    }

    // 状态筛选
    if (state.filters.status !== 'all') {
      result = result.filter(p => p.status === state.filters.status)
    }

    // 设备类型筛选
    if (state.filters.deviceType) {
      result = result.filter(p => 
        p.deviceTypes?.includes(state.filters.deviceType)
      )
    }

    // 搜索筛选
    if (state.searchQuery) {
      const query = state.searchQuery.toLowerCase()
      result = result.filter(p => 
        p.name.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query)
      )
    }

    // 排序
    result.sort((a, b) => {
      const { sortBy, sortOrder } = state.filters
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'updateTime':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
          break
        case 'downloadCount':
          comparison = a.totalDownloads - b.totalDownloads
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return result
  })

  // 获取项目列表
  const fetchProjects = async (force = false) => {
    if (state.isLoading) return

    state.isLoading = true
    state.error = null

    try {
      const params = {
        ...state.filters,
        search: state.searchQuery,
        page: state.pagination.current,
        pageSize: state.pagination.pageSize
      }

      const response = await projectsAPI.getList(params)
      
      state.projects = response.data.projects
      state.pagination.total = response.data.total

      // 更新缓存
      response.data.projects.forEach(project => {
        state.cache.set(project.id, project)
        state.cacheExpiry.set(project.id, Date.now() + 5 * 60 * 1000) // 5分钟过期
      })

    } catch (error) {
      state.error = error.message
    } finally {
      state.isLoading = false
    }
  }

  // 获取项目详情
  const fetchProject = async (id: number, force = false) => {
    // 检查缓存
    if (!force && state.cache.has(id)) {
      const expiry = state.cacheExpiry.get(id)
      if (expiry && Date.now() < expiry) {
        state.currentProject = state.cache.get(id)!
        return state.currentProject
      }
    }

    try {
      const response = await projectsAPI.getDetail(id)
      const project = response.data

      state.currentProject = project
      state.cache.set(id, project)
      state.cacheExpiry.set(id, Date.now() + 5 * 60 * 1000)

      return project
    } catch (error) {
      state.error = error.message
      throw error
    }
  }

  // 更新筛选器
  const updateFilters = (newFilters: Partial<ProjectFilters>) => {
    Object.assign(state.filters, newFilters)
    state.pagination.current = 1 // 重置到第一页
  }

  // 更新搜索查询
  const updateSearchQuery = (query: string) => {
    state.searchQuery = query
    state.pagination.current = 1
  }

  // 清理缓存
  const clearCache = () => {
    state.cache.clear()
    state.cacheExpiry.clear()
  }

  return {
    // 状态
    ...toRefs(state),
    
    // 计算属性
    filteredProjects,
    
    // 操作
    fetchProjects,
    fetchProject,
    updateFilters,
    updateSearchQuery,
    clearCache
  }
})
```

## 📦 文件上传状态

### 1. Uploads Store 设计

#### 1.1 状态定义
```typescript
interface UploadTask {
  id: string
  file: File
  projectId: number
  status: 'pending' | 'uploading' | 'success' | 'error' | 'cancelled'
  progress: number
  speed: number
  uploadedSize: number
  error?: string
  createdAt: number
  completedAt?: number
}

interface UploadsState {
  tasks: Map<string, UploadTask>
  activeUploads: number
  maxConcurrent: number
  globalProgress: number
}
```

#### 1.2 Actions 设计
```typescript
export const useUploadsStore = defineStore('uploads', () => {
  const state = reactive<UploadsState>({
    tasks: new Map(),
    activeUploads: 0,
    maxConcurrent: 3,
    globalProgress: 0
  })

  // 计算属性
  const uploadQueue = computed(() => 
    Array.from(state.tasks.values()).filter(task => task.status === 'pending')
  )

  const completedUploads = computed(() =>
    Array.from(state.tasks.values()).filter(task => 
      task.status === 'success' || task.status === 'error'
    )
  )

  // 创建上传任务
  const createUploadTask = (file: File, projectId: number): string => {
    const taskId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const task: UploadTask = {
      id: taskId,
      file,
      projectId,
      status: 'pending',
      progress: 0,
      speed: 0,
      uploadedSize: 0,
      createdAt: Date.now()
    }

    state.tasks.set(taskId, task)
    return taskId
  }

  // 开始上传
  const startUpload = async (taskId: string) => {
    const task = state.tasks.get(taskId)
    if (!task || task.status !== 'pending') return

    // 检查并发限制
    if (state.activeUploads >= state.maxConcurrent) {
      return // 等待其他上传完成
    }

    task.status = 'uploading'
    state.activeUploads++

    try {
      await uploadFile(task)
      task.status = 'success'
      task.completedAt = Date.now()
    } catch (error) {
      task.status = 'error'
      task.error = error.message
    } finally {
      state.activeUploads--
      processQueue() // 处理队列中的下一个任务
    }
  }

  // 上传文件实现
  const uploadFile = (task: UploadTask): Promise<void> => {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('file', task.file)
      formData.append('projectId', task.projectId.toString())

      const xhr = new XMLHttpRequest()

      // 进度监听
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          task.progress = (event.loaded / event.total) * 100
          task.uploadedSize = event.loaded
          
          // 计算上传速度
          const elapsed = Date.now() - task.createdAt
          task.speed = event.loaded / (elapsed / 1000) // bytes per second
          
          updateGlobalProgress()
        }
      }

      xhr.onload = () => {
        if (xhr.status === 200) {
          task.progress = 100
          resolve()
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`))
        }
      }

      xhr.onerror = () => {
        reject(new Error('Upload failed'))
      }

      xhr.open('POST', '/api/developer/upload')
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
      xhr.send(formData)

      // 保存xhr引用用于取消上传
      task.xhr = xhr
    })
  }

  // 取消上传
  const cancelUpload = (taskId: string) => {
    const task = state.tasks.get(taskId)
    if (!task) return

    if (task.status === 'uploading' && task.xhr) {
      task.xhr.abort()
      state.activeUploads--
    }

    task.status = 'cancelled'
    processQueue()
  }

  // 处理上传队列
  const processQueue = () => {
    const pendingTasks = uploadQueue.value
    const availableSlots = state.maxConcurrent - state.activeUploads

    for (let i = 0; i < Math.min(pendingTasks.length, availableSlots); i++) {
      startUpload(pendingTasks[i].id)
    }
  }

  // 更新全局进度
  const updateGlobalProgress = () => {
    const tasks = Array.from(state.tasks.values())
    const totalProgress = tasks.reduce((sum, task) => sum + task.progress, 0)
    state.globalProgress = tasks.length > 0 ? totalProgress / tasks.length : 0
  }

  return {
    // 状态
    ...toRefs(state),
    
    // 计算属性
    uploadQueue,
    completedUploads,
    
    // 操作
    createUploadTask,
    startUpload,
    cancelUpload,
    processQueue
  }
})
```

## 🔄 状态持久化

### 1. 持久化策略

#### 1.1 存储分类
```typescript
// 持久化配置
interface PersistenceConfig {
  // 永久存储（localStorage）
  permanent: {
    userPreferences: UserPreferences
    authTokens: AuthTokens
    appSettings: AppSettings
  }
  
  // 会话存储（sessionStorage）
  session: {
    currentProject: Project
    searchHistory: string[]
    formDrafts: FormData[]
  }
  
  // 内存存储（运行时）
  memory: {
    uploadTasks: UploadTask[]
    notifications: Notification[]
    temporaryData: any
  }
}
```

#### 1.2 自动持久化
```typescript
// 监听状态变化并自动持久化
export function setupPersistence() {
  const authStore = useAuthStore()
  const projectsStore = useProjectsStore()

  // 监听认证状态变化
  watch(
    () => authStore.token,
    (token) => {
      if (token) {
        storage.set('token', token)
      } else {
        storage.remove('token')
      }
    },
    { immediate: true }
  )

  // 监听用户偏好设置变化
  watch(
    () => projectsStore.filters,
    (filters) => {
      storage.set('projectFilters', filters)
    },
    { deep: true }
  )
}
```

### 2. 状态恢复

#### 2.1 应用启动时恢复状态
```typescript
export async function restoreAppState() {
  const authStore = useAuthStore()
  const projectsStore = useProjectsStore()

  // 恢复认证状态
  const savedToken = storage.get('token')
  if (savedToken) {
    authStore.token = savedToken
    await authStore.initAuth()
  }

  // 恢复用户偏好
  const savedFilters = storage.get('projectFilters')
  if (savedFilters) {
    projectsStore.updateFilters(savedFilters)
  }
}
```

## 🚀 性能优化

### 1. 状态懒加载

#### 1.1 按需加载数据
```typescript
// 懒加载项目数据
const loadProjectIfNeeded = async (projectId: number) => {
  const projectsStore = useProjectsStore()
  
  if (!projectsStore.cache.has(projectId)) {
    await projectsStore.fetchProject(projectId)
  }
  
  return projectsStore.cache.get(projectId)
}
```

### 2. 状态缓存策略

#### 2.1 智能缓存更新
```typescript
// 缓存失效策略
export function setupCacheInvalidation() {
  const projectsStore = useProjectsStore()
  
  // 监听项目更新事件
  eventBus.on('project:updated', (projectId: number) => {
    projectsStore.cache.delete(projectId)
    projectsStore.cacheExpiry.delete(projectId)
  })
  
  // 定期清理过期缓存
  setInterval(() => {
    const now = Date.now()
    for (const [id, expiry] of projectsStore.cacheExpiry.entries()) {
      if (now > expiry) {
        projectsStore.cache.delete(id)
        projectsStore.cacheExpiry.delete(id)
      }
    }
  }, 60000) // 每分钟检查一次
}
```

这个状态管理设计为前端应用提供了完整的状态管理方案，确保数据的一致性、可预测性和高性能。

<template>
  <div class="error-page">
    <div class="error-container">
      <!-- 错误图标和代码 -->
      <div class="error-visual">
        <div class="error-icon">🔍</div>
        <div class="error-code">404</div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          很抱歉，您访问的页面不存在或已被移除。
          可能是链接已失效或地址输入错误。
        </p>
        
        <!-- 建议操作 -->
        <div class="error-suggestions">
          <h3 class="suggestions-title">您可以尝试：</h3>
          <ul class="suggestions-list">
            <li>检查URL地址是否正确</li>
            <li>返回上一页或首页</li>
            <li>使用搜索功能查找内容</li>
            <li>联系我们报告此问题</li>
          </ul>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <RouterLink to="/" class="btn btn-primary">
            <BaseIcon name="home" size="16" />
            返回首页
          </RouterLink>
          <button @click="goBack" class="btn btn-secondary">
            <BaseIcon name="arrow-left" size="16" />
            返回上页
          </button>
          <RouterLink to="/search" class="btn btn-outline">
            <BaseIcon name="search" size="16" />
            搜索内容
          </RouterLink>
        </div>
      </div>
      
      <!-- 相关链接 -->
      <div class="error-links">
        <h3 class="links-title">热门页面</h3>
        <div class="links-grid">
          <RouterLink to="/" class="link-item">
            <BaseIcon name="home" size="20" />
            <span>首页</span>
          </RouterLink>
          <RouterLink to="/projects" class="link-item">
            <BaseIcon name="folder" size="20" />
            <span>项目列表</span>
          </RouterLink>
          <RouterLink to="/search" class="link-item">
            <BaseIcon name="search" size="20" />
            <span>搜索</span>
          </RouterLink>
          <RouterLink to="/login" class="link-item">
            <BaseIcon name="user" size="20" />
            <span>登录</span>
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterLink, useRouter } from 'vue-router'
import BaseIcon from '@base/atoms/BaseIcon.vue'

const router = useRouter()

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) var(--spacing-4);
  background: linear-gradient(135deg, var(--bg-1) 0%, var(--bg-2) 100%);
}

.error-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

/* 错误视觉元素 */
.error-visual {
  margin-bottom: var(--spacing-8);
}

.error-icon {
  font-size: 120px;
  margin-bottom: var(--spacing-4);
  opacity: 0.8;
}

.error-code {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 错误内容 */
.error-content {
  margin-bottom: var(--spacing-8);
}

.error-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-4) 0;
}

.error-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-2);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-6) 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 建议操作 */
.error-suggestions {
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.suggestions-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-3) 0;
}

.suggestions-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestions-list li {
  padding: var(--spacing-2) 0;
  color: var(--color-text-2);
  position: relative;
  padding-left: var(--spacing-6);
}

.suggestions-list li::before {
  content: '•';
  color: var(--color-primary);
  font-weight: bold;
  position: absolute;
  left: var(--spacing-2);
}

/* 操作按钮 */
.error-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-10);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-md);
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--bg-2);
  color: var(--color-text-1);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-3);
  border-color: var(--color-primary);
}

.btn-outline {
  background: transparent;
  color: var(--color-text-2);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 相关链接 */
.error-links {
  max-width: 400px;
  margin: 0 auto;
}

.links-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-4) 0;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.link-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--color-text-2);
  transition: all 0.2s ease;
}

.link-item:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-page {
    padding: var(--spacing-6) var(--spacing-3);
  }
  
  .error-icon {
    font-size: 80px;
  }
  
  .error-code {
    font-size: var(--font-size-4xl);
  }
  
  .error-title {
    font-size: var(--font-size-2xl);
  }
  
  .error-description {
    font-size: var(--font-size-md);
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .error-suggestions {
    text-align: center;
  }
  
  .suggestions-list li {
    padding-left: 0;
  }
  
  .suggestions-list li::before {
    display: none;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .error-page {
  background: linear-gradient(135deg, var(--bg-1) 0%, var(--bg-2) 100%);
}

[data-theme="dark"] .error-suggestions {
  background: var(--bg-2);
  border-color: var(--border-color);
}

/* 动画效果 */
.error-container {
  animation: errorFadeIn 0.6s ease-out;
}

@keyframes errorFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-visual {
  animation: errorBounce 2s ease-in-out infinite;
}

@keyframes errorBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>



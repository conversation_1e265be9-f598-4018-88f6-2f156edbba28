const typeOptions = ['stable', 'beta', 'alpha']
const artifactOptions = ['firmware', 'android-demo', 'sdk']
const releases = []
for (let i = 1; i <= 80; i++) {
  const projectId = (i % 20) + 1
  const type = typeOptions[i % typeOptions.length]
  const artifact = artifactOptions[i % artifactOptions.length]
  releases.push({
    id: i,
    projectId,
    projectName: `项目-${projectId}`,
    version: `v1.${Math.floor(i / 10)}.${i % 10}`,
    changelog: `更新内容 ${i}`,
    createdAt: Date.now() - i * 3600_000,
    status: i % 3 === 0 ? 'failed' : i % 2 === 0 ? 'pending' : 'success',
    size: 10_000_000 + i * 12345,
    type,
    artifact,
    downloadUrl: `/download/project-${projectId}/release-${i}.zip`,
  })
}

let nextId = releases.length + 1

export default [
  {
    method: 'GET',
    path: '/releases',
    handler: async ({ query }) => {
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 10)
      const projectId = query.projectId ? Number(query.projectId) : undefined
      const status = query.status
      const artifact = query.artifact
      const sortBy = query.sortBy || 'createdAt'
      const order = (query.order || 'desc').toLowerCase() === 'asc' ? 'asc' : 'desc'

      let data = releases
      if (projectId) data = data.filter((r) => r.projectId === projectId)
      if (status) data = data.filter((r) => r.status === status)
      if (artifact) data = data.filter((r) => r.artifact === artifact)

      if (sortBy) {
        data = data.slice().sort((a, b) => {
          const getValue = (x) => {
            if (sortBy === 'createdAt') return x.createdAt
            if (sortBy === 'version') return x.version
            return x.createdAt
          }
          const va = getValue(a)
          const vb = getValue(b)
          if (va === vb) return 0
          if (order === 'asc') return va > vb ? 1 : -1
          return va < vb ? 1 : -1
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = data.slice(start, end)

      return {
        code: 0,
        message: 'OK',
        data: { list, page, pageSize, total: data.length },
      }
    },
  },
  {
    method: 'GET',
    path: '/releases/:id',
    handler: async ({ params }) => {
      const id = Number(params.id)
      const item = releases.find((r) => r.id === id)
      if (!item) {
        return { code: 404, message: 'Not Found', data: null }
      }
      // mock 详情包含 assets（移除兼容性与统计）
      const detail = {
        ...item,
        assets: [
          { id: `${id}-main`, name: `${item.projectName}-${item.version}.zip`, size: item.size || 12_345_678, url: item.downloadUrl, mime: 'application/zip', hash: { algo: 'sha256', value: 'mockedhash' } },
          { id: `${id}-readme`, name: 'README.md', size: 2048, url: `/preview/release-${id}/README.md`, mime: 'text/markdown' },
        ],
      }
      return { code: 0, message: 'OK', data: detail }
    },
  },
  {
    method: 'GET',
    path: '/projects/:id/releases',
    handler: async ({ params, query }) => {
      const pid = Number(params.id)
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 10)
      const status = query.status
      const artifact = query.artifact
      const sortBy = query.sortBy || 'createdAt'
      const order = (query.order || 'desc').toLowerCase() === 'asc' ? 'asc' : 'desc'

      let data = releases.filter((r) => r.projectId === pid)
      if (status) data = data.filter((r) => r.status === status)
      if (artifact) data = data.filter((r) => r.artifact === artifact)
      if (sortBy) {
        data = data.slice().sort((a, b) => {
          const getValue = (x) => (sortBy === 'version' ? x.version : x.createdAt)
          const va = getValue(a)
          const vb = getValue(b)
          if (va === vb) return 0
          if (order === 'asc') return va > vb ? 1 : -1
          return va < vb ? 1 : -1
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = data.slice(start, end)

      return { code: 0, message: 'OK', data: { list, page, pageSize, total: data.length } }
    },
  },
  {
    method: 'POST',
    path: '/releases',
    handler: async ({ body }) => {
      const record = {
        id: nextId++,
        projectId: Number(body.projectId || 1),
        version: body.version || `v1.0.${nextId}`,
        changelog: body.changelog || '',
        createdAt: Date.now(),
        status: 'pending',
        size: 12_345_678,
        type: 'stable',
        artifact: 'firmware',
        projectName: `项目-${Number(body.projectId || 1)}`,
        downloadUrl: `/download/project-${Number(body.projectId || 1)}/release-${nextId}.zip`,
      }
      releases.unshift(record)
      return { code: 0, message: 'OK', data: record }
    },
  },
  
  // 删除版本（开发者专用）
  {
    method: 'DELETE',
    path: '/releases/:id',
    handler: async ({ params, req }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const id = Number(params.id)
      const index = releases.findIndex(r => r.id === id)
      
      if (index === -1) {
        return { code: 404, message: '版本不存在', data: null }
      }
      
      const removed = releases.splice(index, 1)[0]
      
      return { 
        code: 0, 
        message: '删除成功', 
        data: { 
          id: removed.id,
          version: removed.version 
        } 
      }
    },
  },
]



import { request } from '@/api/http'
import type { PageResult, ProjectItem, SoftwareType, Version } from '@/types/domain'

export interface FetchDeveloperProjectsParams {
  page?: number
  pageSize?: number
}

export interface CreateSoftwareTypeParams {
  name: string
}

export interface UpdateSoftwareTypeParams {
  name: string
}

export interface CreateVersionParams {
  versionName: string
  fileUrl?: string
  fileSize?: number
}

export interface UpdateVersionParams {
  versionName?: string
  fileUrl?: string
  fileSize?: number
}

export interface FetchVersionsParams {
  page?: number
  pageSize?: number
}

/**
 * 获取开发者管理的项目列表
 */
export function fetchDeveloperProjects(params: FetchDeveloperProjectsParams = {}) {
  return request<PageResult<ProjectItem>>({
    url: '/developer/projects',
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['developer:projects'] },
  })
}

/**
 * 获取项目详情
 */
export function fetchProjectDetail(projectId: number) {
  return request<ProjectItem>({
    url: `/projects/${projectId}`,
    method: 'GET',
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['developer:project-detail'] },
  })
}

// ========== 软件类型管理 ==========

/**
 * 获取项目的软件类型列表
 */
export function fetchProjectSoftwareTypes(projectId: number) {
  return request<SoftwareType[]>({
    url: `/developer/projects/${projectId}/software-types`,
    method: 'GET',
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['developer:software-types'] },
  })
}

/**
 * 创建软件类型
 */
export function createSoftwareType(projectId: number, params: CreateSoftwareTypeParams) {
  return request<SoftwareType>({
    url: `/developer/projects/${projectId}/software-types`,
    method: 'POST',
    data: params,
    retry: { times: 1 },
  })
}

/**
 * 更新软件类型
 */
export function updateSoftwareType(softwareTypeId: number, params: UpdateSoftwareTypeParams) {
  return request<SoftwareType>({
    url: `/developer/software-types/${softwareTypeId}`,
    method: 'PUT',
    data: params,
    retry: { times: 1 },
  })
}

/**
 * 删除软件类型
 */
export function deleteSoftwareType(softwareTypeId: number) {
  return request<{ softwareType: SoftwareType; removedVersions: number }>({
    url: `/developer/software-types/${softwareTypeId}`,
    method: 'DELETE',
    retry: { times: 1 },
  })
}

// ========== 版本管理 ==========

/**
 * 获取软件类型的版本列表
 */
export function fetchSoftwareTypeVersions(softwareTypeId: number, params: FetchVersionsParams = {}) {
  return request<PageResult<Version>>({
    url: `/developer/software-types/${softwareTypeId}/versions`,
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['developer:versions'] },
  })
}

/**
 * 创建版本
 */
export function createVersion(softwareTypeId: number, params: CreateVersionParams) {
  return request<Version>({
    url: `/developer/software-types/${softwareTypeId}/versions`,
    method: 'POST',
    data: params,
    retry: { times: 1 },
  })
}

/**
 * 更新版本
 */
export function updateVersion(versionId: number, params: UpdateVersionParams) {
  return request<Version>({
    url: `/developer/versions/${versionId}`,
    method: 'PUT',
    data: params,
    retry: { times: 1 },
  })
}

/**
 * 删除版本
 */
export function deleteVersion(versionId: number) {
  return request<{ id: number; versionName: string }>({
    url: `/developer/versions/${versionId}`,
    method: 'DELETE',
    retry: { times: 1 },
  })
}

import { request } from '@/api/http'
import type { PageResult, ProjectItem } from '@/types/domain'

export interface FetchProjectsParams {
  page?: number
  pageSize?: number
  search?: string
  type?: string
  device?: string
  tags?: string
  sortBy?: 'name' | 'updatedAt'
  order?: 'asc' | 'desc'
}

export function fetchProjects(params: FetchProjectsParams) {
  return request<PageResult<ProjectItem>>({
    url: '/projects',
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['projects:list'] },
  })
}

export function fetchProjectDetail(id: number) {
  return request<ProjectItem>({
    url: `/projects/${id}`,
    method: 'GET',
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 5 * 60_000, tags: [`project:${id}`] },
  })
}



/**
 * 统一数据管理系统
 * 
 * 提供整个应用的统一数据源，支持：
 * - 示例数据管理
 * - 真实API数据切换
 * - 数据一致性保证
 * - 缓存管理
 */

import { mockDataService } from './mockDataService'
import { liveDataService } from './liveDataService'
import type { UnifiedDataService } from './types'

// 数据源配置
export interface DataConfig {
  useMockData: boolean
  mockApiDelay: number
  cacheEnabled: boolean
  cacheTTL: number
}

// 默认配置
const defaultConfig: DataConfig = {
  useMockData: true, // 当前使用示例数据
  mockApiDelay: 800, // 模拟API延迟（毫秒）
  cacheEnabled: true,
  cacheTTL: 5 * 60 * 1000 // 5分钟缓存
}

let currentConfig = { ...defaultConfig }

// 获取当前数据服务
export function getDataService(): UnifiedDataService {
  if (currentConfig.useMockData) {
    return mockDataService
  } else {
    return liveDataService
  }
}

// 更新配置
export function updateDataConfig(config: Partial<DataConfig>) {
  currentConfig = { ...currentConfig, ...config }
}

// 获取当前配置
export function getDataConfig(): DataConfig {
  return { ...currentConfig }
}

// 切换到真实API
export function switchToLiveAPI() {
  updateDataConfig({ useMockData: false })
}

// 切换到示例数据
export function switchToMockData() {
  updateDataConfig({ useMockData: true })
}

// 导出数据服务
export { mockDataService, liveDataService }
export * from './types'
export * from './mockData'

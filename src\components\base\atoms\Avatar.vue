<script setup lang="ts">
type AvatarSize = 'sm' | 'md' | 'lg'

const props = withDefaults(defineProps<{
  src?: string
  alt?: string
  text?: string
  size?: AvatarSize
  shape?: 'circle' | 'square'
  bgColor?: string
  textColor?: string
}>(), {
  alt: '',
  text: '',
  size: 'md',
  shape: 'circle',
  bgColor: '#e5e7eb',
  textColor: '#111827',
})

const initials = computed(() => {
  if (props.text) return props.text
  if (!props.alt) return ''
  const parts = props.alt.trim().split(/\s+/)
  return parts.slice(0, 2).map((s) => s[0]?.toUpperCase() || '').join('')
})
</script>

<template>
  <span class="avatar" :class="[`size-${size}`, `shape-${shape}`]">
    <slot />
  </span>
</template>

<style scoped>
.avatar { display: inline-flex; align-items: center; justify-content: center; background: var(--bg-3); color: var(--color-text-2); }
.size-sm { width: 24px; height: 24px; font-size: 12px; }
.size-md { width: 32px; height: 32px; font-size: 14px; }
.size-lg { width: 40px; height: 40px; font-size: 16px; }
.shape-circle { border-radius: var(--radius-full); }
.shape-square { border-radius: var(--radius-sm); }
</style>





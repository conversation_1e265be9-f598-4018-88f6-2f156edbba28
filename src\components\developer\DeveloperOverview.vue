<template>
  <div class="developer-overview">
    <h2 class="section-title">我的工作台</h2>
    
    <!-- 工作统计 -->
    <div class="stats-grid">
      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-projects"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats?.myProjectsCount || 0 }}</div>
          <div class="stat-label">管理的项目</div>
          <div class="stat-trend" v-if="stats?.newProjectsThisMonth">
            <span class="trend-up">+{{ stats.newProjectsThisMonth }} 本月新增</span>
          </div>
        </div>
      </div>

      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-releases"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats?.totalReleases || 0 }}</div>
          <div class="stat-label">发布版本数</div>
          <div class="stat-trend" v-if="stats?.releasesThisMonth">
            <span class="trend-up">+{{ stats.releasesThisMonth }} 本月发布</span>
          </div>
        </div>
      </div>

      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ recentActivities.length }}</div>
          <div class="stat-label">最近活动</div>
          <div class="stat-trend" v-if="stats?.activitiesThisMonth">
            <span class="trend-up">+{{ stats.activitiesThisMonth }} 本月</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动和快速操作 -->
    <div class="dashboard-sections">
      <!-- 最近活动 -->
      <div class="recent-activities-section">
        <div class="section-header">
          <h3 class="subsection-title">最近活动</h3>
        </div>
        
        <div class="activities-list">
          <div v-if="loadingActivities" class="loading-state">
            <p>正在加载...</p>
          </div>
          <div v-else-if="recentActivities.length === 0" class="empty-activities">
            <p>暂无最近活动</p>
          </div>
          <div v-else>
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id" 
              class="activity-item"
            >
              <div class="activity-icon">
                <i :class="getActivityIcon(activity.type)"></i>
              </div>
              <div class="activity-content">
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-meta">
                  <span class="activity-project">{{ activity.projectName }}</span>
                  <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
                </div>
              </div>
              <div class="activity-action" v-if="activity.actionable">
                <button 
                  class="btn btn-xs btn-outline"
                  @click="handleActivityAction(activity)"
                >
                  查看
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions-section">
        <div class="section-header">
          <h3 class="subsection-title">快速操作</h3>
        </div>
        
        <div class="quick-actions-grid">
          <div class="quick-action-item" @click="$emit('upload-release')">
            <div class="action-icon upload">
              <i class="icon-upload"></i>
            </div>
            <div class="action-content">
              <div class="action-title">发布新版本</div>
              <div class="action-description">上传文件并发布新版本</div>
            </div>
          </div>

          <div class="quick-action-item" @click="$emit('manage-projects')">
            <div class="action-icon projects">
              <i class="icon-settings"></i>
            </div>
            <div class="action-content">
              <div class="action-title">项目管理</div>
              <div class="action-description">编辑项目信息和设置</div>
            </div>
          </div>


          <div class="quick-action-item" @click="$emit('help-docs')">
            <div class="action-icon help">
              <i class="icon-help"></i>
            </div>
            <div class="action-content">
              <div class="action-title">帮助文档</div>
              <div class="action-description">查看使用指南和FAQ</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目概览 -->
    <div class="projects-overview-section">
      <div class="section-header">
        <h3 class="subsection-title">项目概览</h3>
        <button class="btn btn-sm btn-outline" @click="$emit('view-all-projects')">
          查看全部
        </button>
      </div>
      
      <div class="projects-quick-list">
        <div v-if="loadingProjects" class="loading-state">
          <p>正在加载...</p>
        </div>
        <div v-else-if="recentProjects.length === 0" class="empty-projects">
          <p>暂无项目数据</p>
        </div>
        <div v-else class="projects-grid">
          <div 
            v-for="project in recentProjects.slice(0, 4)" 
            :key="project.id"
            class="project-summary-card"
            @click="$emit('view-project', project.id)"
          >
            <div class="project-header">
              <h4 class="project-name">{{ project.name }}</h4>
              <span :class="['project-type', project.type]">
                {{ getProjectTypeLabel(project.type) }}
              </span>
            </div>
            
            <div class="project-stats">
              <div class="stat-row">
                <span class="stat-label">版本数：</span>
                <span class="stat-value">{{ project.releaseCount }}</span>
              </div>
            </div>
            
            <div class="project-footer">
              <div class="latest-release" v-if="project.latestRelease">
                <span class="release-version">{{ project.latestRelease.version }}</span>
                <span class="release-time">{{ formatTime(project.latestRelease.releaseTime) }}</span>
              </div>
              <div v-else class="no-release">
                <span class="no-release-text">暂无版本</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import { getDataService } from '@/data'
import type { DeveloperStats, Activity, ProjectSummary } from '@/data'

const router = useRouter()
const developerAuth = useDeveloperAuthStore()
const dataService = getDataService()

// Activity 接口已经从 @/data 导入
// ProjectSummary 接口已经从 @/data 导入

// 定义事件
defineEmits<{
  'upload-release': []
  'manage-projects': []
  'help-docs': []
  'view-all-projects': []
  'view-project': [projectId: number]
}>()

// 响应式数据
const stats = ref<DeveloperStats | null>(null)
const recentActivities = ref<Activity[]>([])
const recentProjects = ref<ProjectSummary[]>([])
const loadingActivities = ref(false)
const loadingProjects = ref(false)




// 格式化时间
function formatTime(timestamp: number) {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return new Date(timestamp).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
}

// 获取活动图标
function getActivityIcon(type: Activity['type']) {
  const iconMap = {
    'release_published': 'icon-upload',
    'release_updated': 'icon-edit',
    'project_updated': 'icon-settings',
    'download_milestone': 'icon-trending-up'
  }
  return iconMap[type] || 'icon-activity'
}

// 项目类型标签
function getProjectTypeLabel(type: 'firmware' | 'app' | 'library') {
  const typeMap = {
    'firmware': '固件',
    'app': '应用程序',
    'library': '库文件'
  } as const
  return typeMap[type] || type
}

// 处理活动操作
function handleActivityAction(activity: Activity) {
  if (activity.projectId) {
    router.push(`/dev/project/${activity.projectId}`)
  }
}

// 加载统计数据
async function loadStats() {
  try {
    const userId = developerAuth.user?.id as number
    if (userId) {
      stats.value = await dataService.getDeveloperStats(userId)
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载最近活动
async function loadActivities() {
  loadingActivities.value = true
  try {
    const userId = developerAuth.user?.id as number
    if (userId) {
      recentActivities.value = await dataService.getDeveloperActivities(userId, { pageSize: 10 })
    }
  } catch (error) {
    console.error('加载最近活动失败:', error)
  } finally {
    loadingActivities.value = false
  }
}

// 加载最近项目
async function loadRecentProjects() {
  loadingProjects.value = true
  try {
    recentProjects.value = await dataService.getProjectSummaries({ pageSize: 6, sortBy: 'updatedAt', order: 'desc' })
  } catch (error) {
    console.error('加载项目数据失败:', error)
  } finally {
    loadingProjects.value = false
  }
}


// 初始化数据
onMounted(() => {
  Promise.all([
    loadStats(),
    loadActivities(),
    loadRecentProjects()
  ])
})

</script>

<style scoped>
.developer-overview {
  margin-bottom: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-6) 0;
}

.subsection-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.stat-card {
  padding: var(--spacing-5);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  transition: all var(--transition-base);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  line-height: 1;
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  margin-bottom: var(--spacing-2);
}

.stat-trend {
  font-size: var(--font-size-xs);
}

.trend-up {
  color: var(--color-success);
}

.stat-progress {
  margin-top: var(--spacing-2);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--bg-3);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: var(--spacing-1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
  transition: width var(--transition-base);
}

.progress-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
}

/* 仪表板部分 */
.dashboard-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.recent-activities-section,
.quick-actions-section {
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

/* 最近活动 */
.activities-list {
  max-height: 300px;
  overflow-y: auto;
}

.loading-state,
.empty-activities {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-4);
  font-size: var(--font-size-sm);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  transition: background var(--transition-base);
  margin-bottom: var(--spacing-2);
}

.activity-item:hover {
  background: var(--bg-2);
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  background: var(--color-info-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-info);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-1);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-1);
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.activity-project {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
}

.activity-action {
  flex-shrink: 0;
}

/* 快速操作 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3);
}

.quick-action-item {
  padding: var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.quick-action-item:hover {
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-sm);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.action-icon.upload {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
}

.action-icon.projects {
  background: linear-gradient(135deg, var(--color-success), var(--color-success-light));
}

.action-icon.analytics {
  background: linear-gradient(135deg, var(--color-info), var(--color-info-light));
}

.action-icon.help {
  background: linear-gradient(135deg, var(--color-warning), var(--color-warning-light));
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin-bottom: var(--spacing-1);
}

.action-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  line-height: var(--line-height-relaxed);
}

/* 项目概览 */
.projects-overview-section {
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
}

.projects-quick-list {
  max-height: 400px;
  overflow-y: auto;
}

.empty-projects {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-6);
  font-size: var(--font-size-sm);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

.project-summary-card {
  padding: var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-base);
}

.project-summary-card:hover {
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-sm);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
}

.project-name {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
  flex: 1;
}

.project-type {
  display: inline-block;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
  margin-left: var(--spacing-2);
}

.project-type.firmware {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.project-type.app {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.project-type.library {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.project-stats {
  margin-bottom: var(--spacing-3);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-xs);
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: var(--color-text-3);
}

.stat-value {
  color: var(--color-text-1);
  font-weight: var(--font-weight-medium);
}

.project-footer {
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

.latest-release {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.release-version {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.release-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
}

.no-release {
  text-align: center;
}

.no-release-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  font-style: italic;
}

@media (max-width: 1024px) {
  .dashboard-sections {
    grid-template-columns: 1fr;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .project-header {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: var(--spacing-4);
    gap: var(--spacing-3);
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: var(--font-size-lg);
  }
}
</style>

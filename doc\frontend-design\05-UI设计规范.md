# UI设计规范

## 🎨 设计系统概述

### 1. 设计理念

#### 1.1 设计价值观
- **专业可信**: 体现技术系统的专业性和可靠性
- **简洁高效**: 去除冗余元素，突出核心功能
- **用户友好**: 降低学习成本，提升使用效率
- **一致性**: 统一的视觉语言和交互规范

#### 1.2 设计目标
- **功能性**: 设计服务于功能，不为了美观而影响可用性
- **可访问性**: 支持不同能力的用户群体
- **扩展性**: 设计系统能够支持功能扩展
- **维护性**: 设计规范便于团队协作和维护

### 2. 品牌识别

#### 2.1 产品定位
- **目标用户**: 技术开发人员和企业内部员工
- **使用场景**: 内网办公环境，专业工具软件
- **品牌调性**: 专业、可靠、高效、现代

#### 2.2 视觉风格
- **整体风格**: 现代扁平化设计，简洁专业
- **视觉层次**: 清晰的信息层级，突出重点内容
- **情感表达**: 稳重可靠，给用户信任感

## 🌈 色彩系统

### 1. 主色彩定义

#### 1.1 主色调 (Primary Colors)
```scss
// 主蓝色 - 系统主色调
$primary-color: #1890ff;        // 主要操作按钮、链接
$primary-hover: #40a9ff;        // 悬停状态
$primary-active: #096dd9;       // 激活状态
$primary-disabled: #91d5ff;     // 禁用状态

// 主色渐变
$primary-gradient: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
```

#### 1.2 辅助色调 (Secondary Colors)
```scss
// 成功色 - 成功状态、确认操作
$success-color: #52c41a;
$success-hover: #73d13d;
$success-active: #389e0d;

// 警告色 - 警告信息、需要注意的内容
$warning-color: #faad14;
$warning-hover: #ffc53d;
$warning-active: #d48806;

// 错误色 - 错误状态、危险操作
$error-color: #ff4d4f;
$error-hover: #ff7875;
$error-active: #d9363e;

// 信息色 - 一般信息提示
$info-color: #1890ff;
$info-hover: #40a9ff;
$info-active: #096dd9;
```

### 2. 中性色系统

#### 2.1 文字色彩
```scss
// 文字颜色层级
$text-color-primary: #262626;      // 主要文字 - 标题、重要内容
$text-color-secondary: #595959;    // 次要文字 - 描述、说明
$text-color-tertiary: #8c8c8c;     // 辅助文字 - 提示、标签
$text-color-disabled: #bfbfbf;     // 禁用文字 - 不可操作内容

// 反白文字（深色背景使用）
$text-color-inverse: #ffffff;
$text-color-inverse-secondary: #d9d9d9;
```

#### 2.2 背景色彩
```scss
// 背景颜色层级
$bg-color-white: #ffffff;          // 主要背景 - 卡片、弹窗
$bg-color-light: #fafafa;          // 页面背景
$bg-color-gray: #f5f5f5;           // 区域背景 - 侧边栏、分割区域
$bg-color-dark: #001529;           // 深色背景 - 导航栏
$bg-color-mask: rgba(0, 0, 0, 0.6); // 遮罩背景
```

#### 2.3 边框色彩
```scss
// 边框颜色
$border-color-base: #d9d9d9;       // 基础边框
$border-color-light: #e8e8e8;      // 浅色边框 - 分割线
$border-color-dark: #434343;       // 深色边框
$border-color-focus: #1890ff;      // 焦点边框
```

### 3. 色彩使用规则

#### 3.1 色彩层级原则
- **主色调**: 主要操作按钮、重要链接、选中状态
- **成功色**: 成功提示、确认按钮、正向反馈
- **警告色**: 警告信息、需要谨慎的操作
- **错误色**: 错误提示、删除按钮、危险操作
- **中性色**: 文本内容、背景、边框

#### 3.2 对比度要求
- **AAA级别**: 正文文字与背景对比度 ≥ 7:1
- **AA级别**: 标题文字与背景对比度 ≥ 4.5:1
- **交互元素**: 按钮、链接对比度 ≥ 4.5:1

## 📝 字体系统

### 1. 字体族定义

#### 1.1 字体栈配置
```scss
// 系统字体栈
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                   'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
                   'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
                   'Noto Color Emoji';

// 等宽字体 - 代码、版本号
$font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, 
                   Courier, monospace;

// 中文字体优化
$font-family-chinese: -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                      'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
                      'Helvetica Neue', Helvetica, Arial, sans-serif;
```

### 2. 字体大小体系

#### 2.1 字体大小规范
```scss
// 字体大小层级（基准14px）
$font-size-xs: 10px;      // 极小文字 - 版权信息、次要标签
$font-size-sm: 12px;      // 小号文字 - 辅助信息、标签
$font-size-base: 14px;    // 基础文字 - 正文内容
$font-size-lg: 16px;      // 大号文字 - 重要信息
$font-size-xl: 18px;      // 特大文字 - 小标题
$font-size-xxl: 20px;     // 超大文字 - 页面标题
$font-size-xxxl: 24px;    // 巨大文字 - 主标题

// 标题字体大小
$h1-font-size: 24px;      // 页面主标题
$h2-font-size: 20px;      // 区域标题
$h3-font-size: 18px;      // 小节标题
$h4-font-size: 16px;      // 卡片标题
$h5-font-size: 14px;      // 组件标题
$h6-font-size: 12px;      // 最小标题
```

#### 2.2 行高规范
```scss
// 行高设置
$line-height-xs: 1.2;     // 紧密行距 - 标题
$line-height-sm: 1.4;     // 正常行距 - 标签、按钮
$line-height-base: 1.5;   // 基础行距 - 正文
$line-height-lg: 1.6;     // 宽松行距 - 长文本
$line-height-xl: 2.0;     // 超宽行距 - 特殊场景
```

### 3. 字体权重和样式

#### 3.1 字体权重
```scss
// 字体粗细
$font-weight-light: 300;    // 细体 - 大标题、装饰文字
$font-weight-normal: 400;   // 正常 - 正文内容
$font-weight-medium: 500;   // 中等 - 小标题、重要信息
$font-weight-semibold: 600; // 半粗 - 按钮文字、强调
$font-weight-bold: 700;     // 粗体 - 页面标题、重要标题
```

#### 3.2 字体样式应用场景
- **标题文字**: medium/semibold权重，适当增大字号
- **正文内容**: normal权重，基础字号和行高
- **辅助信息**: normal权重，较小字号，浅色
- **强调文字**: medium/semibold权重，主色调
- **代码文字**: 等宽字体，背景色区分

## 📏 间距系统

### 1. 间距基准

#### 1.1 基础间距单位
```scss
// 间距基准单位（4px基准）
$spacing-unit: 4px;

// 间距规范
$spacing-xs: $spacing-unit;       // 4px  - 最小间距
$spacing-sm: $spacing-unit * 2;   // 8px  - 小间距
$spacing-md: $spacing-unit * 3;   // 12px - 中间距
$spacing-base: $spacing-unit * 4; // 16px - 基础间距
$spacing-lg: $spacing-unit * 6;   // 24px - 大间距
$spacing-xl: $spacing-unit * 8;   // 32px - 超大间距
$spacing-xxl: $spacing-unit * 12; // 48px - 巨大间距
```

### 2. 布局间距应用

#### 2.1 组件内部间距
- **按钮内边距**: 8px 16px（小按钮）、12px 24px（标准按钮）
- **输入框内边距**: 8px 12px
- **卡片内边距**: 16px 24px
- **弹窗内边距**: 24px 32px

#### 2.2 组件间距规则
- **相关元素间距**: 8px-12px
- **组件间距**: 16px-24px
- **区域间距**: 32px-48px
- **页面边距**: 24px（移动端）、48px（桌面端）

### 3. 响应式间距

#### 3.1 移动端间距调整
```scss
// 移动端间距缩放
@media (max-width: 768px) {
  $spacing-scale: 0.75; // 移动端间距缩放75%
  
  // 重新定义移动端间距
  $spacing-mobile-xs: 3px;
  $spacing-mobile-sm: 6px;
  $spacing-mobile-md: 9px;
  $spacing-mobile-base: 12px;
  $spacing-mobile-lg: 18px;
  $spacing-mobile-xl: 24px;
}
```

## 🔲 组件规范

### 1. 按钮组件规范

#### 1.1 按钮尺寸规范
```scss
// 按钮高度规范
$btn-height-sm: 24px;      // 小按钮
$btn-height-base: 32px;    // 标准按钮
$btn-height-lg: 40px;      // 大按钮

// 按钮最小宽度
$btn-min-width: 64px;      // 最小可点击区域

// 按钮圆角
$btn-border-radius: 4px;   // 标准圆角
$btn-border-radius-sm: 2px; // 小圆角
$btn-border-radius-lg: 6px; // 大圆角
```

#### 1.2 按钮状态样式
- **默认状态**: 基础样式
- **悬停状态**: 背景色加深，适当阴影
- **激活状态**: 背景色进一步加深
- **禁用状态**: 降低透明度，禁用交互
- **加载状态**: 显示加载图标，禁用点击

### 2. 表单组件规范

#### 2.1 输入框规范
```scss
// 输入框尺寸
$input-height-sm: 24px;
$input-height-base: 32px;
$input-height-lg: 40px;

// 输入框样式
$input-border-radius: 4px;
$input-border-width: 1px;
$input-padding: 8px 12px;
```

#### 2.2 表单验证样式
- **正常状态**: 默认边框色
- **焦点状态**: 主色调边框，外发光效果
- **错误状态**: 错误色边框，错误提示
- **成功状态**: 成功色边框，成功标识
- **禁用状态**: 灰色背景，禁用交互

### 3. 导航组件规范

#### 3.1 导航栏规范
```scss
// 导航栏高度
$navbar-height: 64px;      // 主导航栏高度
$navbar-height-sm: 48px;   // 次导航栏高度

// 导航项间距
$navbar-item-padding: 16px 24px;
$navbar-item-margin: 0 8px;
```

#### 3.2 面包屑导航
- **分隔符**: ">" 或 "/"
- **当前页**: 不可点击，主文字色
- **链接页**: 可点击，次文字色，悬停变主色调

## 🎭 图标系统

### 1. 图标规范

#### 1.1 图标尺寸
```scss
// 图标尺寸规范
$icon-size-xs: 12px;       // 极小图标 - 内联文字图标
$icon-size-sm: 14px;       // 小图标 - 按钮图标
$icon-size-base: 16px;     // 基础图标 - 菜单图标
$icon-size-lg: 20px;       // 大图标 - 标题图标
$icon-size-xl: 24px;       // 超大图标 - 重要功能图标
$icon-size-xxl: 32px;      // 巨大图标 - 空状态图标
```

#### 1.2 图标风格
- **线性图标**: 1.5px线宽，简洁现代
- **填充图标**: 状态图标，强调重要性
- **双色图标**: 层次丰富，适合品牌展示

### 2. 图标使用规则

#### 2.1 功能图标规范
- **导航图标**: 16px，一致的视觉重量
- **操作图标**: 14-16px，清晰易识别
- **状态图标**: 使用对应的状态色彩
- **装饰图标**: 适当透明度，不干扰内容

#### 2.2 图标与文字组合
- **图标+文字**: 图标在左，间距8px
- **纯图标按钮**: 最小44px点击区域
- **图标对齐**: 与文字基线对齐

## 📐 阴影与边框

### 1. 阴影系统

#### 1.1 阴影层级
```scss
// 阴影层级定义
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);          // 轻微阴影 - 按钮悬停
$shadow-base: 0 4px 8px rgba(0, 0, 0, 0.12);       // 基础阴影 - 卡片
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);        // 大阴影 - 弹窗
$shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.2);        // 超大阴影 - 模态框
```

#### 1.2 阴影使用场景
- **按钮阴影**: 悬停时显示，增强交互感
- **卡片阴影**: 区分层次，突出内容
- **弹窗阴影**: 强调层级关系
- **菜单阴影**: 增强浮动感

### 2. 边框系统

#### 2.1 边框样式
```scss
// 边框宽度
$border-width-sm: 1px;     // 细边框 - 分割线
$border-width-base: 1px;   // 标准边框 - 输入框
$border-width-lg: 2px;     // 粗边框 - 强调边框

// 边框圆角
$border-radius-sm: 2px;    // 小圆角
$border-radius-base: 4px;  // 标准圆角
$border-radius-lg: 8px;    // 大圆角
$border-radius-xl: 12px;   // 超大圆角
```

这个UI设计规范为前端开发提供了完整的视觉设计指导，确保整个系统的视觉一致性和用户体验质量。

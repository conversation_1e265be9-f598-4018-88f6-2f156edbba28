<template>
  <div class="release-manager">
    <div class="section-header">
      <h2 class="section-title">版本发布管理</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="showUploadModal = true">
          发布新版本
        </button>
      </div>
    </div>

    <!-- 项目选择 -->
    <div class="project-selector">
      <select v-model="selectedProjectId" class="project-select" @change="loadReleases">
        <option value="">选择项目</option>
        <option 
          v-for="project in myProjects" 
          :key="project.id"
          :value="project.id"
        >
          {{ project.name }} ({{ getProjectTypeLabel(project.type) }})
        </option>
      </select>
    </div>

    <!-- 版本列表 -->
    <div v-if="selectedProjectId" class="releases-section">
      <div class="releases-header">
        <h3>版本列表</h3>
        <div class="releases-stats">
          <span class="stat-item">总计 {{ releases.length }} 个版本</span>
        </div>
      </div>

      <div v-if="loading" class="loading-state">
        <p>正在加载版本...</p>
      </div>
      <div v-else-if="releases.length === 0" class="empty-state">
        <p>该项目暂无版本</p>
        <button class="btn btn-primary" @click="showUploadModal = true">
          发布首个版本
        </button>
      </div>
      <div v-else class="releases-list">
        <div 
          v-for="release in releases" 
          :key="release.id"
          class="release-item card"
        >
          <div class="release-header">
            <div class="release-info">
              <h4 class="release-version">{{ release.version }}</h4>
              <span :class="['release-type', release.type]">
                {{ getReleaseTypeLabel(release.type) }}
              </span>
              <span :class="['release-status', release.status]">
                {{ getReleaseStatusLabel(release.status) }}
              </span>
            </div>
            <div class="release-meta">
              <span class="release-size">{{ formatFileSize(release.fileSize) }}</span>
              <span class="release-date">{{ formatTime(release.createdAt) }}</span>
            </div>
          </div>

          <div class="release-description" v-if="release.description">
            <p>{{ release.description }}</p>
          </div>

          <div class="release-details">
            <div class="details-grid">
              <div class="detail-item">
                <span class="detail-label">文件名</span>
                <span class="detail-value">{{ release.fileName }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">适用设备</span>
                <span class="detail-value">{{ release.devices.join(', ') }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">下载次数</span>
                <span class="detail-value">{{ release.downloadCount }} 次</span>
              </div>
            </div>
          </div>

          <div class="release-actions">
            <button 
              class="btn btn-sm btn-outline" 
              @click="editRelease(release)"
            >
              编辑
            </button>
            <button 
              class="btn btn-sm btn-outline" 
              @click="downloadRelease(release)"
            >
              下载
            </button>
            <button 
              class="btn btn-sm btn-outline btn-danger" 
              @click="deleteRelease(release)"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传新版本弹窗 -->
    <div v-if="showUploadModal" class="modal-overlay" @click="closeUploadModal">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ editingRelease ? '编辑版本' : '发布新版本' }}</h3>
          <button class="modal-close" @click="closeUploadModal">×</button>
        </div>
        
        <form class="release-form" @submit.prevent="submitRelease">
          <!-- 项目选择 -->
          <div class="form-section" v-if="!editingRelease">
            <h4 class="form-section-title">选择项目</h4>
            <div class="form-group">
              <select v-model="releaseForm.projectId" class="form-select" required>
                <option value="">请选择项目</option>
                <option 
                  v-for="project in myProjects" 
                  :key="project.id"
                  :value="project.id"
                >
                  {{ project.name }} ({{ getProjectTypeLabel(project.type) }})
                </option>
              </select>
            </div>
          </div>

          <!-- 文件上传 -->
          <div class="form-section" v-if="!editingRelease">
            <h4 class="form-section-title">上传文件</h4>
            <div class="form-group">
              <div class="file-upload-area" 
                   :class="{ 'drag-over': dragOver }"
                   @drop="handleDrop"
                   @dragover.prevent="dragOver = true"
                   @dragleave="dragOver = false"
                   @click="triggerFileInput">
                <input ref="fileInput" type="file" hidden @change="handleFileSelect" accept=".bin,.hex,.zip,.tar.gz,.apk,.exe,.msi">
                <div class="upload-content">
                  <i class="icon-upload large-icon"></i>
                  <div class="upload-text">
                    <p class="upload-main">点击上传或拖拽文件到这里</p>
                    <p class="upload-hint">支持 .bin, .hex, .zip, .tar.gz, .apk, .exe, .msi 格式，最大 200MB</p>
                  </div>
                </div>
                <div v-if="selectedFile" class="selected-file">
                  <i class="icon-file"></i>
                  <span class="file-name">{{ selectedFile.name }}</span>
                  <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
                  <button type="button" class="remove-file" @click.stop="removeFile">×</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 版本信息 -->
          <div class="form-section">
            <h4 class="form-section-title">版本信息</h4>
            
            <div class="form-group">
              <label class="form-label">版本号 *</label>
              <input
                v-model="releaseForm.version"
                type="text"
                class="form-input"
                placeholder="例如: v1.0.0 或 *******"
                required
              />
              <div class="form-hint">建议使用语义化版本号，如 v1.0.0</div>
            </div>

            <div class="form-group">
              <label class="form-label">版本类型 *</label>
              <select v-model="releaseForm.type" class="form-select" required>
                <option value="">请选择版本类型</option>
                <option value="development">开发版</option>
                <option value="beta">测试版本</option>
                <option value="stable">稳定版</option>
                <option value="release">发布版本</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">版本描述</label>
              <textarea
                v-model="releaseForm.description"
                class="form-textarea"
                placeholder="请输入版本描述和更新内容..."
                rows="4"
              ></textarea>
            </div>
          </div>

          <!-- 设备兼容性 -->
          <div class="form-section">
            <h4 class="form-section-title">设备兼容性</h4>
            
            <div class="form-group">
              <label class="form-label">适用设备 *</label>
              <div class="devices-selection">
                <div class="devices-input">
                  <input
                    v-model="deviceInput"
                    type="text"
                    class="form-input"
                    placeholder="输入设备型号，按回车添加"
                    @keydown.enter.prevent="addDevice"
                  />
                  <button type="button" class="btn btn-sm btn-outline" @click="addDevice">
                    添加
                  </button>
                </div>
                <div class="devices-list" v-if="releaseForm.devices.length > 0">
                  <div 
                    v-for="(device, index) in releaseForm.devices" 
                    :key="index"
                    class="device-tag"
                  >
                    {{ device }}
                    <button 
                      type="button" 
                      class="remove-device"
                      @click="removeDevice(index)"
                    >
                      ×
                    </button>
                  </div>
                </div>
              </div>
              <div class="form-hint">指定此版本适用的设备型号</div>
            </div>
          </div>

          <!-- 上传进度 -->
          <div v-if="uploading" class="upload-progress">
            <div class="progress-info">
              <span>正在上传...</span>
              <span>{{ uploadProgress }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
            </div>
          </div>

          <div v-if="formError" class="error-message">
            {{ formError }}
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeUploadModal">
              取消
            </button>
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="uploading || (!selectedFile && !editingRelease)"
            >
              {{ uploading ? '上传中...' : editingRelease ? '保存更改' : '发布版本' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="showDeleteModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="modal-close" @click="showDeleteModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除版本 <strong>{{ deletingRelease?.version }}</strong> 吗？</p>
          <p class="warning-text">此操作将删除版本文件，且不可恢复！</p>
        </div>
        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" @click="showDeleteModal = false">取消</button>
          <button type="button" class="btn btn-danger" @click="confirmDelete" :disabled="deleting">
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface Project {
  id: number
  name: string
  type: 'firmware' | 'app' | 'library'
}

interface Release {
  id: number
  projectId: number
  version: string
  type: 'development' | 'beta' | 'stable' | 'release'
  status: 'published' | 'draft' | 'archived'
  fileName: string
  fileSize: number
  description?: string
  devices: string[]
  createdAt: number
  updatedAt: number
}

interface ReleaseForm {
  projectId: number | string
  version: string
  type: '' | 'development' | 'beta' | 'stable' | 'release'
  description?: string
  devices: string[]
}

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const deleting = ref(false)
const myProjects = ref<Project[]>([])
const releases = ref<Release[]>([])
const selectedProjectId = ref<number | string>('')
const deviceInput = ref('')
const dragOver = ref(false)
const uploadProgress = ref(0)

// 弹窗状态
const showUploadModal = ref(false)
const showDeleteModal = ref(false)
const editingRelease = ref<Release | null>(null)
const deletingRelease = ref<Release | null>(null)

// 文件上传
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)

// 表单数据
const releaseForm = reactive<ReleaseForm>({
  projectId: '',
  version: '',
  type: '',
  description: '',
  devices: []
})

const formError = ref('')


// 项目类型标签
function getProjectTypeLabel(type: string) {
  const typeMap = {
    'firmware': '固件',
    'app': '应用程序',
    'library': '库文件'
  }
  return typeMap[type] || type
}

// 版本类型标签
function getReleaseTypeLabel(type: string) {
  const typeMap = {
    'development': '开发版',
    'beta': '测试版',
    'stable': '稳定版',
    'release': '发布版'
  }
  return typeMap[type] || type
}

// 版本状态标签
function getReleaseStatusLabel(status: string) {
  const statusMap = {
    'published': '已发布',
    'draft': '草稿',
    'archived': '已归档'
  }
  return statusMap[status] || status
}

// 格式化数字
function formatNumber(num: number) {
  if (num >= 10000) {
    return Math.floor(num / 1000) + 'k'
  }
  return num.toString()
}

// 格式化文件大小
function formatFileSize(bytes: number) {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return Math.round(size * 10) / 10 + units[unitIndex]
}

// 时间格式化
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载我的项目
async function loadMyProjects() {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    myProjects.value = [
      { id: 1, name: '智能家居控制系统', type: 'firmware' },
      { id: 2, name: '传感器数据采集', type: 'firmware' },
      { id: 3, name: '设备通信协议库', type: 'library' },
      { id: 4, name: '配置管理工具', type: 'app' }
    ]
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 加载版本列表
async function loadReleases() {
  if (!selectedProjectId.value) {
    releases.value = []
    return
  }
  
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟不同项目的版本数据
    if (selectedProjectId.value === 1) {
      releases.value = [
        {
          id: 1,
          projectId: 1,
          version: 'v2.1.0',
          type: 'stable',
          status: 'published',
          fileName: 'smart_home_v2.1.0.bin',
          fileSize: 1024 * 512, // 512KB
          description: '新增蓝牙连接功能，修复已知bug',
          devices: ['ESP32', 'ESP8266'],
          createdAt: Date.now() - 86400000 * 2,
          updatedAt: Date.now() - 86400000 * 2
        },
        {
          id: 2,
          projectId: 1,
          version: 'v2.0.1',
          type: 'stable',
          status: 'published',
          fileName: 'smart_home_v2.0.1.bin',
          fileSize: 1024 * 480,
          description: '修复连接稳定性问题',
          devices: ['ESP32'],
          createdAt: Date.now() - 86400000 * 15,
          updatedAt: Date.now() - 86400000 * 15
        }
      ]
    } else {
      releases.value = []
    }
  } catch (error) {
    console.error('加载版本列表失败:', error)
  } finally {
    loading.value = false
  }
}


// 触发文件选择
function triggerFileInput() {
  fileInput.value?.click()
}

// 处理文件选择
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    handleFile(file)
  }
}

// 处理拖拽
function handleDrop(event: DragEvent) {
  event.preventDefault()
  dragOver.value = false
  
  const file = event.dataTransfer?.files[0]
  if (file) {
    handleFile(file)
  }
}

// 处理文件
function handleFile(file: File) {
  // 文件大小检查
  const maxSize = 200 * 1024 * 1024 // 200MB
  if (file.size > maxSize) {
    formError.value = '文件大小不能超过 200MB'
    return
  }
  
  // 文件类型检查
  const allowedTypes = ['.bin', '.hex', '.zip', '.tar.gz', '.apk', '.exe', '.msi']
  const fileName = file.name.toLowerCase()
  const isValidType = allowedTypes.some(type => fileName.endsWith(type))
  
  if (!isValidType) {
    formError.value = '不支持的文件类型'
    return
  }
  
  selectedFile.value = file
  formError.value = ''
}

// 移除文件
function removeFile() {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 添加设备
function addDevice() {
  const device = deviceInput.value.trim()
  if (device && !releaseForm.devices.includes(device)) {
    releaseForm.devices.push(device)
    deviceInput.value = ''
  }
}

// 移除设备
function removeDevice(index: number) {
  releaseForm.devices.splice(index, 1)
}

// 编辑版本
function editRelease(release: Release) {
  editingRelease.value = release
  Object.assign(releaseForm, {
    projectId: release.projectId,
    version: release.version,
    type: release.type,
    description: release.description || '',
    devices: [...release.devices]
  })
  showUploadModal.value = true
}

// 下载版本
function downloadRelease(release: Release) {
  // 模拟下载
  const link = document.createElement('a')
  link.href = `/api/releases/${release.id}/download`
  link.download = release.fileName
  link.click()
}


// 删除版本
function deleteRelease(release: Release) {
  deletingRelease.value = release
  showDeleteModal.value = true
}

// 确认删除
async function confirmDelete() {
  if (!deletingRelease.value) return
  
  deleting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const index = releases.value.findIndex(r => r.id === deletingRelease.value!.id)
    if (index > -1) {
      releases.value.splice(index, 1)
    }
    
    showDeleteModal.value = false
    deletingRelease.value = null
  } catch (error) {
    console.error('删除失败:', error)
  } finally {
    deleting.value = false
  }
}

// 关闭上传弹窗
function closeUploadModal() {
  showUploadModal.value = false
  editingRelease.value = null
  selectedFile.value = null
  formError.value = ''
  deviceInput.value = ''
  uploadProgress.value = 0
  
  Object.assign(releaseForm, {
    projectId: selectedProjectId.value || '',
    version: '',
    type: '',
    description: '',
    devices: []
  })
  
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 提交发布
async function submitRelease() {
  formError.value = ''
  
  // 基础验证
  if (!editingRelease.value && !selectedFile.value) {
    formError.value = '请选择文件'
    return
  }
  
  if (!releaseForm.version || !releaseForm.type) {
    formError.value = '请填写必填项'
    return
  }
  
  if (releaseForm.devices.length === 0) {
    formError.value = '请添加至少一个适用设备'
    return
  }
  
  uploading.value = true
  uploadProgress.value = 0
  
  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10
      }
    }, 200)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    clearInterval(progressInterval)
    uploadProgress.value = 100
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (editingRelease.value) {
      // 更新现有版本
      Object.assign(editingRelease.value, {
        version: releaseForm.version,
        type: releaseForm.type,
        description: releaseForm.description,
        devices: [...releaseForm.devices],
        updatedAt: Date.now()
      })
    } else {
      // 创建新版本
      const newRelease: Release = {
        id: Date.now(),
        projectId: Number(releaseForm.projectId),
        version: releaseForm.version,
        type: releaseForm.type as Release['type'],
        status: 'published',
        fileName: selectedFile.value!.name,
        fileSize: selectedFile.value!.size,
        description: releaseForm.description,
        devices: [...releaseForm.devices],
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
      
      if (selectedProjectId.value === Number(releaseForm.projectId)) {
        releases.value.unshift(newRelease)
      }
    }
    
    closeUploadModal()
  } catch (error) {
    console.error('发布失败:', error)
    formError.value = '发布失败，请重试'
  } finally {
    uploading.value = false
  }
}

// 初始化
onMounted(() => {
  loadMyProjects()
})
</script>

<style scoped>
.release-manager {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 项目选择 */
.project-selector {
  margin-bottom: var(--spacing-6);
}

.project-select {
  width: 100%;
  max-width: 400px;
  height: 48px;
  padding: 0 var(--spacing-4);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

.project-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* 版本列表 */
.releases-section {
  margin-bottom: var(--spacing-8);
}

.releases-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.releases-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.releases-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-item {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.loading-state,
.empty-state {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-8);
  font-size: var(--font-size-lg);
}

.empty-state button {
  margin-top: var(--spacing-4);
}

.releases-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.release-item {
  padding: var(--spacing-5);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.release-item:hover {
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-sm);
}

.release-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.release-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.release-version {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0;
}

.release-type,
.release-status {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.release-type.development {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.release-type.beta {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.release-type.stable {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.release-type.release {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.release-status.published {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.release-status.draft {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.release-status.archived {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.release-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.release-size {
  font-weight: var(--font-weight-medium);
}

.release-description {
  margin-bottom: var(--spacing-4);
}

.release-description p {
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.release-details {
  margin-bottom: var(--spacing-4);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.detail-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.detail-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-1);
  font-weight: var(--font-weight-medium);
}


.release-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 600px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.large-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--bg-1);
  z-index: 1;
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-5);
}

.warning-text {
  color: var(--color-danger-dark);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-2);
}

.release-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-section-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input {
  height: 40px;
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-hint {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
}

/* 文件上传 */
.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.large-icon {
  font-size: 48px;
  color: var(--color-text-3);
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.upload-main {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-1);
  margin: 0;
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  margin: 0;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--bg-2);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-4);
}

.file-name {
  flex: 1;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-1);
}

.file-size {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.remove-file {
  background: none;
  border: none;
  color: var(--color-danger);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

/* 设备选择 */
.devices-selection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.devices-input {
  display: flex;
  gap: var(--spacing-2);
}

.devices-input .form-input {
  flex: 1;
}

.devices-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.device-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.remove-device {
  background: none;
  border: none;
  color: var(--color-primary-dark);
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
}

/* 上传进度 */
.upload-progress {
  padding: var(--spacing-4);
  background: var(--bg-2);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-1);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-3);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  transition: width var(--transition-base);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  position: sticky;
  bottom: 0;
  background: var(--bg-1);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .project-select {
    max-width: none;
  }
  
  .releases-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .releases-stats {
    width: 100%;
    justify-content: space-between;
  }
  
  .release-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .release-meta {
    align-items: flex-start;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .release-actions {
    width: 100%;
  }
  
  .modal-content {
    margin: var(--spacing-2);
    max-height: 95vh;
  }
  
  .large-modal {
    max-width: none;
  }
  
  .release-form {
    padding: var(--spacing-4);
  }
  
  .devices-input {
    flex-direction: column;
  }
}
</style>

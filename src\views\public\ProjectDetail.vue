<template>
  <div class="page page-project-detail">
    <div class="project-header">
      <ProjectHeader 
        :title="project?.name || `项目 ${projectId}`"
        :description="projectData.description"
      />
      <ProjectStats :stats="projectStats" />
    </div>

    <div class="project-content">
      <div class="categories">
        <div class="category-card card" v-for="cat in artifacts" :key="cat.key">
          <div class="category-header">
            <h3 class="category-title">{{ cat.name }}</h3>
          </div>
          <div v-if="cat.latest" class="latest">
            <div class="latest-top">
              <div class="ver">{{ cat.latest.version }}</div>
              <div class="meta">{{ new Date(cat.latest.createdAt).toLocaleDateString() }} · {{ mapType(cat.latest.type) }}</div>
            </div>
            <div class="desc">{{ cat.latest.changelog || '—' }}</div>
            <div class="actions">
              <a v-if="cat.latest.downloadUrl" :href="cat.latest.downloadUrl" target="_blank" class="btn btn-primary btn-sm">下载最新</a>
              <RouterLink class="btn btn-outline btn-sm" :to="{ name: 'SoftwareDetail', params: { projectId: projectId, artifact: cat.key } }">历史版本</RouterLink>
            </div>
          </div>
          <div v-else class="empty">暂无版本</div>
        </div>
      </div>

      <div class="project-sidebar">
        <ProjectInfo :info-items="projectInfo" />
        <ProjectHelp @contact-support="handleContactSupport" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import ProjectHeader from '@/components/business/ProjectHeader.vue'
import ProjectStats from '@/components/business/ProjectStats.vue'
 
import ProjectInfo from '@/components/business/ProjectInfo.vue'
import ProjectHelp from '@/components/business/ProjectHelp.vue'
import { useProjectsStore } from '@/stores/projects'
import { useReleasesStore } from '@/stores/releases'

const route = useRoute()
const projectId = computed(() => Number(route.params.id))
const projects = useProjectsStore()
const releases = useReleasesStore()

const project = ref<any>(null)

const projectData = computed(() => ({
  description: project.value?.description || '—',
}))

const projectStats = computed(() => [
  { value: String(project.value?.stats?.versionCount ?? releases.total), label: '版本数' },
  { value: project.value?.updatedAt ? new Date(project.value.updatedAt).toLocaleString() : '—', label: '最近更新' },
])

const artifactKeys = ['firmware', 'android-demo', 'sdk'] as const
type ArtifactKey = typeof artifactKeys[number]

const artifacts = computed(() => {
  const groups: Record<ArtifactKey, any[]> = { 'firmware': [], 'android-demo': [], 'sdk': [] }
  for (const r of releases.list) {
    const key = ((r as any).artifact || 'firmware') as ArtifactKey
    if (!groups[key]) continue
    groups[key].push(r)
  }
  const latestOf = (arr: any[]) => arr.slice().sort((a, b) => b.createdAt - a.createdAt)[0]
  return [
    { key: 'firmware', name: '固件', latest: latestOf(groups['firmware'] || []) },
    { key: 'android-demo', name: 'Android Demo', latest: latestOf(groups['android-demo'] || []) },
    { key: 'sdk', name: 'SDK', latest: latestOf(groups['sdk'] || []) },
  ]
})

const projectInfo = computed(() => [
  { label: '创建时间', value: project.value?.createdAt ? new Date(project.value.createdAt).toLocaleDateString() : '—' },
])

// 事件处理函数
function handleDownload(version: any) {
  const item = releases.list.find((r) => r.version === version.number)
  if (item?.downloadUrl) {
    window.open(item.downloadUrl, '_blank')
  }
}

function handleViewDetails(version: any) {
  console.log('查看版本详情:', version)
  // TODO: 实现查看详情逻辑
}

function handleContactSupport() {
  console.log('联系技术支持')
  // TODO: 实现联系支持逻辑
}

function mapType(t?: string) {
  if (t === 'firmware') return '固件'
  if (t === 'app') return '应用'
  if (t === 'library') return '库'
  return '—'
}

onMounted(async () => {
  project.value = await projects.getDetail(projectId.value)
  await releases.loadByProject(projectId.value, { page: 1, pageSize: 50, sortBy: 'createdAt', order: 'desc' })
})

async function handleChangePage(page: number) {}
</script>

<style scoped>
.page {
  padding: var(--spacing-6) 0;
}

.project-header {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
}

.project-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--spacing-8);
}

.categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
}
.category-card { padding: var(--spacing-6); }
.category-header { display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--spacing-3); }
.category-title { margin: 0; font-size: var(--font-size-lg); }
.latest-top { display: flex; align-items: baseline; gap: var(--spacing-2); }
.ver { font-weight: var(--font-weight-semibold); }
.meta { color: var(--color-text-3); font-size: var(--font-size-sm); }
.desc { color: var(--color-text-2); margin: var(--spacing-2) 0 var(--spacing-3); }
.actions { display: flex; gap: var(--spacing-2); }

.project-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .project-content {
    grid-template-columns: 1fr;
  }
  
  .project-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .project-header {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}
</style>

import { ref, computed } from 'vue'

// 全局加载状态
const globalLoading = ref(false)
const globalProgress = ref(0)
const loadingTitle = ref('加载中...')
const loadingDescription = ref('')
const showProgress = ref(false)

// 顶部进度条状态
const topProgressVisible = ref(false)
const topProgress = ref(0)
const topProgressColor = ref('var(--color-primary)')

// 加载队列管理
const loadingQueue = ref(new Set<string>())

/**
 * 加载状态管理
 */
export function useLoading() {
  // 显示全页面加载
  const showPageLoading = (options?: {
    title?: string
    description?: string
    showProgress?: boolean
  }) => {
    if (options?.title) loadingTitle.value = options.title
    if (options?.description) loadingDescription.value = options.description
    if (options?.showProgress !== undefined) showProgress.value = options.showProgress
    
    globalLoading.value = true
    globalProgress.value = 0
  }

  // 隐藏全页面加载
  const hidePageLoading = () => {
    globalLoading.value = false
    globalProgress.value = 0
    loadingTitle.value = '加载中...'
    loadingDescription.value = ''
    showProgress.value = false
  }

  // 更新加载进度
  const updateProgress = (progress: number) => {
    globalProgress.value = Math.max(0, Math.min(100, progress))
  }

  // 显示顶部进度条
  const showTopProgress = () => {
    topProgressVisible.value = true
    topProgress.value = 0
  }

  // 隐藏顶部进度条
  const hideTopProgress = () => {
    topProgress.value = 100
    setTimeout(() => {
      topProgressVisible.value = false
      topProgress.value = 0
    }, 200)
  }

  // 更新顶部进度条
  const updateTopProgress = (progress: number) => {
    topProgress.value = Math.max(0, Math.min(100, progress))
  }

  // 设置顶部进度条颜色
  const setTopProgressColor = (color: string) => {
    topProgressColor.value = color
  }

  // 队列式加载管理
  const startLoading = (key: string) => {
    loadingQueue.value.add(key)
  }

  const stopLoading = (key: string) => {
    loadingQueue.value.delete(key)
  }

  // 清除所有加载状态
  const clearAllLoading = () => {
    loadingQueue.value.clear()
    hidePageLoading()
    hideTopProgress()
  }

  return {
    // 全页面加载状态
    globalLoading: computed(() => globalLoading.value),
    globalProgress: computed(() => globalProgress.value),
    loadingTitle: computed(() => loadingTitle.value),
    loadingDescription: computed(() => loadingDescription.value),
    showProgress: computed(() => showProgress.value),
    
    // 顶部进度条状态
    topProgressVisible: computed(() => topProgressVisible.value),
    topProgress: computed(() => topProgress.value),
    topProgressColor: computed(() => topProgressColor.value),
    
    // 队列状态
    isLoading: computed(() => loadingQueue.value.size > 0),
    loadingCount: computed(() => loadingQueue.value.size),
    
    // 方法
    showPageLoading,
    hidePageLoading,
    updateProgress,
    showTopProgress,
    hideTopProgress,
    updateTopProgress,
    setTopProgressColor,
    startLoading,
    stopLoading,
    clearAllLoading
  }
}

/**
 * 路由加载进度管理
 */
export function useRouterProgress() {
  const { 
    showTopProgress, 
    hideTopProgress, 
    updateTopProgress,
    setTopProgressColor 
  } = useLoading()

  let progressTimer: number | null = null

  const startRouterProgress = () => {
    setTopProgressColor('var(--color-primary)')
    showTopProgress()
    
    // 模拟进度增长
    let progress = 0
    progressTimer = window.setInterval(() => {
      progress += Math.random() * 15
      if (progress > 90) {
        progress = 90
        if (progressTimer) {
          clearInterval(progressTimer)
          progressTimer = null
        }
      }
      updateTopProgress(progress)
    }, 100)
  }

  const finishRouterProgress = () => {
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
    updateTopProgress(100)
    hideTopProgress()
  }

  const failRouterProgress = () => {
    setTopProgressColor('var(--color-danger)')
    setTimeout(() => {
      finishRouterProgress()
    }, 500)
  }

  return {
    startRouterProgress,
    finishRouterProgress,
    failRouterProgress
  }
}

/**
 * 异步操作加载管理
 */
export function useAsyncLoading() {
  const { startLoading, stopLoading } = useLoading()

  /**
   * 包装异步函数，自动管理加载状态
   */
  const withLoading = async <T>(
    fn: () => Promise<T>,
    key: string = 'default'
  ): Promise<T> => {
    try {
      startLoading(key)
      const result = await fn()
      return result
    } finally {
      stopLoading(key)
    }
  }

  /**
   * 包装异步函数，显示全页面加载
   */
  const withPageLoading = async <T>(
    fn: () => Promise<T>,
    options?: {
      title?: string
      description?: string
      showProgress?: boolean
    }
  ): Promise<T> => {
    const { showPageLoading, hidePageLoading } = useLoading()
    
    try {
      showPageLoading(options)
      const result = await fn()
      return result
    } finally {
      hidePageLoading()
    }
  }

  return {
    withLoading,
    withPageLoading
  }
}

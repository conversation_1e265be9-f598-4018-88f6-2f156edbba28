import { defineStore } from 'pinia'
import { getDataService } from '@/data'

const dataService = getDataService()
import type { SearchResultItem, SearchPageResult, Facets } from '@/types/domain'
import { SimpleCache } from '@/utils/cache'

interface SearchState {
  q: string
  type: string
  versionType: string
  device: string
  page: number
  pageSize: number
  list: SearchResultItem[]
  total: number
  facets?: Facets
  loading: boolean
  error: string | null
  history: string[]
  cache: SimpleCache<SearchPageResult<SearchResultItem>>
}

export const useSearchStore = defineStore('search', {
  state: (): SearchState => ({
    q: '',
    type: '',
    versionType: '',
    device: '',
    page: 1,
    pageSize: 10,
    list: [],
    total: 0,
    facets: undefined,
    loading: false,
    error: null,
    history: [],
    cache: new SimpleCache<SearchPageResult<SearchResultItem>>({ ttlMs: 60 * 1000 }),
  }),
  actions: {
    async search(params?: any) {
      if (params) {
        this.q = params.q !== undefined ? params.q : this.q
        this.type = params.type !== undefined ? (params.type as string) : this.type
        this.versionType = params.versionType !== undefined ? (params.versionType as string) : this.versionType
        this.device = params.device !== undefined ? (params.device as string) : this.device
        this.page = params.page !== undefined ? (params.page as number) : this.page
        this.pageSize = params.pageSize !== undefined ? (params.pageSize as number) : this.pageSize
      }

      const key = JSON.stringify({ q: this.q, type: this.type, versionType: this.versionType, device: this.device, page: this.page, pageSize: this.pageSize })
      const cached = this.cache.get(key)
      if (cached) {
        this.list = cached.list
        this.total = cached.total
        this.facets = cached.facets
        return
      }

      this.loading = true
      this.error = null
      try {
        const res = await dataService.search(this.q, {
          page: this.page,
          pageSize: this.pageSize,
          type: this.type || undefined
        })
        this.list = res.list
        this.total = res.total
        this.facets = res.facets
        this.cache.set(key, res)
      } catch (e: any) {
        this.error = e?.message || String(e)
        throw e
      } finally {
        this.loading = false
      }
    },
    saveHistory(keyword: string) {
      const val = keyword.trim()
      if (!val) return
      this.history = [val, ...this.history.filter((x) => x !== val)].slice(0, 10)
      try {
        localStorage.setItem('search_history_v1', JSON.stringify(this.history))
      } catch {}
    },
    restoreHistory() {
      try {
        const raw = localStorage.getItem('search_history_v1')
        if (raw) this.history = JSON.parse(raw)
      } catch {}
    },
    clear() {
      this.q = ''
      this.type = ''
      this.versionType = ''
      this.device = ''
      this.page = 1
      this.list = []
      this.total = 0
    },
  },
})



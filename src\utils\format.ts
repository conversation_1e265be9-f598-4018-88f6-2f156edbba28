import type { ArtifactKind, ReleaseType } from '@/types/domain'

export function formatDateTime(input?: number | string | Date): string {
  if (!input && input !== 0) return ''
  const date = input instanceof Date ? input : new Date(input)
  if (Number.isNaN(date.getTime())) return ''
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

export function formatReleaseType(type?: ReleaseType): string {
  if (!type) return ''
  switch (type) {
    case 'stable':
      return '稳定版'
    case 'beta':
      return '测试版'
    case 'alpha':
      return '开发版'
    default:
      return String(type)
  }
}

export function formatArtifact(kind?: ArtifactKind): string {
  if (!kind) return ''
  switch (kind) {
    case 'firmware':
      return '固件'
    case 'android-demo':
      return '安卓演示'
    case 'sdk':
      return 'SDK'
    case 'app':
      return '应用'
    case 'library':
      return '库'
    default:
      return String(kind)
  }
}

export function formatBytes(size?: number): string {
  if (size === undefined || size === null) return ''
  if (size < 1024) return `${size} B`
  const units = ['KB', 'MB', 'GB', 'TB']
  let value = size / 1024
  let unitIndex = 0
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex += 1
  }
  return `${value.toFixed(1)} ${units[unitIndex]}`
}




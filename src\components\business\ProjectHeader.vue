<template>
  <div class="project-basic-info">
    <h1 class="project-title">{{ title }}</h1>
    <p class="project-description">{{ description }}</p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  description: string
}

defineProps<Props>()
</script>

<style scoped>
.project-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-3) 0;
}

.project-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-2);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-4) 0;
}

</style>

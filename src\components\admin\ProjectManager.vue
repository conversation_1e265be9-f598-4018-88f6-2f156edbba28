<template>
  <div class="project-manager">
    <div class="section-header">
      <h2 class="section-title">项目管理</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="showCreateModal = true">
          新建项目
        </button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filters-bar">
      <div class="search-box">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜索项目..." 
          class="search-input"
          @input="handleSearch"
        >
      </div>
      <div class="filter-options">
        <select v-model="statusFilter" class="filter-select" @change="handleFilter">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">已暂停</option>
        </select>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="projects-grid">
      <div v-if="loading && projects.length === 0" class="loading-state">
        <p>正在加载项目...</p>
      </div>
      <div v-else-if="projects.length === 0" class="empty-state">
        <p>暂无符合条件的项目</p>
      </div>
      <div 
        v-for="project in paginatedProjects" 
        :key="project.id"
        class="project-card card"
      >
        <div class="project-header">
          <div class="project-title">
            <h3 class="project-name">{{ project.name }}</h3>
          </div>
          <div class="project-actions">
            <button class="btn btn-sm btn-outline" @click="editProject(project)">
              编辑
            </button>
            <button 
              class="btn btn-sm btn-danger" 
              @click="deleteProject(project)"
            >
              删除
            </button>
          </div>
        </div>

        <p class="project-description">{{ project.description || '暂无描述' }}</p>

        <div class="project-meta">
          <div class="meta-row">
            <span class="meta-label">状态：</span>
            <span :class="['status-badge', project.status]">
              {{ project.status === 'active' ? '活跃' : '已暂停' }}
            </span>
          </div>
          <div class="meta-row">
            <span class="meta-label">创建时间：</span>
            <span class="meta-value">{{ formatTime(project.createdAt) }}</span>
          </div>
          <div class="meta-row">
            <span class="meta-label">更新时间：</span>
            <span class="meta-value">{{ formatTime(project.updatedAt) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button 
        class="btn btn-sm btn-outline" 
        :disabled="currentPage <= 1" 
        @click="changePage(currentPage - 1)"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
      </span>
      <button 
        class="btn btn-sm btn-outline" 
        :disabled="currentPage >= totalPages" 
        @click="changePage(currentPage + 1)"
      >
        下一页
      </button>
    </div>

    <!-- 创建/编辑项目弹窗 -->
    <div v-if="showCreateModal" class="modal-overlay" @click="closeCreateModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingProject ? '编辑项目' : '新建项目' }}</h3>
          <button class="modal-close" @click="closeCreateModal">×</button>
        </div>
        <form class="project-form" @submit.prevent="saveProject">
          <div class="form-group">
            <label class="form-label">项目名称 *</label>
            <input
              v-model="projectForm.name"
              type="text"
              class="form-input"
              placeholder="请输入项目名称"
              required
            />
          </div>
          <div class="form-group">
            <label class="form-label">项目描述</label>
            <textarea
              v-model="projectForm.description"
              class="form-textarea"
              placeholder="请输入项目描述"
              rows="3"
            ></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">状态</label>
            <select v-model="projectForm.status" class="form-select">
              <option value="active">活跃</option>
              <option value="inactive">已暂停</option>
            </select>
          </div>
          <div v-if="formError" class="error-message">
            {{ formError }}
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeCreateModal">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? '保存中...' : '确认保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="showDeleteModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="modal-close" @click="showDeleteModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除项目 <strong>{{ deletingProject?.name }}</strong> 吗？</p>
          <p class="warning-text">此操作将删除项目的所有版本和文件，且不可恢复！</p>
        </div>
        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" @click="showDeleteModal = false">取消</button>
          <button type="button" class="btn btn-danger" @click="confirmDelete" :disabled="deleting">
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { getDataService } from '@/data'
import type { ProjectItem } from '@/types/domain'

const dataService = getDataService()

interface ProjectForm {
  name: string
  description?: string
  status: 'active' | 'inactive'
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const projects = ref<ProjectItem[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = 12

// 弹窗状态
const showCreateModal = ref(false)
const showDeleteModal = ref(false)
const editingProject = ref<ProjectItem | null>(null)
const deletingProject = ref<ProjectItem | null>(null)

// 表单数据
const projectForm = reactive<ProjectForm>({
  name: '',
  description: '',
  status: 'active'
})

const formError = ref('')

// 分页后的项目列表就是从服务返回的列表
const paginatedProjects = computed(() => projects.value)

const totalPages = computed(() => {
  return Math.ceil(totalCount.value / pageSize)
})

// 时间格式化
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  loadProjects()
}

// 过滤处理
function handleFilter() {
  currentPage.value = 1
  loadProjects()
}

// 分页
function changePage(page: number) {
  currentPage.value = page
  loadProjects()
}

// 加载项目列表
async function loadProjects() {
  loading.value = true
  try {
    const result = await dataService.getProjects({
      page: currentPage.value,
      pageSize,
      search: searchQuery.value,
      status: statusFilter.value,
      sortBy: 'updatedAt',
      order: 'desc'
    })
    projects.value = result.list
    totalCount.value = result.total
  } catch (error) {
    console.error('加载项目列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 添加总数变量
const totalCount = ref(0)


// 编辑项目
function editProject(project: ProjectItem) {
  editingProject.value = project
  Object.assign(projectForm, {
    name: project.name,
    description: project.description || '',
    status: project.status
  })
  showCreateModal.value = true
}

// 删除项目
function deleteProject(project: ProjectItem) {
  deletingProject.value = project
  showDeleteModal.value = true
}

// 确认删除
async function confirmDelete() {
  if (!deletingProject.value) return
  
  deleting.value = true
  try {
    await dataService.deleteProject(deletingProject.value.id)
    
    showDeleteModal.value = false
    deletingProject.value = null
    
    // 重新加载项目列表
    await loadProjects()
  } catch (error) {
    console.error('删除失败:', error)
  } finally {
    deleting.value = false
  }
}

// 关闭创建弹窗
function closeCreateModal() {
  showCreateModal.value = false
  editingProject.value = null
  formError.value = ''
  Object.assign(projectForm, {
    name: '',
    description: '',
    status: 'active'
  })
}

// 保存项目
async function saveProject() {
  formError.value = ''
  
  // 基础验证
  if (!projectForm.name.trim()) {
    formError.value = '请填写项目名称'
    return
  }
  
  saving.value = true
  try {
    if (editingProject.value) {
      // 更新现有项目
      await dataService.updateProject(editingProject.value.id, {
        name: projectForm.name,
        description: projectForm.description,
        status: projectForm.status
      })
    } else {
      // 创建新项目
      await dataService.createProject({
        name: projectForm.name,
        description: projectForm.description,
        status: projectForm.status
      })
    }
    
    closeCreateModal()
    // 重新加载项目列表
    await loadProjects()
  } catch (error) {
    console.error('保存失败:', error)
    formError.value = '保存失败，请重试'
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.project-manager {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 搜索和过滤栏 */
.filters-bar {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.filter-options {
  display: flex;
  gap: var(--spacing-3);
}

.filter-select {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.loading-state,
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-8);
  font-size: var(--font-size-lg);
}

.project-card {
  padding: var(--spacing-5);
  transition: all var(--transition-base);
  border: 1px solid var(--border-color);
}

.project-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-light);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.project-title {
  flex: 1;
}

.project-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-2) 0;
}


.project-actions {
  display: flex;
  gap: var(--spacing-2);
}

.project-description {
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-4) 0;
}

.project-meta {
  margin-bottom: var(--spacing-4);
}

.meta-row {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-label {
  color: var(--color-text-3);
  min-width: 90px;
  font-weight: var(--font-weight-medium);
}

.meta-value {
  color: var(--color-text-1);
}


.status-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.active {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-badge.inactive {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4) 0;
}

.page-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 520px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-5);
}

.warning-text {
  color: var(--color-danger-dark);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-2);
}

.project-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input,
.form-select {
  height: 40px;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .filters-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .project-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .project-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .project-footer {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
  }
}
</style>

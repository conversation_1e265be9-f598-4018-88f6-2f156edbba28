import axios from 'axios'
import { eventBus } from '@/utils/eventBus'

export async function uploadFile(data: { name: string; size?: number }, onProgress?: (p: number) => void) {
  // 模拟上传进度：实际项目使用 axios 的 onUploadProgress
  const total = 100
  for (let p = 0; p <= total; p += 10) {
    await new Promise((r) => setTimeout(r, 80))
    onProgress?.(p)
    eventBus.emit('upload:progress', { progress: p })
  }
  // 提交到 mock 接口
  const resp = await fetch('/upload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  })
  const json = await resp.json()
  return json
}



# 数据架构设计

## 📊 数据需求分析

### 1. 数据分类与特征

#### 1.1 业务数据分类

**核心业务数据**
- **用户数据**: 用户基本信息、登录凭证、权限信息
  - 特征：数据量小、访问频繁、一致性要求高
  - 预估量：100-200条记录
  - 增长率：年增长20-50条

- **项目数据**: 项目基本信息、项目配置、项目状态
  - 特征：数据量中等、读写比例高、需要搜索
  - 预估量：100-500条记录
  - 增长率：年增长50-100条

- **版本发布数据**: 版本信息、文件元数据、发布记录
  - 特征：数据量大、主要读取、历史数据重要
  - 预估量：5000-20000条记录
  - 增长率：年增长2000-5000条

**权限关系数据**
- **用户项目权限**: 用户与项目的权限关系
  - 特征：关系型数据、查询频繁、实时性要求高
  - 预估量：200-1000条记录
  - 增长率：随用户和项目增长

**日志审计数据**
- **操作日志**: 用户操作记录、系统事件日志
  - 特征：只写入、数据量大、需要归档
  - 预估量：每日100-1000条
  - 增长率：随系统使用量线性增长

- **下载日志**: 文件下载记录、统计数据
  - 特征：高并发写入、主要用于统计分析
  - 预估量：每日500-5000条
  - 增长率：随用户量和文件数量增长

#### 1.2 数据存储特征分析

**数据访问模式**
```
数据类型        读写比例    并发要求    一致性要求    存储需求
用户数据        70:30       中等        强一致性      关系型
项目数据        80:20       中等        强一致性      关系型  
版本数据        95:5        高          最终一致      关系型
权限数据        90:10       高          强一致性      关系型
操作日志        5:95        低          最终一致      时序型
下载日志        10:90       高          最终一致      时序型
```

**数据生命周期**
- **永久保存**: 用户数据、项目数据、版本数据
- **定期归档**: 操作日志（2年）、下载日志（1年）
- **定期清理**: 临时文件、缓存数据、会话数据

### 2. 数据存储架构

#### 2.1 数据库选型分析

**关系型数据库选择**
- **MySQL优势**:
  - 事务ACID支持完善
  - 索引和查询优化成熟
  - 运维工具和经验丰富
  - 企业级稳定性

- **数据库版本**: MySQL 8.0+
  - 支持JSON数据类型
  - 窗口函数支持
  - 改进的全文索引
  - 更好的性能优化

**数据存储策略**
```
┌─────────────────────────────────────┐
│            MySQL 8.0               │
│                                     │
│  ┌─────────────┐  ┌─────────────┐  │
│  │  Core DB    │  │   Log DB    │  │
│  │ (业务数据)   │  │  (日志数据)  │  │
│  │             │  │             │  │
│  │ • users     │  │ • operation │  │
│  │ • projects  │  │   _logs     │  │
│  │ • releases  │  │ • download  │  │
│  │ • permissions│  │   _logs     │  │
│  └─────────────┘  └─────────────┘  │
└─────────────────────────────────────┘
```

#### 2.2 数据分区策略

**表分区设计**
```sql
-- 操作日志按月分区
CREATE TABLE operation_logs (
    id BIGINT NOT NULL AUTO_INCREMENT,
    created_at DATETIME NOT NULL,
    ...
) PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    ...
);

-- 下载日志按日分区
CREATE TABLE download_logs (
    id BIGINT NOT NULL AUTO_INCREMENT,
    download_started_at DATETIME NOT NULL,
    ...
) PARTITION BY RANGE (TO_DAYS(download_started_at)) (
    PARTITION p20240101 VALUES LESS THAN (TO_DAYS('2024-01-02')),
    PARTITION p20240102 VALUES LESS THAN (TO_DAYS('2024-01-03')),
    ...
);
```

### 3. 数据模型设计

#### 3.1 实体关系设计

**核心实体关系图**
```
┌─────────────┐         ┌─────────────┐
│    User     │         │   Project   │
│    (用户)    │◄────────┤   (项目)     │
│             │  N:1    │             │
└──────┬──────┘         └──────┬──────┘
       │                       │
       │ 1:N                   │ 1:N
       │                       │
       ▼                       ▼
┌─────────────┐         ┌─────────────┐
│UserProject  │         │   Release   │
│Permission   │         │   (版本)     │
│(用户项目权限)│         │             │
└─────────────┘         └──────┬──────┘
                               │
                               │ 1:N
                               ▼
                        ┌─────────────┐
                        │DownloadLog  │
                        │ (下载日志)   │
                        └─────────────┘
```

#### 3.2 数据字典设计

**用户表 (users)**
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    role ENUM('ADMIN', 'DEVELOPER') NOT NULL COMMENT '用户角色',
    status ENUM('ACTIVE', 'DISABLED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    last_login_at DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0 COMMENT '乐观锁版本号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**项目表 (projects)**
```sql
CREATE TABLE projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    category ENUM('FIRMWARE', 'APP') NOT NULL COMMENT '项目分类',
    status ENUM('ACTIVE', 'PAUSED', 'DEPRECATED') DEFAULT 'ACTIVE' COMMENT '项目状态',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    total_downloads BIGINT DEFAULT 0 COMMENT '总下载量',
    latest_version VARCHAR(20) COMMENT '最新版本号',
    latest_release_at DATETIME COMMENT '最新发布时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0 COMMENT '乐观锁版本号',
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**版本发布表 (software_releases)**
```sql
CREATE TABLE software_releases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL COMMENT '项目ID',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    version_type ENUM('DEVELOPMENT', 'BETA', 'STABLE', 'RELEASE') NOT NULL COMMENT '版本类型',
    title VARCHAR(200) NOT NULL COMMENT '版本标题',
    description TEXT COMMENT '版本描述',
    device_type VARCHAR(50) NOT NULL COMMENT '设备类型',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_hash VARCHAR(128) NOT NULL COMMENT '文件SHA256哈希',
    download_count BIGINT DEFAULT 0 COMMENT '下载次数',
    status ENUM('DRAFT', 'PUBLISHED', 'DEPRECATED') DEFAULT 'PUBLISHED' COMMENT '版本状态',
    publisher_id BIGINT NOT NULL COMMENT '发布者ID',
    compatibility TEXT COMMENT '兼容性说明',
    published_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0 COMMENT '乐观锁版本号',
    UNIQUE KEY uk_project_version (project_id, version),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (publisher_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4. 数据一致性设计

#### 4.1 事务设计原则

**事务边界划分**
- **业务事务**: Service层方法级别事务
- **数据一致性**: 强一致性要求的操作使用事务
- **性能考虑**: 只读操作使用只读事务
- **嵌套事务**: 避免过深的事务嵌套

**事务隔离级别**
```sql
-- 设置合适的事务隔离级别
SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED;

-- 核心业务操作使用可重复读
SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
```

#### 4.2 数据完整性约束

**主键约束**
- 所有表必须有主键
- 使用自增BIGINT作为主键
- 复合主键用于关系表

**外键约束**
```sql
-- 用户项目权限外键
ALTER TABLE user_project_permissions 
ADD CONSTRAINT fk_upp_user FOREIGN KEY (user_id) REFERENCES users(id),
ADD CONSTRAINT fk_upp_project FOREIGN KEY (project_id) REFERENCES projects(id);

-- 版本发布外键
ALTER TABLE software_releases
ADD CONSTRAINT fk_release_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE;
```

**唯一性约束**
```sql
-- 用户名唯一
ALTER TABLE users ADD UNIQUE KEY uk_username (username);

-- 项目名称唯一  
ALTER TABLE projects ADD UNIQUE KEY uk_project_name (name);

-- 项目版本号唯一
ALTER TABLE software_releases ADD UNIQUE KEY uk_project_version (project_id, version);
```

#### 4.3 并发控制设计

**乐观锁机制**
```sql
-- 添加版本号字段
ALTER TABLE users ADD COLUMN version BIGINT DEFAULT 0;
ALTER TABLE projects ADD COLUMN version BIGINT DEFAULT 0;
ALTER TABLE software_releases ADD COLUMN version BIGINT DEFAULT 0;

-- 更新时检查版本号
UPDATE users SET 
  username = ?, 
  version = version + 1 
WHERE id = ? AND version = ?;
```

**悲观锁场景**
```sql
-- 下载计数更新使用悲观锁
SELECT download_count FROM software_releases 
WHERE id = ? FOR UPDATE;

UPDATE software_releases 
SET download_count = download_count + 1 
WHERE id = ?;
```

### 5. 数据索引设计

#### 5.1 索引策略

**主键索引**
- 所有表自动创建主键索引
- 使用聚簇索引提高查询性能

**唯一索引**
```sql
-- 用户名唯一索引
CREATE UNIQUE INDEX idx_users_username ON users(username);

-- 邮箱唯一索引
CREATE UNIQUE INDEX idx_users_email ON users(email);

-- 项目名称唯一索引
CREATE UNIQUE INDEX idx_projects_name ON projects(name);
```

**复合索引**
```sql
-- 项目查询索引
CREATE INDEX idx_projects_category_status ON projects(category, status);

-- 版本查询索引
CREATE INDEX idx_releases_project_version ON software_releases(project_id, version_type, published_at);

-- 权限查询索引
CREATE INDEX idx_permissions_user_project ON user_project_permissions(user_id, project_id);

-- 日志查询索引
CREATE INDEX idx_operation_logs_user_time ON operation_logs(user_id, created_at);
CREATE INDEX idx_download_logs_release_time ON download_logs(release_id, download_started_at);
```

**全文索引**
```sql
-- 项目搜索全文索引
ALTER TABLE projects ADD FULLTEXT(name, description);

-- 版本搜索全文索引
ALTER TABLE software_releases ADD FULLTEXT(title, description);
```

#### 5.2 索引优化策略

**查询优化**
- 根据WHERE条件建立索引
- 避免函数索引和隐式转换
- 使用覆盖索引减少回表
- 合理使用前缀索引

**索引维护**
```sql
-- 定期分析表统计信息
ANALYZE TABLE users, projects, software_releases;

-- 检查索引使用情况
SELECT 
  table_name,
  index_name,
  cardinality,
  packed
FROM information_schema.statistics 
WHERE table_schema = 'sw_publish';
```

### 6. 数据分页设计

#### 6.1 分页策略

**基于OFFSET的分页**
```sql
-- 适用于小数据量分页
SELECT * FROM projects 
WHERE status = 'ACTIVE'
ORDER BY updated_at DESC
LIMIT 20 OFFSET 40;
```

**基于游标的分页**
```sql
-- 适用于大数据量分页
SELECT * FROM software_releases 
WHERE published_at < '2024-01-01 10:00:00'
ORDER BY published_at DESC
LIMIT 20;
```

#### 6.2 分页性能优化

**深分页优化**
```sql
-- 使用子查询优化深分页
SELECT r.* FROM software_releases r
INNER JOIN (
  SELECT id FROM software_releases 
  WHERE project_id = ?
  ORDER BY published_at DESC
  LIMIT 20 OFFSET 1000
) t ON r.id = t.id;
```

### 7. 数据备份与恢复

#### 7.1 备份策略

**全量备份**
```bash
# 每周全量备份
mysqldump --single-transaction --routines --triggers \
  --all-databases > backup_$(date +%Y%m%d).sql
```

**增量备份**
```bash
# 每日增量备份
mysqlbinlog --start-datetime="2024-01-01 00:00:00" \
  --stop-datetime="2024-01-01 23:59:59" \
  mysql-bin.000001 > incremental_$(date +%Y%m%d).sql
```

#### 7.2 恢复策略

**恢复流程**
1. 停止应用服务
2. 恢复全量备份
3. 应用增量备份
4. 检查数据一致性
5. 重启应用服务

### 8. 数据监控设计

#### 8.1 监控指标

**性能监控**
```sql
-- 慢查询监控
SELECT 
  query_time,
  lock_time,
  rows_sent,
  rows_examined,
  sql_text
FROM mysql.slow_log
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- 连接数监控
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';
```

**容量监控**
```sql
-- 表大小监控
SELECT 
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
FROM information_schema.tables 
WHERE table_schema = 'sw_publish'
ORDER BY size_mb DESC;
```

#### 8.2 数据质量监控

**数据一致性检查**
```sql
-- 检查外键一致性
SELECT COUNT(*) FROM user_project_permissions upp
LEFT JOIN users u ON upp.user_id = u.id
WHERE u.id IS NULL;

-- 检查数据完整性
SELECT project_id, COUNT(*) as release_count
FROM software_releases 
GROUP BY project_id
HAVING release_count = 0;
```

### 9. 数据迁移设计

#### 9.1 版本升级策略

**数据库版本管理**
- 使用Flyway进行数据库版本管理
- 编写可回滚的迁移脚本
- 测试环境先行验证

**迁移脚本示例**
```sql
-- V1.1__Add_user_email_column.sql
ALTER TABLE users 
ADD COLUMN email VARCHAR(100) UNIQUE COMMENT '邮箱';

-- 数据迁移
UPDATE users SET email = CONCAT(username, '@company.com');
```

#### 9.2 数据清理策略

**历史数据清理**
```sql
-- 清理过期操作日志
DELETE FROM operation_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- 清理过期下载日志
DELETE FROM download_logs 
WHERE download_started_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

这个数据架构设计为系统提供了完整的数据管理方案，确保数据的完整性、一致性、性能和可用性。

# 数据库设计文档

## 1. 数据库概述

### 1.1 数据库基本信息
- **数据库类型**: MySQL 8.4+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_0900_ai_ci
- **存储引擎**: InnoDB
- **时区**: UTC

### 1.2 命名规范
- 表名：小写字母+下划线，复数形式（如：users, projects）
- 字段名：小写字母+下划线（如：user_id, created_at）
- 索引名：idx_表名_字段名（如：idx_users_username）
- 外键名：fk_表名_字段名（如：fk_releases_project_id）

## 2. 数据表设计

### 2.1 用户表 (users)

```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    role ENUM('admin', 'developer') NOT NULL COMMENT '用户角色',
    status ENUM('active', 'disabled') DEFAULT 'active' COMMENT '用户状态',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

**字段说明**:
- `id`: 主键，自增
- `username`: 用户名，唯一，用于登录
- `password`: 密码，使用BCrypt加密
- `email`: 邮箱，可选，用于找回密码
- `role`: 角色，admin（超级管理员）或developer（开发者）
- `status`: 状态，active（活跃）或disabled（禁用）

### 2.2 项目表 (projects)

```sql
CREATE TABLE projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    category ENUM('firmware', 'app') NOT NULL COMMENT '项目分类',
    status ENUM('active', 'paused', 'deprecated') DEFAULT 'active' COMMENT '项目状态',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    total_downloads BIGINT DEFAULT 0 COMMENT '总下载次数',
    latest_version VARCHAR(20) COMMENT '最新版本号',
    latest_release_at TIMESTAMP NULL COMMENT '最新发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_projects_name (name),
    INDEX idx_projects_category (category),
    INDEX idx_projects_status (status),
    INDEX idx_projects_created_by (created_by),
    
    FOREIGN KEY fk_projects_created_by (created_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目表';
```

**字段说明**:
- `category`: firmware（固件）或app（应用程序）
- `status`: active（活跃）、paused（暂停）、deprecated（已废弃）
- `total_downloads`: 项目总下载次数，通过触发器或定时任务更新
- `latest_version`: 最新版本号，冗余字段便于查询

### 2.3 用户项目权限表 (user_project_permissions)

```sql
CREATE TABLE user_project_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    permission_type ENUM('readonly', 'full') DEFAULT 'full' COMMENT '权限类型',
    granted_by BIGINT NOT NULL COMMENT '权限授予者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_project (user_id, project_id),
    INDEX idx_permissions_user_id (user_id),
    INDEX idx_permissions_project_id (project_id),
    
    FOREIGN KEY fk_permissions_user_id (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY fk_permissions_project_id (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY fk_permissions_granted_by (granted_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户项目权限表';
```

**字段说明**:
- `permission_type`: readonly（只读）或full（完整权限）
- `granted_by`: 权限授予者，通常是admin
- 唯一约束：一个用户对一个项目只能有一个权限记录

### 2.4 版本发布表 (software_releases)

```sql
CREATE TABLE software_releases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '发布ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    version_type ENUM('开发版', '稳定版', '发布版本', '测试版本') NOT NULL COMMENT '版本类型',
    title VARCHAR(200) NOT NULL COMMENT '版本标题',
    description TEXT COMMENT '版本描述',
    device_type VARCHAR(50) NOT NULL COMMENT '设备类型',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_hash VARCHAR(128) NOT NULL COMMENT '文件哈希值',
    download_count BIGINT DEFAULT 0 COMMENT '下载次数',
    status ENUM('draft', 'published', 'deprecated') DEFAULT 'published' COMMENT '发布状态',
    publisher_id BIGINT NOT NULL COMMENT '发布者ID',
    compatibility TEXT COMMENT '兼容性说明',
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_project_version (project_id, version),
    INDEX idx_releases_project_id (project_id),
    INDEX idx_releases_version (version),
    INDEX idx_releases_version_type (version_type),
    INDEX idx_releases_device_type (device_type),
    INDEX idx_releases_publisher_id (publisher_id),
    INDEX idx_releases_published_at (published_at),
    INDEX idx_releases_status (status),
    
    FOREIGN KEY fk_releases_project_id (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY fk_releases_publisher_id (publisher_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='版本发布表';
```

**字段说明**:
- `version`: 版本号，格式为v1.0.0.0
- `version_type`: 版本类型，四种类型
- `device_type`: 设备类型，如耳机、音箱、Android、iOS等
- `file_hash`: 文件的SHA256哈希值，用于完整性校验
- `status`: draft（草稿）、published（已发布）、deprecated（已废弃）

### 2.5 下载日志表 (download_logs)

```sql
CREATE TABLE download_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    release_id BIGINT NOT NULL COMMENT '版本ID',
    user_ip VARCHAR(45) NOT NULL COMMENT '用户IP地址',
    user_agent TEXT COMMENT '用户代理',
    referer VARCHAR(500) COMMENT '来源页面',
    download_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下载开始时间',
    download_completed_at TIMESTAMP NULL COMMENT '下载完成时间',
    bytes_downloaded BIGINT DEFAULT 0 COMMENT '已下载字节数',
    status ENUM('started', 'completed', 'failed', 'cancelled') DEFAULT 'started' COMMENT '下载状态',
    
    INDEX idx_download_logs_release_id (release_id),
    INDEX idx_download_logs_user_ip (user_ip),
    INDEX idx_download_logs_started_at (download_started_at),
    INDEX idx_download_logs_status (status),
    
    FOREIGN KEY fk_download_logs_release_id (release_id) REFERENCES software_releases(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='下载日志表';
```

**字段说明**:
- `user_ip`: 用户IP地址，IPv4或IPv6
- `bytes_downloaded`: 已下载字节数，用于断点续传
- `status`: 下载状态，跟踪下载过程

### 2.6 操作日志表 (operation_logs)

```sql
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NULL COMMENT '操作用户ID',
    username VARCHAR(50) COMMENT '操作用户名',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id BIGINT COMMENT '目标ID',
    target_name VARCHAR(200) COMMENT '目标名称',
    details TEXT COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    result ENUM('success', 'failed') DEFAULT 'success' COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_operation_logs_user_id (user_id),
    INDEX idx_operation_logs_action (action),
    INDEX idx_operation_logs_target_type (target_type),
    INDEX idx_operation_logs_target_id (target_id),
    INDEX idx_operation_logs_created_at (created_at),
    INDEX idx_operation_logs_result (result),
    
    FOREIGN KEY fk_operation_logs_user_id (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

**字段说明**:
- `action`: 操作类型，如login、create_project、upload_file等
- `target_type`: 目标类型，如project、release、user等
- `result`: 操作结果，成功或失败

## 3. 视图定义

### 3.1 项目统计视图

```sql
CREATE VIEW project_statistics AS
SELECT 
    p.id,
    p.name,
    p.category,
    p.status,
    COUNT(r.id) as release_count,
    COALESCE(SUM(r.download_count), 0) as total_downloads,
    MAX(r.published_at) as latest_release_at,
    p.created_at
FROM projects p
LEFT JOIN software_releases r ON p.id = r.project_id AND r.status = 'published'
GROUP BY p.id, p.name, p.category, p.status, p.created_at;
```

### 3.2 用户项目权限视图

```sql
CREATE VIEW user_project_access AS
SELECT 
    u.id as user_id,
    u.username,
    u.role,
    p.id as project_id,
    p.name as project_name,
    p.category,
    COALESCE(upp.permission_type, 'none') as permission_type
FROM users u
CROSS JOIN projects p
LEFT JOIN user_project_permissions upp ON u.id = upp.user_id AND p.id = upp.project_id
WHERE u.role = 'developer' AND p.status = 'active';
```

## 4. 索引优化策略

### 4.1 主要查询场景分析

1. **公开访问查询**:
   - 项目列表按分类筛选
   - 版本列表按项目ID查询
   - 下载统计查询

2. **开发者查询**:
   - 用户权限验证
   - 项目版本管理
   - 操作日志查询

3. **Admin查询**:
   - 用户管理
   - 权限分配
   - 系统统计

### 4.2 复合索引设计

```sql
-- 项目列表查询优化
CREATE INDEX idx_projects_category_status ON projects(category, status, updated_at);

-- 版本发布查询优化  
CREATE INDEX idx_releases_project_status_published ON software_releases(project_id, status, published_at DESC);

-- 下载日志统计优化
CREATE INDEX idx_download_logs_release_date ON download_logs(release_id, download_started_at);

-- 操作日志查询优化
CREATE INDEX idx_operation_logs_user_date ON operation_logs(user_id, created_at DESC);

-- 搜索功能优化索引
CREATE INDEX idx_projects_search ON projects(name, description, category, status);
CREATE INDEX idx_releases_search ON software_releases(title, description, version, device_type, version_type, status);
CREATE INDEX idx_releases_project_device ON software_releases(project_id, device_type, version_type);
CREATE INDEX idx_releases_published_download ON software_releases(published_at DESC, download_count DESC);

-- 批量操作优化索引
CREATE INDEX idx_releases_ids ON software_releases(id, project_id, status);
CREATE INDEX idx_permissions_user_project ON user_project_permissions(user_id, project_id);
```

### 4.3 全文搜索索引

```sql
-- 为搜索功能创建全文索引
ALTER TABLE projects ADD FULLTEXT(name, description);
ALTER TABLE software_releases ADD FULLTEXT(title, description);

-- 使用全文搜索的查询示例
-- 项目搜索
SELECT *, MATCH(name, description) AGAINST('蓝牙耳机' IN BOOLEAN MODE) as relevance_score
FROM projects 
WHERE MATCH(name, description) AGAINST('蓝牙耳机' IN BOOLEAN MODE)
ORDER BY relevance_score DESC;

-- 版本搜索
SELECT r.*, p.name as project_name,
       MATCH(r.title, r.description) AGAINST('蓝牙连接' IN BOOLEAN MODE) as relevance_score
FROM software_releases r
JOIN projects p ON r.project_id = p.id
WHERE MATCH(r.title, r.description) AGAINST('蓝牙连接' IN BOOLEAN MODE)
ORDER BY relevance_score DESC;
```

## 5. 数据完整性约束

### 5.1 检查约束

```sql
-- 版本号格式检查
ALTER TABLE software_releases 
ADD CONSTRAINT chk_version_format 
CHECK (version REGEXP '^v[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$');

-- 文件大小检查（200MB限制）
ALTER TABLE software_releases 
ADD CONSTRAINT chk_file_size 
CHECK (file_size > 0 AND file_size <= 209715200);

-- 邮箱格式检查
ALTER TABLE users 
ADD CONSTRAINT chk_email_format 
CHECK (email IS NULL OR email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
```

### 5.2 触发器

```sql
-- 自动更新项目的最新版本信息
DELIMITER //
CREATE TRIGGER tr_update_project_latest_version
AFTER INSERT ON software_releases
FOR EACH ROW
BEGIN
    UPDATE projects 
    SET latest_version = NEW.version,
        latest_release_at = NEW.published_at,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.project_id;
END//

-- 自动更新下载计数
CREATE TRIGGER tr_update_download_count
AFTER UPDATE ON download_logs
FOR EACH ROW
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE software_releases 
        SET download_count = download_count + 1
        WHERE id = NEW.release_id;
        
        UPDATE projects 
        SET total_downloads = total_downloads + 1
        WHERE id = (SELECT project_id FROM software_releases WHERE id = NEW.release_id);
    END IF;
END//
DELIMITER ;
```

## 6. 分区策略

### 6.1 日志表分区（按时间）

```sql
-- 下载日志表按月分区
ALTER TABLE download_logs PARTITION BY RANGE (YEAR(download_started_at) * 100 + MONTH(download_started_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 操作日志表按月分区
ALTER TABLE operation_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 7. 备份策略

### 7.1 备份计划
- **全量备份**: 每天凌晨2:00执行
- **增量备份**: 每小时执行一次
- **日志备份**: 每15分钟执行一次
- **备份保留**: 保留30天的备份文件

### 7.2 恢复策略
- **RTO**: 恢复时间目标 < 1小时
- **RPO**: 恢复点目标 < 15分钟
- **测试频率**: 每月进行一次恢复测试

## 8. 监控与维护

### 8.1 性能监控指标
- 慢查询监控（>1秒）
- 表空间使用率监控
- 索引效率监控
- 连接数监控

### 8.2 维护任务
- 定期分析表统计信息
- 清理过期的日志数据
- 碎片整理
- 索引重建

## 9. 初始化数据

### 9.1 Admin账号初始化

```sql
-- 插入默认admin账号（密码需要在应用中加密）
INSERT INTO users (username, password, email, role, status) 
VALUES ('admin', '$2a$10$...', '<EMAIL>', 'admin', 'active');
```

### 9.2 系统配置数据

```sql
-- 创建示例项目
INSERT INTO projects (name, description, category, created_by) 
VALUES 
('智能耳机固件', '蓝牙5.0智能耳机固件项目', 'firmware', 1),
('手机APP', 'Android/iOS移动应用', 'app', 1);
```

这个数据库设计考虑了系统的所有功能需求，包括用户权限管理、项目版本控制、文件管理和日志记录，同时优化了查询性能和数据完整性。

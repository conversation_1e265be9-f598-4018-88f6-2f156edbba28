import { request } from '@/api/http'
import type { ReleaseItem, PageResult, ReleaseDetail } from '@/types/domain'

export interface FetchReleasesParams {
  page?: number
  pageSize?: number
  projectId?: number
  status?: string
  artifact?: string
  sortBy?: 'createdAt' | 'version'
  order?: 'asc' | 'desc'
}

export function fetchReleases(params: FetchReleasesParams) {
  return request<PageResult<ReleaseItem>>({
    url: '/releases',
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['releases:list'] },
  })
}

export function fetchProjectReleases(projectId: number, params: Omit<FetchReleasesParams, 'projectId'> = {}) {
  return request<PageResult<ReleaseItem>>({
    url: `/projects/${projectId}/releases`,
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: [`project:${projectId}`, 'releases:list'] },
  })
}

export function createRelease(data: { projectId: number; version: string; changelog?: string }) {
  return request<ReleaseItem>({ url: '/releases', method: 'POST', data })
}

export function fetchReleaseDetail(id: number) {
  return request<ReleaseDetail>({
    url: `/releases/${id}`,
    method: 'GET',
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: [`release:${id}`] },
  })
}



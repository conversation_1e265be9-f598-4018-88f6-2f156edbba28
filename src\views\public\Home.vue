<template>
  <div class="page page-home">
    <!-- Hero -->
    <section class="hero section">
      <div class="container-xl">
        <div class="hero-panel hero-gradient">
          <div class="hero-inner">
            <div class="hero-content">
              <div class="hero-badge">Enterprise Release Platform</div>
              <h1 class="hero-title text-gradient">软件发布管理系统</h1>
              <p class="hero-subtitle">安全、高效的企业级软件版本管理和发布平台</p>
              <p class="hero-description">
                为开发团队提供专业的软件版本控制、发布管理和下载服务，支持固件、APP等多种软件类型的统一管理。
              </p>
              <div class="hero-actions">
                <RouterLink to="/projects" class="btn btn-primary btn-lg">浏览项目</RouterLink>
                <RouterLink to="/search" class="btn btn-outline btn-lg">搜索软件</RouterLink>
              </div>
              <div class="hero-stats">
                <div class="stat-card glass">
                  <span class="stat-number">{{ systemStats?.totalProjects || 0 }}+</span>
                  <span class="stat-label">活跃项目</span>
                </div>
                <div class="stat-card glass">
                  <span class="stat-number">{{ systemStats?.totalReleases || 0 }}+</span>
                  <span class="stat-label">版本发布</span>
                </div>
                
              </div>
            </div>
            <div class="hero-visual">
              <div class="orb orb-1"></div>
              <div class="orb orb-2"></div>
              <div class="mesh"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    

    <!-- Recent Releases -->
    <section class="section recent-releases-section">
      <div class="container-xl">
        <div class="releases-panel">
          <div class="section-header section-header-row">
            <h2 class="section-title">最新发布</h2>
            <RouterLink to="/projects" class="section-link">查看全部</RouterLink>
          </div>
          <div class="releases-grid" v-if="releasesStore.list.length > 0">
            <ReleaseCard v-for="release in releasesStore.list" :key="release.id" :item="release" />
          </div>
          <div class="releases-grid" v-else>
            <div class="release-card card skeleton" v-for="i in 6" :key="i">
              <div class="release-header">
                <div class="skeleton-title"></div>
                <div class="skeleton-badge"></div>
              </div>
              <div class="skeleton-text"></div>
              <div class="release-meta">
                <div class="skeleton-chip"></div>
                <div class="skeleton-chip"></div>
              </div>
              <div class="release-actions">
                <div class="skeleton-btn"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>   
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { RouterLink } from 'vue-router'
import { useReleasesStore } from '@/stores/releases'
import { getDataService } from '@/data'
import ReleaseCard from '@/components/business/ReleaseCard.vue'
import type { SystemStats } from '@/data'

const releasesStore = useReleasesStore()
const dataService = getDataService()
const systemStats = ref<SystemStats | null>(null)

onMounted(async () => {
  await Promise.all([
    releasesStore.loadList({ page: 1, pageSize: 6, sortBy: 'createdAt', order: 'desc' }),
    loadSystemStats()
  ])
})

// 加载系统统计数据
async function loadSystemStats() {
  try {
    systemStats.value = await dataService.getSystemStats()
  } catch (error) {
    console.error('加载系统统计失败:', error)
  }
}
</script>

<style scoped>
.page { padding: 0; }

/* Hero */
.hero-panel { position: relative; overflow: hidden; border-radius: var(--radius-lg); }
.hero-gradient { background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--bg-1) 100%); }
.hero-inner { display: grid; grid-template-columns: 1.2fr 0.8fr; align-items: center; gap: var(--spacing-10); padding: var(--spacing-12) var(--spacing-10); }

.hero-content { max-width: 720px; }

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: var(--radius-full);
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  color: var(--color-text-3);
  font-size: var(--font-size-xs);
  margin-bottom: var(--spacing-3);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin: 0 0 var(--spacing-3) 0;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-3) 0;
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-2);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-6) 0;
}

.hero-actions { display: flex; flex-wrap: wrap; gap: var(--spacing-3); margin-bottom: var(--spacing-6); }

.hero-stats { display: flex; gap: var(--spacing-4); flex-wrap: wrap; }

.stat-card { display: flex; flex-direction: column; align-items: center; min-width: 140px; padding: var(--spacing-4) var(--spacing-5); border-radius: var(--radius-lg); }

.stat-number { font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--color-primary); line-height: 1; }
.stat-label { font-size: var(--font-size-sm); color: var(--color-text-3); margin-top: 6px; }

/* Hero visual */
.hero-visual { position: relative; height: 100%; min-height: 280px; }

.orb { position: absolute; border-radius: 50%; filter: blur(8px); opacity: 0.6; }
.orb-1 { width: 240px; height: 240px; background: #a5b4fc; top: 10%; right: 10%; }
.orb-2 { width: 160px; height: 160px; background: #93c5fd; bottom: 10%; right: 25%; }
.mesh { position: absolute; inset: 10% 0 0 10%; right: 0; border-radius: var(--radius-xl); background: radial-gradient(120px 80px at 40% 40%, rgba(255,255,255,0.8), rgba(255,255,255,0)); }

/* Features (removed) */

/* Recent releases */
.recent-releases-section { background: transparent; }
.releases-panel { background: var(--bg-2); border-radius: var(--radius-lg); padding: var(--spacing-10); }
.section-header-row { display: flex; align-items: center; justify-content: space-between; }
.section-link { color: var(--color-primary); text-decoration: none; font-weight: var(--font-weight-medium); }
.section-link:hover { text-decoration: underline; }

.releases-grid { 
  display: grid; 
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
  gap: var(--spacing-4); 
}

/* Skeleton */
.skeleton { position: relative; overflow: hidden; }
.skeleton::after { content: ''; position: absolute; inset: 0; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent); transform: translateX(-100%); animation: shimmer 1.2s infinite; }
@keyframes shimmer { 100% { transform: translateX(100%); } }
.skeleton-title { width: 40%; height: 20px; background: var(--bg-3); border-radius: var(--radius-sm); }
.skeleton-badge { width: 80px; height: 18px; background: var(--bg-3); border-radius: var(--radius-sm); }
.skeleton-text { width: 100%; height: 16px; background: var(--bg-3); border-radius: var(--radius-sm); margin: var(--spacing-3) 0; }
.skeleton-chip { width: 72px; height: 18px; background: var(--bg-3); border-radius: var(--radius-sm); }
.skeleton-btn { width: 96px; height: 28px; background: var(--bg-3); border-radius: var(--radius-sm); }

/* CTA */
.cta-section { background: transparent; }
.cta-panel { background: var(--gradient-brand); color: #fff; text-align: center; border-radius: var(--radius-lg); padding: var(--spacing-12) var(--spacing-10); }
.cta-content { max-width: 720px; margin: 0 auto; }
.cta-title { font-size: var(--font-size-2xl); font-weight: var(--font-weight-bold); margin: 0 0 var(--spacing-3) 0; }
.cta-description { font-size: var(--font-size-lg); opacity: 0.9; margin: 0 0 var(--spacing-6) 0; }
.cta-actions { display: flex; justify-content: center; gap: var(--spacing-3); flex-wrap: wrap; }

/* Responsive */
@media (max-width: 1024px) {
  .hero-inner { grid-template-columns: 1fr; gap: var(--spacing-8); }
  .hero-visual { min-height: 200px; }
}

@media (max-width: 768px) {
  .hero-title { font-size: var(--font-size-3xl); }
  .hero-subtitle { font-size: var(--font-size-lg); }
  .hero-description { font-size: var(--font-size-md); }
}
</style>



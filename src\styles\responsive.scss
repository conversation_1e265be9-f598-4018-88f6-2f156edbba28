/* Modern Responsive Design System 2024 */

:root {
  /* Breakpoint system - Mobile first approach */
  --breakpoint-xs: 475px;   /* Large phones */
  --breakpoint-sm: 640px;   /* Small tablets */
  --breakpoint-md: 768px;   /* Tablets */
  --breakpoint-lg: 1024px;  /* Laptops */
  --breakpoint-xl: 1280px;  /* Desktops */
  --breakpoint-2xl: 1536px; /* Large screens */
  
  /* Container breakpoints */
  --container-breakpoint-sm: 100%;
  --container-breakpoint-md: 100%;
  --container-breakpoint-lg: 100%;
  --container-breakpoint-xl: var(--container-xl);
  --container-breakpoint-2xl: var(--container-2xl);
}

/* Modern Responsive Mixins */
@mixin mobile-only {
  @media (max-width: 639px) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: 640px) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: 640px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: 1024px) and (max-width: 1279px) {
    @content;
  }
}

@mixin large-up {
  @media (min-width: 1280px) {
    @content;
  }
}

/* Standard breakpoint mixins */
@mixin xs-only {
  @media (max-width: 474px) {
    @content;
  }
}

@mixin sm-up {
  @media (min-width: 640px) {
    @content;
  }
}

@mixin sm-only {
  @media (min-width: 640px) and (max-width: 767px) {
    @content;
  }
}

@mixin md-up {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin md-only {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin lg-up {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin lg-only {
  @media (min-width: 1024px) and (max-width: 1279px) {
    @content;
  }
}

@mixin xl-up {
  @media (min-width: 1280px) {
    @content;
  }
}

@mixin xl-only {
  @media (min-width: 1280px) and (max-width: 1535px) {
    @content;
  }
}

@mixin xxl-up {
  @media (min-width: 1536px) {
    @content;
  }
}

/* 响应式工具类 */

/* 显示/隐藏 */
.hidden { display: none !important; }
.visible { display: block !important; }

/* 移动端显示/隐藏 */
@media (max-width: 767px) {
  .hidden-mobile { display: none !important; }
  .visible-mobile { display: block !important; }
}

/* 平板显示/隐藏 */
@media (min-width: 768px) and (max-width: 1023px) {
  .hidden-tablet { display: none !important; }
  .visible-tablet { display: block !important; }
}

/* 桌面端显示/隐藏 */
@media (min-width: 1024px) {
  .hidden-desktop { display: none !important; }
  .visible-desktop { display: block !important; }
}

/* 响应式文字对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

@media (max-width: 767px) {
  .text-left-mobile { text-align: left !important; }
  .text-center-mobile { text-align: center !important; }
  .text-right-mobile { text-align: right !important; }
}

@media (min-width: 768px) {
  .text-left-desktop { text-align: left !important; }
  .text-center-desktop { text-align: center !important; }
  .text-right-desktop { text-align: right !important; }
}

/* 响应式间距 */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }

/* 响应式宽度 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

@media (max-width: 767px) {
  .w-full-mobile { width: 100% !important; }
  .w-auto-mobile { width: auto !important; }
}

@media (min-width: 768px) {
  .w-full-desktop { width: 100% !important; }
  .w-auto-desktop { width: auto !important; }
}

/* Flexbox 工具类 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Grid 工具类 */
.grid { display: grid; }
.inline-grid { display: inline-grid; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* 响应式 Grid 列数 */
@media (min-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .sm\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@media (min-width: 1200px) {
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

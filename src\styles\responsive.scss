/* Modern Responsive Design System 2024 - Enhanced Multi-Screen Support */

:root {
  /* Enhanced Breakpoint System - Mobile First with Micro-breakpoints */
  --breakpoint-2xs: 320px;   /* Small phones (iPhone SE) */
  --breakpoint-xs: 375px;    /* Standard phones (iPhone 12) */
  --breakpoint-sm: 640px;    /* Large phones / Small tablets */
  --breakpoint-md: 768px;    /* Tablets (iPad) */
  --breakpoint-lg: 1024px;   /* Small laptops / Large tablets */
  --breakpoint-xl: 1280px;   /* Laptops / Small desktops */
  --breakpoint-2xl: 1440px;  /* Large desktops */
  --breakpoint-3xl: 1920px;  /* Ultra-wide screens */
  --breakpoint-4xl: 2560px;  /* 4K displays */

  /* Container System - Fluid with max-widths */
  --container-2xs: 100%;
  --container-xs: 100%;
  --container-sm: 100%;
  --container-md: 100%;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1440px;
  --container-3xl: 1600px;
  --container-4xl: 1800px;

  /* Responsive Spacing Scale */
  --spacing-responsive-xs: clamp(0.5rem, 2vw, 1rem);
  --spacing-responsive-sm: clamp(1rem, 3vw, 1.5rem);
  --spacing-responsive-md: clamp(1.5rem, 4vw, 2.5rem);
  --spacing-responsive-lg: clamp(2rem, 6vw, 4rem);
  --spacing-responsive-xl: clamp(3rem, 8vw, 6rem);

  /* Responsive Typography Scale */
  --font-size-responsive-xs: clamp(0.75rem, 2.5vw, 0.875rem);
  --font-size-responsive-sm: clamp(0.875rem, 3vw, 1rem);
  --font-size-responsive-base: clamp(1rem, 3.5vw, 1.125rem);
  --font-size-responsive-lg: clamp(1.125rem, 4vw, 1.25rem);
  --font-size-responsive-xl: clamp(1.25rem, 5vw, 1.5rem);
  --font-size-responsive-2xl: clamp(1.5rem, 6vw, 2rem);
  --font-size-responsive-3xl: clamp(2rem, 8vw, 3rem);
}

/* Modern Responsive Mixins with Enhanced Support */
@mixin mobile-2xs {
  @media (max-width: 374px) {
    @content;
  }
}

@mixin mobile-xs {
  @media (max-width: 639px) {
    @content;
  }
}

@mixin tablet-sm {
  @media (min-width: 640px) {
    @content;
  }
}

@mixin tablet-md {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin laptop-sm {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin desktop-sm {
  @media (min-width: 1280px) {
    @content;
  }
}

@mixin desktop-lg {
  @media (min-width: 1440px) {
    @content;
  }
}

@mixin ultrawide {
  @media (min-width: 1920px) {
    @content;
  }
}

@mixin retina-4k {
  @media (min-width: 2560px) {
    @content;
  }
}

/* Modern Grid System - CSS Grid + Flexbox Hybrid */
@mixin grid-responsive($columns: 12, $gap: var(--spacing-4)) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;

  @include mobile-xs {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  @include tablet-sm {
    grid-template-columns: repeat(2, 1fr);
  }

  @include tablet-md {
    grid-template-columns: repeat(3, 1fr);
  }

  @include laptop-sm {
    grid-template-columns: repeat($columns, 1fr);
  }
}

/* Responsive Container Queries Support */
@container (min-width: 320px) {
  .container-query-sm {
    padding: var(--spacing-2);
  }
}

@container (min-width: 640px) {
  .container-query-md {
    padding: var(--spacing-4);
  }
}

@container (min-width: 1024px) {
  .container-query-lg {
    padding: var(--spacing-6);
  }
}

/* Modern Aspect Ratio Support */
@mixin aspect-ratio($ratio: 16/9) {
  aspect-ratio: $ratio;

  @supports not (aspect-ratio: 1) {
    position: relative;

    &::before {
      content: '';
      display: block;
      padding-bottom: calc(100% / ($ratio));
    }

    > * {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

/* Responsive Utilities */
.responsive-grid {
  @include grid-responsive();
}

.responsive-grid-2 {
  @include grid-responsive(2);
}

.responsive-grid-3 {
  @include grid-responsive(3);
}

.responsive-grid-4 {
  @include grid-responsive(4);
}

.responsive-grid-6 {
  @include grid-responsive(6);
}

/* Responsive Visibility */
.hidden-mobile {
  @include mobile-xs {
    display: none !important;
  }
}

.hidden-tablet {
  @include tablet-sm {
    display: none !important;
  }
}

.hidden-desktop {
  @include desktop-sm {
    display: none !important;
  }
}

.visible-mobile {
  display: none !important;

  @include mobile-xs {
    display: block !important;
  }
}

.visible-tablet {
  display: none !important;

  @include tablet-sm {
    display: block !important;
  }
}

.visible-desktop {
  display: none !important;

  @include desktop-sm {
    display: block !important;
  }
}

/* Responsive Text Alignment */
.text-center-mobile {
  @include mobile-xs {
    text-align: center !important;
  }
}

.text-left-desktop {
  @include desktop-sm {
    text-align: left !important;
  }
}

/* Modern Scroll Behavior */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-snap-x {
  scroll-snap-type: x mandatory;
  overflow-x: auto;

  > * {
    scroll-snap-align: start;
  }
}

.scroll-snap-y {
  scroll-snap-type: y mandatory;
  overflow-y: auto;

  > * {
    scroll-snap-align: start;
  }
}

@mixin tablet-only {
  @media (min-width: 640px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: 1024px) and (max-width: 1279px) {
    @content;
  }
}

@mixin large-up {
  @media (min-width: 1280px) {
    @content;
  }
}

/* Standard breakpoint mixins */
@mixin xs-only {
  @media (max-width: 474px) {
    @content;
  }
}

@mixin sm-up {
  @media (min-width: 640px) {
    @content;
  }
}

@mixin sm-only {
  @media (min-width: 640px) and (max-width: 767px) {
    @content;
  }
}

@mixin md-up {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin md-only {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin lg-up {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin lg-only {
  @media (min-width: 1024px) and (max-width: 1279px) {
    @content;
  }
}

@mixin xl-up {
  @media (min-width: 1280px) {
    @content;
  }
}

@mixin xl-only {
  @media (min-width: 1280px) and (max-width: 1535px) {
    @content;
  }
}

@mixin xxl-up {
  @media (min-width: 1536px) {
    @content;
  }
}

/* 响应式工具类 */

/* 显示/隐藏 */
.hidden { display: none !important; }
.visible { display: block !important; }

/* 移动端显示/隐藏 */
@media (max-width: 767px) {
  .hidden-mobile { display: none !important; }
  .visible-mobile { display: block !important; }
}

/* 平板显示/隐藏 */
@media (min-width: 768px) and (max-width: 1023px) {
  .hidden-tablet { display: none !important; }
  .visible-tablet { display: block !important; }
}

/* 桌面端显示/隐藏 */
@media (min-width: 1024px) {
  .hidden-desktop { display: none !important; }
  .visible-desktop { display: block !important; }
}

/* 响应式文字对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

@media (max-width: 767px) {
  .text-left-mobile { text-align: left !important; }
  .text-center-mobile { text-align: center !important; }
  .text-right-mobile { text-align: right !important; }
}

@media (min-width: 768px) {
  .text-left-desktop { text-align: left !important; }
  .text-center-desktop { text-align: center !important; }
  .text-right-desktop { text-align: right !important; }
}

/* 响应式间距 */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }

/* 响应式宽度 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

@media (max-width: 767px) {
  .w-full-mobile { width: 100% !important; }
  .w-auto-mobile { width: auto !important; }
}

@media (min-width: 768px) {
  .w-full-desktop { width: 100% !important; }
  .w-auto-desktop { width: auto !important; }
}

/* Flexbox 工具类 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Grid 工具类 */
.grid { display: grid; }
.inline-grid { display: inline-grid; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* 响应式 Grid 列数 */
@media (min-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .sm\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@media (min-width: 1200px) {
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

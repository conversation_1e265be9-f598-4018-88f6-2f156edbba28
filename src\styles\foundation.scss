/* Modern UI Foundation Components 2024 */

/* Container System - Responsive containers */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }  
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

/* Responsive container padding */
@media (min-width: 640px) {
  .container { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
}
@media (min-width: 1024px) {
  .container { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }
}

/* Section System - Modern spacing and typography */
.section {
  padding-top: var(--spacing-16);
  padding-bottom: var(--spacing-16);
}

.section-sm { padding-top: var(--spacing-8); padding-bottom: var(--spacing-8); }
.section-md { padding-top: var(--spacing-12); padding-bottom: var(--spacing-12); }
.section-lg { padding-top: var(--spacing-20); padding-bottom: var(--spacing-20); }
.section-xl { padding-top: var(--spacing-24); padding-bottom: var(--spacing-24); }

.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-12);
  text-align: center;
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  font-family: var(--font-family-display);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.section-subtitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  margin-bottom: var(--spacing-2);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  max-width: 640px;
}

/* Responsive section spacing */
@media (max-width: 768px) {
  .section { padding-top: var(--spacing-12); padding-bottom: var(--spacing-12); }
  .section-lg { padding-top: var(--spacing-16); padding-bottom: var(--spacing-16); }
  .section-xl { padding-top: var(--spacing-20); padding-bottom: var(--spacing-20); }
  .section-header { margin-bottom: var(--spacing-8); }
  .section-title { font-size: var(--font-size-2xl); }
}

/* Button System - Modern iOS/Material Design inspired */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-xl);
  border: 1px solid transparent;
  font-family: var(--font-family-text);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  transition: var(--transition-all);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  
  /* Focus states */
  &:focus-visible {
    outline: none;
    box-shadow: var(--focus-ring);
  }
  
  /* Active state */
  &:active {
    transform: scale(0.98);
  }
  
  /* Disabled state */
  &:disabled,
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

/* Button Sizes */
.btn-xs { 
  padding: var(--spacing-1_5) var(--spacing-3); 
  font-size: var(--font-size-sm);
  border-radius: var(--radius-lg);
}
.btn-sm { 
  padding: var(--spacing-2) var(--spacing-4); 
  font-size: var(--font-size-sm);
  border-radius: var(--radius-lg);
}
.btn-md { 
  padding: var(--spacing-3) var(--spacing-6); 
  font-size: var(--font-size-base);
  border-radius: var(--radius-xl);
}
.btn-lg { 
  padding: var(--spacing-4) var(--spacing-8); 
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}
.btn-xl { 
  padding: var(--spacing-5) var(--spacing-10); 
  font-size: var(--font-size-xl);
  border-radius: var(--radius-2xl);
}

/* Primary Button - Gradient background */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  border-color: var(--color-primary-600);
  box-shadow: var(--shadow-sm);
}
.btn-primary:hover {
  background: var(--color-primary-600);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}
.btn-primary:active {
  transform: translateY(0) scale(0.98);
}

/* Secondary Button - Subtle background */
.btn-secondary {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-xs);
}
.btn-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

/* Outline Button - Border focused */
.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary-300);
}
.btn-outline:hover {
  background: var(--color-primary-50);
  border-color: var(--color-primary-400);
  color: var(--color-primary-600);
}

/* Ghost Button - Minimal style */
.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border-color: transparent;
}
.btn-ghost:hover {
  background: var(--bg-tertiary);
  color: var(--color-text-primary);
}

/* Semantic Button Variants */
.btn-success {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success-600);
}
.btn-success:hover {
  background: var(--color-success-600);
}

.btn-warning {
  background: var(--color-warning);
  color: white;
  border-color: var(--color-warning-600);
}
.btn-warning:hover {
  background: var(--color-warning-600);
}

.btn-danger {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger-600);
}
.btn-danger:hover {
  background: var(--color-danger-600);
}
.btn-danger:focus-visible {
  box-shadow: var(--focus-ring-danger);
}

/* Button Groups */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}
.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}
.btn-group .btn:first-child { border-radius: var(--radius-xl) 0 0 var(--radius-xl); }
.btn-group .btn:last-child { border-radius: 0 var(--radius-xl) var(--radius-xl) 0; border-right-width: 1px; }

/* Icon Buttons */
.btn-icon {
  width: 44px;
  height: 44px;
  padding: 0;
  border-radius: var(--radius-full);
}
.btn-icon.btn-sm { width: 36px; height: 36px; }
.btn-icon.btn-lg { width: 52px; height: 52px; }

/* Modern Card System - Elevation based */
.surface {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
}

.card {
  position: relative;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition-all);
}

/* Card hover effects */
.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--border-secondary);
}

.card.card-interactive {
  cursor: pointer;
}

.card.card-interactive:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Card variants */
.card-flat {
  box-shadow: none;
  border: 1px solid var(--border-primary);
}

.card-elevated {
  box-shadow: var(--shadow-md);
  border: none;
}

.card-floating {
  box-shadow: var(--shadow-xl);
  border: none;
}

/* Card content structure */
.card-header {
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--border-primary);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-4) var(--spacing-6) var(--spacing-6) var(--spacing-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
  margin-top: auto;
}

/* Card without dividers */
.card-seamless .card-header,
.card-seamless .card-footer {
  border: none;
  background: transparent;
}

/* Card sizes */
.card-sm { border-radius: var(--radius-lg); }
.card-sm .card-header,
.card-sm .card-body,
.card-sm .card-footer { padding: var(--spacing-4); }

.card-lg { border-radius: var(--radius-2xl); }
.card-lg .card-header,
.card-lg .card-body,
.card-lg .card-footer { padding: var(--spacing-8); }

/* Modern Glass Effects - iOS inspired */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: var(--radius-xl);
}

.glass-strong {
  background: var(--glass-bg-strong);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow-strong);
  border-radius: var(--radius-xl);
}

/* Frosted glass variants */
.glass-subtle {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-sm);
}

.glass-card {
  @extend .glass;
  transition: var(--transition-all);
}

.glass-card:hover {
  background: var(--glass-bg-strong);
  box-shadow: var(--glass-shadow-strong);
  transform: translateY(-1px);
}

/* Accent & Decorative Elements */
.accent-top {
  position: relative;
}
.accent-top::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; 
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.accent-left {
  position: relative;
  padding-left: var(--spacing-4);
}
.accent-left::before {
  content: '';
  position: absolute;
  top: 0; left: 0; bottom: 0;
  width: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

/* Modern Form Elements */
.form-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.form-label {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.form-input,
.form-textarea,
.form-select {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1.5px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: var(--transition-colors);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--color-text-tertiary);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--color-danger);
}

.form-input.error:focus,
.form-textarea.error:focus,
.form-select.error:focus {
  box-shadow: var(--focus-ring-danger);
}

.form-help {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  line-height: var(--line-height-normal);
}

.form-error {
  font-size: var(--font-size-sm);
  color: var(--color-danger);
  line-height: var(--line-height-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* Badge System */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  white-space: nowrap;
}

.badge-sm { padding: var(--spacing-0_5) var(--spacing-2); font-size: var(--font-size-2xs); }
.badge-lg { padding: var(--spacing-2) var(--spacing-4); font-size: var(--font-size-sm); }

.badge-primary { background: var(--color-primary-100); color: var(--color-primary-700); }
.badge-success { background: var(--color-success-100); color: var(--color-success-700); }
.badge-warning { background: var(--color-warning-100); color: var(--color-warning-700); }
.badge-danger { background: var(--color-danger-100); color: var(--color-danger-700); }
.badge-gray { background: var(--color-gray-100); color: var(--color-gray-700); }

.badge-outline {
  background: transparent;
  border: 1px solid currentColor;
}

/* Avatar System */
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: var(--color-gray-200);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-semibold);
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-xs { width: 24px; height: 24px; font-size: var(--font-size-2xs); }
.avatar-sm { width: 32px; height: 32px; font-size: var(--font-size-xs); }
.avatar-md { width: 40px; height: 40px; font-size: var(--font-size-sm); }
.avatar-lg { width: 48px; height: 48px; font-size: var(--font-size-base); }
.avatar-xl { width: 64px; height: 64px; font-size: var(--font-size-lg); }
.avatar-2xl { width: 80px; height: 80px; font-size: var(--font-size-xl); }

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-group {
  display: flex;
  align-items: center;
}
.avatar-group .avatar {
  margin-left: -var(--spacing-2);
  border: 2px solid var(--bg-primary);
}
.avatar-group .avatar:first-child {
  margin-left: 0;
}

/* Utility Helpers */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: var(--font-weight-bold);
}

.bg-gradient {
  background: var(--gradient-primary);
  color: white;
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary-300);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Focus visible improvements */
*:focus-visible {
  outline: none;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .container { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
  
  .btn { padding: var(--spacing-3) var(--spacing-5); font-size: var(--font-size-sm); }
  .btn-lg { padding: var(--spacing-4) var(--spacing-6); font-size: var(--font-size-base); }
  
  .card-header, .card-body, .card-footer { padding: var(--spacing-4); }
}



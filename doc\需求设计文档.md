# 软件/固件发布管理系统 - 需求设计文档

## 1. 项目概述

### 1.1 项目名称
软件/固件发布管理系统

### 1.2 技术架构
- 前端：Vue 3 + TypeScript + Vite
- 后端：Spring Boot
- 数据库：待确定（建议MySQL/PostgreSQL）
- 架构模式：前后端分离

### 1.3 核心功能
基于现有需求文档，系统主要用于软件和固件的版本管理与发布，支持文件上传、版本控制、用户权限管理和下载服务。

## 2. 用户角色与权限

### 2.1 用户角色定义

#### Admin（超级管理员）
- **访问权限**：最高权限
- **主要功能**：
  - 管理所有项目（创建、删除项目）
  - 创建和管理开发者账号
  - 分配开发者的项目权限
  - 拥有所有项目的完整操作权限
- **账号管理**：代码中写死admin账号密码
- **访问方式**：登录后进入admin控制台

#### 开发者（项目管理员）
- **访问权限**：指定项目的管理权限
- **主要功能**：
  - 查看分配给自己的项目列表
  - 管理项目内的文件发布（增删改查）
  - 编辑项目信息和项目简介
  - 上传新版本文件
  - 查看项目相关的操作日志
- **账号管理**：由admin创建和分配权限
- **访问方式**：需要登录认证

#### 普通员工
- **访问权限**：只读访问
- **主要功能**：
  - 浏览所有项目列表
  - 查看项目详细信息
  - 下载项目内的固件/APP文件
  - 查看版本信息和更新日志
- **访问方式**：无需注册登录，直接访问

### 2.2 权限控制
- 前端路由守卫（区分admin、开发者、普通访问）
- 后端API权限验证（基于JWT Token和角色）
- 项目级权限控制（开发者只能访问分配的项目）

## 3. 功能模块设计

### 3.1 项目管理模块

#### 3.1.1 项目基本信息
- 项目名称和唯一标识
- 项目简介和描述
- 项目分类（固件类/APP类）
- 创建时间和创建者
- 项目状态（活跃/暂停/废弃）

#### 3.1.2 项目权限管理
- Admin可以创建/删除项目
- Admin分配开发者的项目访问权限
- 开发者只能管理被分配的项目
- 普通员工可以访问所有公开项目

### 3.2 文件管理模块

#### 3.2.1 文件上传功能
- 支持任意文件格式
- 文件大小限制：200MB以内
- 上传进度显示
- 文件校验（MD5/SHA256）
- 本地服务器存储

#### 3.2.2 版本管理
- 版本号格式：v1.0.0.0
- 版本类型：开发版、稳定版、发布版本、测试版本
- 版本发布后允许修改信息
- 版本比较功能
- 历史版本保留

#### 3.2.3 设备类型管理
- 大分类：固件、APP
- 具体设备类型：耳机、音箱、Android、iOS等
- 支持创建文件时手动输入设备类型

#### 3.2.4 文件信息
必须字段：
- 版本号（v1.0.0.0格式）
- 版本类型（开发版/稳定版/发布版本/测试版本）
- 发布日期
- 文件大小
- 更新说明
- 适用设备类型

可选字段：
- 发布者信息
- 下载次数
- 文件哈希值
- 兼容性说明

### 3.3 用户管理模块

#### 3.3.1 Admin功能
- 硬编码admin账号密码
- Admin控制台界面
- 创建开发者账号
- 分配项目权限给开发者
- 查看系统整体统计

#### 3.3.2 认证功能
- 开发者登录
- JWT Token管理
- 会话管理
- 权限验证

#### 3.3.3 开发者账号管理
- 开发者基本信息
- 项目权限分配
- 操作日志记录（仅开发者和admin操作）

### 3.4 下载管理模块

#### 3.4.1 下载服务
- 直接链接下载
- 断点续传支持（可选）
- 下载统计
- 无需限制下载频率和并发数

#### 3.4.2 文件服务
- 本地服务器存储
- 文件访问控制（普通员工可直接访问）
- 文件完整性校验

## 4. 数据库设计（MySQL）

### 4.1 核心表结构

#### users（用户表）
- id（主键）、username、password、email、role（admin/developer）、status、created_at、updated_at

#### projects（项目表）
- id（主键）、name、description、category（firmware/app）、status（active/paused/deprecated）、created_by、created_at、updated_at

#### user_project_permissions（用户项目权限表）
- id（主键）、user_id、project_id、permission_type、created_at

#### software_releases（发布版本表）
- id（主键）、project_id、version（v1.0.0.0）、version_type（开发版/稳定版/发布版本/测试版本）、title、description、file_path、file_size、file_hash、device_type、publisher_id、download_count、published_at、created_at、updated_at

#### operation_logs（操作日志表）
- id（主键）、user_id、action、target_type、target_id、details、ip_address、created_at

## 5. API接口设计

### 5.1 公开接口（无需认证）
- GET /api/releases - 获取发布列表
- GET /api/releases/{id} - 获取版本详情
- GET /api/download/{id} - 下载文件

### 5.2 管理接口（需要认证）
- POST /api/auth/login - 用户登录
- POST /api/releases - 创建新版本
- PUT /api/releases/{id} - 更新版本信息
- DELETE /api/releases/{id} - 删除版本
- POST /api/upload - 文件上传

## 6. 前端页面设计

### 6.1 公开页面
- 首页 - 展示最新版本和热门下载
- 版本列表页 - 分类浏览所有版本
- 版本详情页 - 显示详细信息和下载链接
- 搜索页面 - 版本搜索功能

### 6.2 管理页面
- 登录页面
- 管理员仪表板
- 版本管理页面
- 文件上传页面
- 用户管理页面（如需要）

## 7. 待确认的关键问题

### 7.1 业务逻辑相关
1. **文件存储方式**：本地存储还是云存储？预计存储容量？
2. **版本发布流程**：是否需要审核机制？版本发布后是否可以撤回？
3. **设备类型分类**：具体有哪些设备类型？是否需要层级分类？
4. **文件大小限制**：单个文件最大允许多大？
5. **用户注册**：管理员账号如何创建？是否支持多个管理员？

### 7.2 技术实现相关
6. **数据库选择**：MySQL、PostgreSQL还是其他？
7. **文件访问控制**：是否需要访问权限控制？
8. **系统监控**：是否需要下载统计、访问分析等功能？
9. **部署环境**：开发、测试、生产环境规划？
10. **安全要求**：是否需要HTTPS、文件扫描等安全措施？

### 7.3 用户体验相关
11. **界面风格**：有特定的UI设计要求吗？
12. **搜索功能**：需要支持哪些搜索维度？
13. **通知机制**：新版本发布时是否需要通知机制？
14. **移动端适配**：是否需要响应式设计？

## 8. 项目计划建议

### 第一阶段（MVP）
- 基础的版本浏览和下载功能
- 简单的管理员上传功能
- 基本的用户认证

### 第二阶段
- 完善的版本管理功能
- 用户权限细化
- 搜索和筛选功能

### 第三阶段
- 下载统计和分析
- 高级功能优化
- 性能优化和安全加固

# 组件系统设计

## 🧩 组件系统概述

### 1. 组件设计原则

#### 1.1 设计理念
- **原子化设计**: 从基础原子组件构建复杂界面
- **组合优于继承**: 通过组合小组件实现复杂功能
- **单一职责**: 每个组件只负责一个明确的功能
- **可预测性**: 相同输入产生相同输出，无副作用

#### 1.2 组件分层
```
组件系统分层架构
├── 原子组件 (Atoms)
│   ├── Button - 按钮
│   ├── Input - 输入框
│   ├── Icon - 图标
│   ├── Badge - 徽章
│   └── Avatar - 头像
├── 分子组件 (Molecules)
│   ├── SearchBox - 搜索框
│   ├── FormItem - 表单项
│   ├── DropdownMenu - 下拉菜单
│   ├── Pagination - 分页器
│   └── UserInfo - 用户信息
├── 有机体组件 (Organisms)
│   ├── Header - 页面头部
│   ├── Sidebar - 侧边栏
│   ├── ProjectCard - 项目卡片
│   ├── ReleaseList - 版本列表
│   └── UserTable - 用户表格
├── 模板组件 (Templates)
│   ├── PublicLayout - 公开页面布局
│   ├── DeveloperLayout - 开发者布局
│   ├── AdminLayout - 管理员布局
│   └── AuthLayout - 认证页面布局
└── 页面组件 (Pages)
    ├── HomePage - 首页
    ├── ProjectListPage - 项目列表
    ├── ProjectDetailPage - 项目详情
    └── DeveloperDashboard - 开发者仪表板
```

### 2. 组件规范

#### 2.1 命名规范
- **组件名称**: PascalCase，如 `ProjectCard`
- **Props名称**: camelCase，如 `showDescription`
- **事件名称**: kebab-case，如 `update:model-value`
- **插槽名称**: kebab-case，如 `header-actions`

#### 2.2 文件结构规范
```
ComponentName/
├── index.ts          # 组件导出入口
├── ComponentName.vue # 组件主文件
├── types.ts          # TypeScript类型定义
├── styles.scss       # 组件样式
├── __tests__/        # 测试文件
│   └── ComponentName.test.ts
└── README.md         # 组件文档
```

## 🔧 原子组件设计

### 1. Button 按钮组件

#### 1.1 功能需求
- 支持多种按钮类型（主要、次要、文本、链接）
- 支持多种尺寸（小、中、大）
- 支持禁用和加载状态
- 支持图标和文字组合

#### 1.2 Props 设计
```typescript
interface ButtonProps {
  // 按钮类型
  type?: 'primary' | 'secondary' | 'text' | 'link' | 'danger'
  // 按钮尺寸
  size?: 'small' | 'medium' | 'large'
  // 禁用状态
  disabled?: boolean
  // 加载状态
  loading?: boolean
  // 图标
  icon?: string
  // 图标位置
  iconPosition?: 'left' | 'right'
  // HTML类型
  htmlType?: 'button' | 'submit' | 'reset'
  // 是否为块级按钮
  block?: boolean
  // 是否为幽灵按钮（透明背景）
  ghost?: boolean
}
```

#### 1.3 使用示例
```vue
<!-- 基础按钮 -->
<Button type="primary">确认</Button>

<!-- 带图标的按钮 -->
<Button type="secondary" icon="download" icon-position="left">
  下载文件
</Button>

<!-- 加载状态按钮 -->
<Button :loading="isUploading" @click="handleUpload">
  上传文件
</Button>

<!-- 危险操作按钮 -->
<Button type="danger" @click="handleDelete">
  删除项目
</Button>
```

### 2. Input 输入框组件

#### 2.1 功能需求
- 支持多种输入类型（文本、密码、数字、搜索）
- 支持前缀和后缀图标
- 支持清除功能
- 支持实时验证

#### 2.2 Props 设计
```typescript
interface InputProps {
  // 输入值
  modelValue: string
  // 输入类型
  type?: 'text' | 'password' | 'number' | 'search' | 'email'
  // 占位符
  placeholder?: string
  // 尺寸
  size?: 'small' | 'medium' | 'large'
  // 禁用状态
  disabled?: boolean
  // 只读状态
  readonly?: boolean
  // 前缀图标
  prefixIcon?: string
  // 后缀图标
  suffixIcon?: string
  // 是否可清除
  clearable?: boolean
  // 最大长度
  maxLength?: number
  // 验证状态
  status?: 'success' | 'warning' | 'error'
  // 验证消息
  message?: string
}
```

#### 2.3 使用示例
```vue
<!-- 基础输入框 -->
<Input v-model="projectName" placeholder="请输入项目名称" />

<!-- 搜索输入框 -->
<Input
  v-model="searchKeyword"
  type="search"
  prefix-icon="search"
  placeholder="搜索项目..."
  clearable
/>

<!-- 带验证的输入框 -->
<Input
  v-model="version"
  :status="versionStatus"
  :message="versionMessage"
  placeholder="版本号，如：v1.0.0.0"
/>
```

### 3. Icon 图标组件

#### 3.1 功能需求
- 支持图标库中的所有图标
- 支持自定义尺寸和颜色
- 支持加载状态和旋转动画

#### 3.2 Props 设计
```typescript
interface IconProps {
  // 图标名称
  name: string
  // 图标尺寸
  size?: number | string
  // 图标颜色
  color?: string
  // 是否旋转
  spin?: boolean
  // 自定义类名
  className?: string
}
```

## 🔗 分子组件设计

### 1. SearchBox 搜索框组件

#### 1.1 功能需求
- 集成输入框和搜索按钮
- 支持搜索建议和历史记录
- 支持快捷键操作
- 支持高级搜索选项

#### 1.2 组件设计
```typescript
interface SearchBoxProps {
  // 搜索值
  modelValue: string
  // 占位符
  placeholder?: string
  // 搜索建议
  suggestions?: SearchSuggestion[]
  // 是否显示历史记录
  showHistory?: boolean
  // 历史记录
  history?: string[]
  // 是否显示高级搜索
  showAdvanced?: boolean
  // 加载状态
  loading?: boolean
}

interface SearchSuggestion {
  text: string
  type: 'keyword' | 'project' | 'version'
  value?: any
}
```

#### 1.3 使用示例
```vue
<SearchBox
  v-model="searchQuery"
  placeholder="搜索项目或版本..."
  :suggestions="searchSuggestions"
  :loading="isSearching"
  show-history
  show-advanced
  @search="handleSearch"
  @suggestion-click="handleSuggestionClick"
/>
```

### 2. FormItem 表单项组件

#### 2.1 功能需求
- 标签和输入控件的组合
- 支持必填标识和验证提示
- 支持帮助文本
- 响应式布局

#### 2.2 组件设计
```typescript
interface FormItemProps {
  // 标签文本
  label?: string
  // 是否必填
  required?: boolean
  // 验证状态
  status?: 'success' | 'warning' | 'error'
  // 验证消息
  message?: string
  // 帮助文本
  help?: string
  // 标签宽度
  labelWidth?: string
  // 布局方向
  layout?: 'horizontal' | 'vertical'
}
```

### 3. Pagination 分页器组件

#### 3.1 功能需求
- 支持页码跳转和页面大小设置
- 显示总数和范围信息
- 支持简洁模式
- 响应式适配

#### 3.2 组件设计
```typescript
interface PaginationProps {
  // 当前页码
  current: number
  // 每页条数
  pageSize: number
  // 总条数
  total: number
  // 页面大小选项
  pageSizeOptions?: number[]
  // 是否显示页面大小选择器
  showSizeChanger?: boolean
  // 是否显示快速跳转
  showQuickJumper?: boolean
  // 是否显示总数
  showTotal?: boolean
  // 简洁模式
  simple?: boolean
}
```

## 🏗️ 有机体组件设计

### 1. ProjectCard 项目卡片组件

#### 1.1 功能需求
- 展示项目基本信息
- 支持不同显示模式（卡片、列表）
- 支持快速操作
- 支持状态指示

#### 1.2 组件设计
```typescript
interface ProjectCardProps {
  // 项目数据
  project: Project
  // 显示模式
  mode?: 'card' | 'list'
  // 是否显示操作按钮
  showActions?: boolean
  // 可用操作
  actions?: ProjectAction[]
  // 是否可选择
  selectable?: boolean
  // 选中状态
  selected?: boolean
}

interface Project {
  id: number
  name: string
  description: string
  category: 'firmware' | 'app'
  latestVersion: string
  totalDownloads: number
  updatedAt: string
  status: 'active' | 'paused' | 'deprecated'
}

interface ProjectAction {
  key: string
  label: string
  icon?: string
  type?: 'primary' | 'secondary' | 'danger'
  visible?: boolean
  disabled?: boolean
}
```

#### 1.3 使用示例
```vue
<ProjectCard
  :project="projectData"
  mode="card"
  show-actions
  :actions="projectActions"
  @action-click="handleActionClick"
  @click="handleProjectClick"
/>
```

### 2. ReleaseList 版本列表组件

#### 2.1 功能需求
- 展示版本历史时间线
- 支持版本筛选和排序
- 支持批量操作
- 支持版本对比

#### 2.2 组件设计
```typescript
interface ReleaseListProps {
  // 版本列表数据
  releases: Release[]
  // 是否显示筛选器
  showFilters?: boolean
  // 是否支持批量操作
  batchable?: boolean
  // 是否支持版本对比
  comparable?: boolean
  // 加载状态
  loading?: boolean
  // 空状态文案
  emptyText?: string
}

interface Release {
  id: number
  version: string
  versionType: '开发版' | '稳定版' | '发布版本' | '测试版本'
  title: string
  description: string
  deviceType: string
  fileSize: string
  downloadCount: number
  publishedAt: string
  status: 'published' | 'deprecated'
}
```

### 3. UserTable 用户表格组件

#### 2.1 功能需求
- 展示用户列表和权限信息
- 支持搜索、筛选、排序
- 支持行内编辑
- 支持批量权限操作

#### 3.2 组件设计
```typescript
interface UserTableProps {
  // 用户数据
  users: User[]
  // 表格列配置
  columns?: TableColumn[]
  // 是否支持选择
  selectable?: boolean
  // 是否支持行内编辑
  editable?: boolean
  // 加载状态
  loading?: boolean
  // 分页配置
  pagination?: PaginationConfig
}

interface User {
  id: number
  username: string
  email: string
  role: 'admin' | 'developer'
  status: 'active' | 'disabled'
  projects: UserProject[]
  createdAt: string
  lastLoginAt: string
}
```

## 📋 模板组件设计

### 1. PublicLayout 公开页面布局

#### 1.1 布局结构
```vue
<template>
  <div class="public-layout">
    <!-- 页面头部 -->
    <Header>
      <template #logo>
        <Logo />
      </template>
      <template #nav>
        <PublicNavigation />
      </template>
      <template #actions>
        <LoginButton />
      </template>
    </Header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 页面底部 -->
    <Footer>
      <FooterContent />
    </Footer>
  </div>
</template>
```

### 2. DeveloperLayout 开发者布局

#### 2.1 布局结构
```vue
<template>
  <div class="developer-layout">
    <!-- 顶部导航 -->
    <Header>
      <template #logo>
        <Logo />
      </template>
      <template #nav>
        <DeveloperNavigation />
      </template>
      <template #user>
        <UserDropdown />
      </template>
    </Header>

    <div class="layout-body">
      <!-- 侧边栏 -->
      <Sidebar v-if="showSidebar">
        <DeveloperMenu />
      </Sidebar>

      <!-- 内容区域 -->
      <div class="content-wrapper">
        <!-- 面包屑导航 -->
        <Breadcrumb v-if="showBreadcrumb" />
        
        <!-- 页面内容 -->
        <div class="page-content">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>
```

## 🎯 组件状态管理

### 1. 组件内部状态

#### 1.1 响应式数据管理
```typescript
// 使用 Composition API 管理组件状态
export default defineComponent({
  setup() {
    // 响应式状态
    const loading = ref(false)
    const error = ref<string | null>(null)
    const data = ref<any[]>([])

    // 计算属性
    const isEmpty = computed(() => data.value.length === 0)
    const hasError = computed(() => error.value !== null)

    // 方法
    const loadData = async () => {
      loading.value = true
      error.value = null
      try {
        const result = await api.getData()
        data.value = result
      } catch (err) {
        error.value = err.message
      } finally {
        loading.value = false
      }
    }

    return {
      loading,
      error,
      data,
      isEmpty,
      hasError,
      loadData
    }
  }
})
```

### 2. 组件间通信

#### 2.1 Props 和 Events
```typescript
// 父子组件通信
interface ComponentProps {
  modelValue: any
  config: ComponentConfig
}

interface ComponentEmits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
  (e: 'action', action: string, data?: any): void
}
```

#### 2.2 Provide/Inject
```typescript
// 祖先组件提供数据
const projectKey = Symbol('project')

// 提供项目上下文
provide(projectKey, {
  project: readonly(currentProject),
  updateProject,
  refreshProject
})

// 后代组件注入数据
const projectContext = inject(projectKey)
```

## 🧪 组件测试策略

### 1. 单元测试
```typescript
// 组件单元测试示例
describe('Button Component', () => {
  it('should render with correct text', () => {
    const wrapper = mount(Button, {
      slots: {
        default: 'Click me'
      }
    })
    
    expect(wrapper.text()).toBe('Click me')
  })

  it('should emit click event', async () => {
    const wrapper = mount(Button)
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })

  it('should be disabled when loading', () => {
    const wrapper = mount(Button, {
      props: { loading: true }
    })
    
    expect(wrapper.find('button').attributes('disabled')).toBeDefined()
  })
})
```

### 2. 集成测试
```typescript
// 组合组件测试
describe('SearchBox Integration', () => {
  it('should show suggestions when typing', async () => {
    const wrapper = mount(SearchBox, {
      props: {
        suggestions: mockSuggestions
      }
    })

    const input = wrapper.find('input')
    await input.setValue('test')
    await input.trigger('input')

    expect(wrapper.find('.suggestions').isVisible()).toBe(true)
  })
})
```

这个组件系统设计为前端开发提供了完整的组件架构和实现指导，确保组件的可复用性、可维护性和一致性。

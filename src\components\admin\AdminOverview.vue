<template>
  <div class="admin-overview">
    <h2 class="section-title">系统概览</h2>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-users"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats?.totalDevelopers || 0 }}</div>
          <div class="stat-label">开发者总数</div>
          <div class="stat-trend" v-if="stats?.developersGrowth">
            <span :class="(stats.developersGrowth || 0) > 0 ? 'trend-up' : 'trend-down'">
              {{ (stats.developersGrowth || 0) > 0 ? '+' : '' }}{{ stats.developersGrowth }}
            </span>
          </div>
        </div>
      </div>

      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-projects"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats?.totalProjects || 0 }}</div>
          <div class="stat-label">项目总数</div>
          <div class="stat-trend" v-if="stats?.projectsGrowth">
            <span :class="(stats.projectsGrowth || 0) > 0 ? 'trend-up' : 'trend-down'">
              {{ (stats.projectsGrowth || 0) > 0 ? '+' : '' }}{{ stats.projectsGrowth }}
            </span>
          </div>
        </div>
      </div>

      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-releases"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats?.totalReleases || 0 }}</div>
          <div class="stat-label">版本总数</div>
          <div class="stat-trend" v-if="stats?.releasesGrowth">
            <span :class="(stats.releasesGrowth || 0) > 0 ? 'trend-up' : 'trend-down'">
              {{ (stats.releasesGrowth || 0) > 0 ? '+' : '' }}{{ stats.releasesGrowth }}
            </span>
          </div>
        </div>
      </div>

      <div class="stat-card card">
        <div class="stat-icon">
          <i class="icon-activity"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats?.totalActivities || 0 }}</div>
          <div class="stat-label">系统活动</div>
          <div class="stat-trend" v-if="stats?.activitiesGrowth">
            <span :class="(stats.activitiesGrowth || 0) > 0 ? 'trend-up' : 'trend-down'">
              {{ (stats.activitiesGrowth || 0) > 0 ? '+' : '' }}{{ stats.activitiesGrowth }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <h3 class="subsection-title">最近活动</h3>
      <div class="activity-list">
        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
          <div class="activity-icon">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          <div class="activity-content">
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
          </div>
        </div>
      </div>
      
      <div v-if="recentActivities.length === 0" class="empty-activities">
        <p>暂无最近活动</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDataService } from '@/data'
import type { SystemStats, Activity } from '@/data'

const dataService = getDataService()

// 响应式数据
const stats = ref<SystemStats | null>(null)
const recentActivities = ref<Activity[]>([])
const loading = ref(true)


// 格式化时间
function formatTime(timestamp: number) {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

// 获取活动图标
function getActivityIcon(type: Activity['type']) {
  const iconMap = {
    'developer_created': 'icon-user-plus',
    'developer_deleted': 'icon-user-minus',
    'project_created': 'icon-folder-plus',
    'project_deleted': 'icon-folder-minus',
    'permission_granted': 'icon-key-plus',
    'permission_revoked': 'icon-key-minus'
  }
  return iconMap[type] || 'icon-activity'
}

// 加载统计数据
async function loadStats() {
  try {
    stats.value = await dataService.getSystemStats()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载最近活动
async function loadRecentActivities() {
  try {
    recentActivities.value = await dataService.getSystemActivities({ pageSize: 5 })
  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}

// 初始化数据
onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadRecentActivities()
    ])
  } finally {
    loading.value = false
  }
})

</script>

<style scoped>
.admin-overview {
  margin-bottom: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-6) 0;
}

.subsection-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-4) 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.stat-card {
  padding: var(--spacing-5);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-base);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  margin-top: var(--spacing-1);
}

.stat-trend {
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

.trend-up {
  color: var(--color-success);
}

.trend-down {
  color: var(--color-danger);
}

/* 最近活动 */
.recent-activities {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  padding: var(--spacing-5);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  transition: background var(--transition-base);
}

.activity-item:hover {
  background: var(--bg-2);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: var(--color-info-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-info);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-1);
  line-height: var(--line-height-relaxed);
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  margin-top: var(--spacing-1);
}

.empty-activities {
  text-align: center;
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  padding: var(--spacing-4);
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: var(--spacing-4);
    gap: var(--spacing-3);
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
  }
  
  .stat-number {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>

# 信息架构设计

## 🏗️ 信息架构总览

### 1. 信息架构原则

#### 1.1 层次清晰原则
- **三层架构**: 系统 → 项目 → 版本/文件
- **分类明确**: 固件类 vs APP类项目
- **权限分层**: 公开内容 vs 管理内容

#### 1.2 用户心智模型匹配
- **熟悉性**: 符合用户对软件下载网站的认知
- **一致性**: 相同类型内容使用相同的组织方式
- **可预测性**: 用户能够预测在哪里找到什么内容

#### 1.3 可发现性原则
- **多路径访问**: 重要内容可通过多种方式到达
- **相关性推荐**: 在适当位置推荐相关内容
- **搜索友好**: 所有内容都可被搜索发现

### 2. 核心内容模型

#### 2.1 项目内容模型
```
项目 (Project)
├── 基本信息
│   ├── 项目名称 (必需)
│   ├── 项目描述 (必需)
│   ├── 项目分类 (固件/APP)
│   ├── 创建时间
│   └── 更新时间
├── 统计信息
│   ├── 总下载量
│   ├── 版本数量
│   ├── 最新版本
│   └── 最后更新时间
├── 版本列表
│   └── → 版本内容模型
└── 权限信息
    ├── 创建者
    ├── 管理者列表
    └── 访问权限
```

#### 2.2 版本内容模型
```
版本 (Release)
├── 版本信息
│   ├── 版本号 (v1.0.0.0)
│   ├── 版本类型 (开发版/稳定版/发布版本/测试版本)
│   ├── 版本标题
│   ├── 版本描述/更新日志
│   └── 发布时间
├── 适配信息
│   ├── 设备类型 (耳机/音箱/Android/iOS)
│   ├── 兼容性说明
│   └── 系统要求
├── 文件信息
│   ├── 文件名称
│   ├── 文件大小
│   ├── 文件格式
│   ├── 文件哈希值
│   └── 下载链接
├── 统计信息
│   ├── 下载次数
│   ├── 发布者
│   └── 状态 (已发布/已废弃)
└── 操作记录
    ├── 创建记录
    ├── 修改记录
    └── 下载记录
```

#### 2.3 用户内容模型
```
用户 (User)
├── 基本信息
│   ├── 用户名
│   ├── 邮箱
│   ├── 角色 (admin/developer)
│   └── 状态 (active/disabled)
├── 权限信息
│   ├── 系统权限
│   ├── 项目权限列表
│   └── 权限获取时间
├── 活动信息
│   ├── 最后登录时间
│   ├── 操作历史
│   └── 创建的项目/版本
└── 统计信息
    ├── 创建项目数
    ├── 发布版本数
    └── 操作次数
```

## 🗂️ 导航架构设计

### 1. 主导航结构

#### 1.1 公开访问导航
```
主导航
├── 首页 (/)
├── 项目 (/projects)
│   ├── 全部项目
│   ├── 固件类项目
│   └── APP类项目
├── 搜索 (/search)
└── 登录 (/login)
```

#### 1.2 开发者导航
```
开发者导航
├── 工作台 (/developer)
├── 我的项目 (/developer/projects)
├── 版本管理 (/developer/releases)
├── 文件上传 (/developer/upload)
├── 操作日志 (/developer/logs)
└── 个人设置 (/developer/profile)
```

#### 1.3 管理员导航
```
管理员导航
├── 系统控制台 (/admin)
├── 项目管理 (/admin/projects)
├── 用户管理 (/admin/users)
├── 权限管理 (/admin/permissions)
├── 系统设置 (/admin/settings)
└── 系统日志 (/admin/logs)
```

### 2. 页面内导航设计

#### 2.1 项目详情页导航
```
项目详情页内导航
├── 概览 (默认)
│   ├── 项目基本信息
│   ├── 最新版本信息
│   └── 快速下载入口
├── 版本历史
│   ├── 版本时间线
│   ├── 版本筛选器
│   └── 版本对比功能
├── 统计数据
│   ├── 下载统计
│   ├── 版本分布
│   └── 趋势分析
└── 项目设置 (仅管理者可见)
    ├── 项目信息编辑
    ├── 权限设置
    └── 删除项目
```

#### 2.2 开发者工作台导航
```
工作台页内导航
├── 仪表板 (默认)
│   ├── 数据概览
│   ├── 快速操作
│   └── 最近活动
├── 项目管理
│   ├── 项目列表
│   ├── 项目创建
│   └── 项目设置
├── 版本管理
│   ├── 待发布版本
│   ├── 已发布版本
│   └── 版本统计
└── 系统通知
    ├── 系统消息
    ├── 权限变更
    └── 操作结果
```

### 3. 面包屑导航规则

#### 3.1 公开页面面包屑
```
首页 > 项目 > 项目名称 > 版本详情
首页 > 搜索 > 搜索结果
首页 > 项目 > 固件类
```

#### 3.2 开发者页面面包屑
```
工作台 > 我的项目 > 项目名称 > 版本管理
工作台 > 文件上传 > 选择项目
工作台 > 操作日志 > 详细记录
```

#### 3.3 管理员页面面包屑
```
控制台 > 用户管理 > 用户详情
控制台 > 权限管理 > 项目权限
控制台 > 系统设置 > 基础配置
```

## 📋 内容分类体系

### 1. 项目分类体系

#### 1.1 主分类
- **固件类项目**: 硬件设备的固件程序
  - 耳机固件
  - 音箱固件
  - 路由器固件
  - 其他硬件固件

- **APP类项目**: 软件应用程序
  - Android应用
  - iOS应用
  - Windows应用
  - 跨平台应用

#### 1.2 辅助分类标签
- **设备品牌**: 按设备制造商分类
- **设备型号**: 按具体设备型号分类
- **功能特性**: 按主要功能分类
- **技术特征**: 按技术实现分类

### 2. 版本分类体系

#### 2.1 版本类型分类
- **开发版 (Development)**: 
  - 最新功能
  - 可能不稳定
  - 开发测试使用

- **测试版 (Beta)**:
  - 功能基本完整
  - 需要测试验证
  - 限定用户使用

- **稳定版 (Stable)**:
  - 经过充分测试
  - 功能稳定可靠
  - 推荐一般用户使用

- **发布版本 (Release)**:
  - 正式发布版本
  - 生产环境使用
  - 长期支持维护

#### 2.2 版本状态分类
- **已发布**: 正常可下载使用
- **已废弃**: 不再推荐使用
- **已撤回**: 发现问题已撤回

### 3. 内容优先级体系

#### 3.1 高优先级内容
- 最新稳定版本
- 推荐下载版本
- 热门项目
- 重要更新通知

#### 3.2 中优先级内容
- 历史版本
- 相关项目推荐
- 统计数据
- 帮助文档

#### 3.3 低优先级内容
- 开发版本
- 详细技术信息
- 操作日志
- 系统设置

## 🔍 搜索架构设计

### 1. 搜索内容范围

#### 1.1 全文搜索范围
- 项目名称和描述
- 版本标题和更新说明
- 设备类型和兼容性信息
- 标签和关键词

#### 1.2 精确搜索范围
- 版本号
- 文件名
- 用户名
- 项目ID

### 2. 搜索结果组织

#### 2.1 搜索结果分类
```
搜索结果组织
├── 项目结果
│   ├── 完全匹配项目
│   ├── 部分匹配项目
│   └── 相关推荐项目
├── 版本结果
│   ├── 版本号匹配
│   ├── 描述匹配
│   └── 文件名匹配
└── 筛选器
    ├── 项目分类筛选
    ├── 版本类型筛选
    ├── 设备类型筛选
    └── 时间范围筛选
```

#### 2.2 搜索结果排序
- **相关性排序**: 根据匹配度评分
- **时间排序**: 按更新时间排序
- **热度排序**: 按下载量排序
- **名称排序**: 按字母顺序排序

### 3. 搜索体验优化

#### 3.1 搜索建议
- 自动完成建议
- 历史搜索记录
- 热门搜索推荐
- 相关搜索建议

#### 3.2 搜索结果展示
- 关键词高亮显示
- 结果摘要生成
- 快速操作入口
- 结果数量统计

## 📊 数据展示架构

### 1. 统计数据层次

#### 1.1 系统级统计
- 总项目数量
- 总版本数量
- 总下载次数
- 活跃用户数量

#### 1.2 项目级统计
- 项目下载总量
- 版本发布数量
- 最新版本信息
- 用户反馈统计

#### 1.3 版本级统计
- 版本下载次数
- 下载趋势变化
- 用户使用反馈
- 问题报告统计

### 2. 数据可视化需求

#### 2.1 趋势类数据
- 下载量趋势图
- 版本发布时间线
- 用户活跃度曲线
- 问题修复进度

#### 2.2 分布类数据
- 项目分类分布饼图
- 设备类型分布图
- 版本类型分布图
- 用户权限分布图

#### 2.3 对比类数据
- 项目下载量对比
- 版本性能对比
- 时间段数据对比
- 用户群体对比

这个信息架构设计为整个前端系统提供了清晰的内容组织和导航结构，确保用户能够高效地找到和使用所需的信息。

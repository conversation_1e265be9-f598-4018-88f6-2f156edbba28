import { defineStore } from 'pinia'
import type { DownloadTask, DownloadStatus } from '@/types/domain'

const controllers = new Map<string, AbortController>()

function createId() {
  return Math.random().toString(36).slice(2)
}

export const useDownloadsStore = defineStore('downloads', {
  state: () => ({
    tasks: [] as DownloadTask[],
  }),
  getters: {
    activeCount(state) {
      return state.tasks.filter((t) => t.status === 'downloading').length
    },
  },
  actions: {
    enqueue(payload: { url: string; filename: string; size?: number; releaseId?: number; projectId?: number }) {
      const now = Date.now()
      const task: DownloadTask = {
        id: createId(),
        url: payload.url,
        filename: payload.filename,
        size: payload.size,
        receivedBytes: 0,
        status: 'queued',
        createdAt: now,
        updatedAt: now,
        releaseId: payload.releaseId,
        projectId: payload.projectId,
      }
      this.tasks.unshift(task)
      this.start(task.id)
      return task
    },
    async start(id: string) {
      const task = this.tasks.find((t) => t.id === id)
      if (!task || task.status === 'downloading') return
      task.status = 'downloading'
      task.updatedAt = Date.now()

      try {
        // 简化的下载授权逻辑（在新架构中，下载链接已经是授权的）
        const auth = { authorized: true }

        const controller = new AbortController()
        controllers.set(task.id, controller)
        const startedAt = Date.now()
        let lastTickAt = startedAt
        let lastBytes = 0

        const resp = await fetch(task.url, { signal: controller.signal })
        const contentLength = resp.headers.get('content-length')
        if (!task.size && contentLength) task.size = Number(contentLength)
        const reader = resp.body?.getReader()
        const chunks: Uint8Array[] = []
        if (!reader) throw new Error('无法读取下载数据流')

        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          if (value) {
            chunks.push(value)
            task.receivedBytes += value.byteLength
          }
          const now = Date.now()
          const dt = (now - lastTickAt) / 1000
          if (dt >= 0.3) {
            const delta = task.receivedBytes - lastBytes
            task.speedBps = Math.max(0, Math.floor(delta / dt))
            if (task.size) {
              const left = Math.max(0, task.size - task.receivedBytes)
              task.etaSeconds = task.speedBps > 0 ? Math.ceil(left / task.speedBps) : undefined
            }
            lastTickAt = now
            lastBytes = task.receivedBytes
            task.updatedAt = now
          }
        }

        const blob = new Blob(chunks)
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = task.filename
        document.body.appendChild(a)
        a.click()
        a.remove()
        setTimeout(() => URL.revokeObjectURL(url), 5000)

        task.status = 'completed'
        if (!task.size) task.size = task.receivedBytes
        task.updatedAt = Date.now()
      } catch (e: any) {
        const isAbort = e?.name === 'AbortError'
        const currentStatus = task.status as any
        if (isAbort && (currentStatus === 'paused' || currentStatus === 'cancelled')) {
          // 状态已在 pause/cancel 方法中设置
        } else {
          task.status = 'failed'
          task.error = e?.message || String(e)
        }
        task.updatedAt = Date.now()
      }
    },
    pause(id: string) {
      const task = this.tasks.find((t) => t.id === id)
      if (!task) return
      const ctl = controllers.get(id)
      if (ctl) ctl.abort()
      task.status = 'paused'
      task.updatedAt = Date.now()
    },
    cancel(id: string) {
      const task = this.tasks.find((t) => t.id === id)
      if (!task) return
      const ctl = controllers.get(id)
      if (ctl) ctl.abort()
      task.status = 'cancelled'
      task.updatedAt = Date.now()
    },
    retry(id: string) {
      const task = this.tasks.find((t) => t.id === id)
      if (!task) return
      task.receivedBytes = 0
      task.error = undefined
      task.status = 'queued'
      task.updatedAt = Date.now()
      this.start(id)
    },
    updateStatus(id: string, status: DownloadStatus) {
      const task = this.tasks.find((t) => t.id === id)
      if (!task) return
      task.status = status
      task.updatedAt = Date.now()
    },
    clearCompleted() {
      this.tasks = this.tasks.filter((t) => t.status !== 'completed')
    },
  },
})



export interface ApiResponse<T = unknown> {
  code: number
  message: string
  data: T
  traceId?: string
}

export type ErrorKind = 'Network' | 'Http' | 'Business' | 'Canceled' | 'Unknown'

export enum ErrorCode {
  SUCCESS = 0,
  VALIDATION = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  TOO_MANY_REQUESTS = 429,
  SERVER_ERROR = 500,
}

export interface AppErrorOptions {
  message: string
  code?: number
  httpStatus?: number
  kind?: ErrorKind
  url?: string
  method?: string
  cause?: unknown
}

export class AppError extends Error {
  public readonly code?: number
  public readonly httpStatus?: number
  public readonly kind: ErrorKind
  public readonly url?: string
  public readonly method?: string

  constructor(options: AppErrorOptions) {
    super(options.message)
    this.name = 'AppError'
    this.code = options.code
    this.httpStatus = options.httpStatus
    this.kind = options.kind || 'Unknown'
    this.url = options.url
    this.method = options.method
    if (options.cause) {
      // @ts-expect-error node18-compatible cause
      this.cause = options.cause
    }
  }
}

export function createAppError(options: AppErrorOptions): AppError {
  return new AppError(options)
}



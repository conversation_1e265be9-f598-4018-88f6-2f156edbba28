# 路由系统设计

## 🗺️ 路由架构设计

### 1. 路由设计原则

#### 1.1 设计理念
- **语义化URL**: URL能够清晰表达页面内容和层级关系
- **RESTful风格**: 遵循REST规范，资源和操作的清晰映射
- **用户友好**: URL易于理解和记忆，支持直接访问
- **SEO友好**: 良好的URL结构有利于搜索引擎优化

#### 1.2 路由分类
```
路由系统分类
├── 公开路由 (Public Routes)
│   ├── 首页和浏览页面
│   ├── 项目详情页面
│   ├── 搜索结果页面
│   └── 错误页面
├── 认证路由 (Auth Routes)
│   ├── 登录页面
│   ├── 密码重置页面
│   └── 认证回调页面
├── 保护路由 (Protected Routes)
│   ├── 开发者工作区
│   ├── 管理员控制台
│   └── 用户设置页面
└── 动态路由 (Dynamic Routes)
    ├── 参数化路径
    ├── 嵌套路由
    └── 可选参数路由
```

### 2. URL 结构设计

#### 2.1 URL 命名规范
```typescript
// URL结构规范
interface URLStructure {
  // 资源类型
  resource: 'projects' | 'releases' | 'users' | 'admin'
  
  // 资源ID（可选）
  id?: string | number
  
  // 子资源（可选）
  subResource?: 'releases' | 'permissions' | 'logs'
  
  // 操作类型（可选）
  action?: 'edit' | 'create' | 'settings' | 'delete'
  
  // 查询参数
  query?: Record<string, string>
}

// URL示例
const urlExamples = {
  // 公开路由
  home: '/',
  projects: '/projects',
  projectDetail: '/projects/:id',
  projectReleases: '/projects/:id/releases',
  releaseDetail: '/projects/:id/releases/:releaseId',
  search: '/search',
  
  // 认证路由
  login: '/login',
  
  // 开发者路由
  developerDashboard: '/developer',
  developerProjects: '/developer/projects',
  developerProjectEdit: '/developer/projects/:id/edit',
  developerUpload: '/developer/upload',
  
  // 管理员路由
  adminDashboard: '/admin',
  adminUsers: '/admin/users',
  adminUserEdit: '/admin/users/:id/edit',
  adminPermissions: '/admin/permissions'
}
```

## 🛣️ 路由配置

### 1. 基础路由配置

#### 1.1 公开路由
```typescript
// router/modules/public.ts
export const publicRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/public/HomePage.vue'),
    meta: {
      title: '首页',
      requiresAuth: false,
      keepAlive: true
    }
  },
  {
    path: '/projects',
    name: 'ProjectList',
    component: () => import('@/views/public/ProjectListPage.vue'),
    meta: {
      title: '项目列表',
      requiresAuth: false,
      keepAlive: true
    }
  },
  {
    path: '/projects/:id(\\d+)',
    name: 'ProjectDetail',
    component: () => import('@/views/public/ProjectDetailPage.vue'),
    props: (route) => ({ 
      projectId: Number(route.params.id) 
    }),
    meta: {
      title: '项目详情',
      requiresAuth: false
    },
    children: [
      {
        path: '',
        name: 'ProjectOverview',
        component: () => import('@/views/public/ProjectOverview.vue')
      },
      {
        path: 'releases',
        name: 'ProjectReleases',
        component: () => import('@/views/public/ProjectReleases.vue')
      },
      {
        path: 'releases/:releaseId(\\d+)',
        name: 'ReleaseDetail',
        component: () => import('@/views/public/ReleaseDetail.vue'),
        props: (route) => ({
          projectId: Number(route.params.id),
          releaseId: Number(route.params.releaseId)
        })
      }
    ]
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/public/SearchPage.vue'),
    props: (route) => ({
      query: route.query.q,
      type: route.query.type,
      category: route.query.category
    }),
    meta: {
      title: '搜索结果',
      requiresAuth: false
    }
  }
]
```

#### 1.2 认证路由
```typescript
// router/modules/auth.ts
export const authRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginPage.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideForAuthenticated: true // 已登录用户隐藏
    },
    beforeEnter: (to, from, next) => {
      const authStore = useAuthStore()
      if (authStore.isAuthenticated) {
        // 已登录用户重定向到工作台
        const redirectPath = authStore.isAdmin ? '/admin' : '/developer'
        next(redirectPath)
      } else {
        next()
      }
    }
  },
  {
    path: '/logout',
    name: 'Logout',
    beforeEnter: async (to, from, next) => {
      const authStore = useAuthStore()
      await authStore.logout()
      next('/login')
    }
  }
]
```

#### 1.3 开发者路由
```typescript
// router/modules/developer.ts
export const developerRoutes: RouteRecordRaw[] = [
  {
    path: '/developer',
    component: () => import('@/layouts/DeveloperLayout.vue'),
    meta: {
      requiresAuth: true,
      roles: ['developer', 'admin']
    },
    children: [
      {
        path: '',
        name: 'DeveloperDashboard',
        component: () => import('@/views/developer/DashboardPage.vue'),
        meta: {
          title: '工作台',
          breadcrumb: [{ title: '工作台' }]
        }
      },
      {
        path: 'projects',
        name: 'DeveloperProjects',
        component: () => import('@/views/developer/MyProjectsPage.vue'),
        meta: {
          title: '我的项目',
          breadcrumb: [
            { title: '工作台', to: '/developer' },
            { title: '我的项目' }
          ]
        }
      },
      {
        path: 'projects/:id(\\d+)',
        component: () => import('@/views/developer/ProjectManagePage.vue'),
        props: true,
        meta: {
          title: '项目管理',
          breadcrumb: [
            { title: '工作台', to: '/developer' },
            { title: '我的项目', to: '/developer/projects' },
            { title: '项目管理' }
          ]
        },
        children: [
          {
            path: '',
            name: 'ProjectManageOverview',
            component: () => import('@/views/developer/ProjectOverview.vue')
          },
          {
            path: 'releases',
            name: 'ProjectManageReleases',
            component: () => import('@/views/developer/ReleaseManage.vue')
          },
          {
            path: 'settings',
            name: 'ProjectSettings',
            component: () => import('@/views/developer/ProjectSettings.vue'),
            meta: {
              permission: 'project:edit'
            }
          }
        ]
      },
      {
        path: 'upload',
        name: 'FileUpload',
        component: () => import('@/views/developer/FileUploadPage.vue'),
        meta: {
          title: '文件上传',
          breadcrumb: [
            { title: '工作台', to: '/developer' },
            { title: '文件上传' }
          ]
        }
      },
      {
        path: 'logs',
        name: 'OperationLogs',
        component: () => import('@/views/developer/OperationLogsPage.vue'),
        meta: {
          title: '操作日志',
          breadcrumb: [
            { title: '工作台', to: '/developer' },
            { title: '操作日志' }
          ]
        }
      }
    ]
  }
]
```

#### 1.4 管理员路由
```typescript
// router/modules/admin.ts
export const adminRoutes: RouteRecordRaw[] = [
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: {
      requiresAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/DashboardPage.vue'),
        meta: {
          title: '管理控制台',
          breadcrumb: [{ title: '控制台' }]
        }
      },
      {
        path: 'projects',
        name: 'AdminProjects',
        component: () => import('@/views/admin/ProjectManagePage.vue'),
        meta: {
          title: '项目管理',
          breadcrumb: [
            { title: '控制台', to: '/admin' },
            { title: '项目管理' }
          ]
        }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/UserManagePage.vue'),
        meta: {
          title: '用户管理',
          breadcrumb: [
            { title: '控制台', to: '/admin' },
            { title: '用户管理' }
          ]
        }
      },
      {
        path: 'users/:id(\\d+)/edit',
        name: 'AdminUserEdit',
        component: () => import('@/views/admin/UserEditPage.vue'),
        props: true,
        meta: {
          title: '编辑用户',
          breadcrumb: [
            { title: '控制台', to: '/admin' },
            { title: '用户管理', to: '/admin/users' },
            { title: '编辑用户' }
          ]
        }
      },
      {
        path: 'permissions',
        name: 'AdminPermissions',
        component: () => import('@/views/admin/PermissionManagePage.vue'),
        meta: {
          title: '权限管理',
          breadcrumb: [
            { title: '控制台', to: '/admin' },
            { title: '权限管理' }
          ]
        }
      }
    ]
  }
]
```

### 2. 路由元信息设计

#### 2.1 Meta 字段定义
```typescript
interface RouteMeta {
  // 页面标题
  title?: string
  
  // 权限控制
  requiresAuth?: boolean
  roles?: string[]
  permissions?: string[]
  
  // 页面缓存
  keepAlive?: boolean
  
  // 面包屑导航
  breadcrumb?: BreadcrumbItem[]
  
  // 页面特性
  hideNavigation?: boolean
  fullscreen?: boolean
  hideForAuthenticated?: boolean
  
  // SEO相关
  description?: string
  keywords?: string[]
  
  // 自定义数据
  custom?: Record<string, any>
}

interface BreadcrumbItem {
  title: string
  to?: string
  icon?: string
}
```

## 🔒 路由守卫设计

### 1. 全局路由守卫

#### 1.1 全局前置守卫
```typescript
// router/guards/index.ts
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 1. 设置页面标题
    setPageTitle(to)
    
    // 2. 认证检查
    const authResult = await checkAuthentication(to)
    if (!authResult.success) {
      next(authResult.redirect)
      return
    }
    
    // 3. 权限检查
    const permissionResult = checkPermissions(to)
    if (!permissionResult.success) {
      next('/403')
      return
    }
    
    // 4. 项目权限检查（特定路由）
    if (to.params.id) {
      const projectAccessResult = await checkProjectAccess(to)
      if (!projectAccessResult.success) {
        next('/403')
        return
      }
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach((to, from) => {
    // 1. 记录页面访问日志
    logPageVisit(to, from)
    
    // 2. 更新用户活动时间
    updateUserActivity()
    
    // 3. 页面加载完成处理
    handlePageLoadComplete(to)
  })
}

// 设置页面标题
function setPageTitle(route: RouteLocationNormalized) {
  const title = route.meta.title
  if (title) {
    document.title = `${title} - 软件发布系统`
  } else {
    document.title = '软件发布系统'
  }
}

// 认证检查
async function checkAuthentication(route: RouteLocationNormalized) {
  const authStore = useAuthStore()
  
  // 不需要认证的路由直接通过
  if (!route.meta.requiresAuth) {
    return { success: true }
  }
  
  // 检查是否已登录
  if (!authStore.isAuthenticated) {
    return {
      success: false,
      redirect: {
        name: 'Login',
        query: { redirect: route.fullPath }
      }
    }
  }
  
  // 检查token是否过期
  if (!authStore.checkSession()) {
    try {
      await authStore.refreshAuth()
    } catch (error) {
      return {
        success: false,
        redirect: {
          name: 'Login',
          query: { redirect: route.fullPath }
        }
      }
    }
  }
  
  return { success: true }
}

// 权限检查
function checkPermissions(route: RouteLocationNormalized) {
  const authStore = useAuthStore()
  
  // 检查角色权限
  if (route.meta.roles) {
    const hasRole = route.meta.roles.includes(authStore.user?.role)
    if (!hasRole) {
      return { success: false }
    }
  }
  
  // 检查具体权限
  if (route.meta.permissions) {
    const hasPermission = route.meta.permissions.every(permission =>
      authStore.permissions.includes(permission)
    )
    if (!hasPermission) {
      return { success: false }
    }
  }
  
  return { success: true }
}

// 项目访问权限检查
async function checkProjectAccess(route: RouteLocationNormalized) {
  const authStore = useAuthStore()
  const projectId = Number(route.params.id)
  
  // 管理员有所有项目的访问权限
  if (authStore.isAdmin) {
    return { success: true }
  }
  
  // 检查开发者是否有该项目的访问权限
  if (authStore.isDeveloper) {
    const hasAccess = authStore.hasProjectAccess(projectId)
    return { success: hasAccess }
  }
  
  return { success: false }
}
```

### 2. 组件内路由守卫

#### 2.1 路由进入守卫
```typescript
// 在组件中使用路由守卫
export default defineComponent({
  async beforeRouteEnter(to, from, next) {
    // 组件创建前的路由守卫
    try {
      // 预加载必要数据
      const projectId = Number(to.params.id)
      const projectData = await projectAPI.getDetail(projectId)
      
      next(vm => {
        // 将数据传递给组件实例
        vm.initializeWithData(projectData)
      })
    } catch (error) {
      // 数据加载失败，重定向到错误页面
      next('/404')
    }
  },
  
  async beforeRouteUpdate(to, from) {
    // 路由参数变化时的守卫
    if (to.params.id !== from.params.id) {
      // 参数变化，重新加载数据
      await this.loadProjectData(Number(to.params.id))
    }
  },
  
  beforeRouteLeave(to, from, next) {
    // 离开路由前的确认
    if (this.hasUnsavedChanges) {
      const answer = window.confirm('您有未保存的更改，确定要离开吗？')
      if (!answer) {
        next(false)
        return
      }
    }
    next()
  }
})
```

## 🔄 动态路由

### 1. 动态路由注册

#### 1.1 基于权限的动态路由
```typescript
// 动态路由生成
export function generateDynamicRoutes(userRole: string, permissions: string[]) {
  const routes: RouteRecordRaw[] = []
  
  // 根据角色添加路由
  if (userRole === 'admin') {
    routes.push(...adminRoutes)
  }
  
  if (userRole === 'developer' || userRole === 'admin') {
    routes.push(...developerRoutes)
  }
  
  // 根据权限过滤路由
  return routes.filter(route => {
    if (route.meta?.permissions) {
      return route.meta.permissions.every(permission =>
        permissions.includes(permission)
      )
    }
    return true
  })
}

// 动态添加路由
export function addDynamicRoutes(router: Router, userRole: string, permissions: string[]) {
  const dynamicRoutes = generateDynamicRoutes(userRole, permissions)
  
  dynamicRoutes.forEach(route => {
    router.addRoute(route)
  })
}
```

### 2. 路由懒加载

#### 2.1 组件懒加载配置
```typescript
// 路由组件懒加载
const lazyLoad = (componentPath: string) => {
  return () => import(/* webpackChunkName: "[request]" */ `@/views/${componentPath}.vue`)
}

// 按功能模块分组的懒加载
const loadProjectModule = () => import(
  /* webpackChunkName: "project-module" */
  '@/views/project/index.vue'
)

const loadAdminModule = () => import(
  /* webpackChunkName: "admin-module" */
  '@/views/admin/index.vue'
)
```

## 📱 路由响应式处理

### 1. 移动端路由适配

#### 1.1 移动端路由重定向
```typescript
// 移动端路由适配
function setupMobileRoutes(router: Router) {
  router.beforeEach((to, from, next) => {
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    
    if (isMobile && to.name === 'ProjectDetail') {
      // 移动端项目详情页使用不同的组件
      next({
        name: 'MobileProjectDetail',
        params: to.params,
        query: to.query
      })
    } else {
      next()
    }
  })
}
```

### 2. 路由状态同步

#### 2.1 URL状态同步
```typescript
// URL状态同步
export function syncUrlState() {
  const route = useRoute()
  const router = useRouter()
  
  // 监听查询参数变化
  watch(
    () => route.query,
    (newQuery) => {
      // 同步查询参数到组件状态
      const searchStore = useSearchStore()
      if (newQuery.q) {
        searchStore.updateQuery(newQuery.q as string)
      }
      if (newQuery.category) {
        searchStore.updateCategory(newQuery.category as string)
      }
    },
    { immediate: true }
  )
  
  // 组件状态变化时更新URL
  const updateUrl = (query: Record<string, string>) => {
    router.push({
      name: route.name,
      params: route.params,
      query: { ...route.query, ...query }
    })
  }
  
  return { updateUrl }
}
```

## 🔧 路由工具函数

### 1. 导航工具

#### 1.1 编程式导航封装
```typescript
// 导航工具函数
export class NavigationUtils {
  private router: Router
  
  constructor(router: Router) {
    this.router = router
  }
  
  // 安全导航（检查权限）
  async safeNavigate(to: RouteLocationRaw) {
    try {
      await this.router.push(to)
    } catch (error) {
      console.error('Navigation failed:', error)
      // 导航失败时的降级处理
      if (error.name === 'NavigationDuplicated') {
        // 重复导航，忽略错误
        return
      }
      // 其他错误，返回首页
      this.router.push('/')
    }
  }
  
  // 返回上一页（带默认页面）
  goBack(fallback: RouteLocationRaw = '/') {
    if (window.history.length > 1) {
      this.router.go(-1)
    } else {
      this.router.push(fallback)
    }
  }
  
  // 打开新标签页
  openInNewTab(to: RouteLocationRaw) {
    const resolved = this.router.resolve(to)
    window.open(resolved.href, '_blank')
  }
}
```

### 2. 路由状态管理

#### 2.1 路由历史管理
```typescript
// 路由历史管理
export function useRouteHistory() {
  const history = ref<RouteLocationNormalized[]>([])
  const currentIndex = ref(-1)
  
  const router = useRouter()
  
  // 记录路由历史
  router.afterEach((to) => {
    history.value.push(to)
    currentIndex.value = history.value.length - 1
    
    // 限制历史记录长度
    if (history.value.length > 50) {
      history.value.shift()
      currentIndex.value--
    }
  })
  
  // 获取前一个路由
  const getPreviousRoute = () => {
    return currentIndex.value > 0 ? history.value[currentIndex.value - 1] : null
  }
  
  // 检查是否可以返回
  const canGoBack = computed(() => currentIndex.value > 0)
  
  return {
    history: readonly(history),
    currentIndex: readonly(currentIndex),
    getPreviousRoute,
    canGoBack
  }
}
```

这个路由系统设计为前端应用提供了完整的路由管理方案，包括路由配置、权限控制、动态路由和响应式处理，确保良好的用户导航体验。

/**
 * Mock数据服务实现
 * 
 * 提供统一的示例数据接口，模拟真实API的行为
 */

import type { 
  ProjectItem, 
  ReleaseItem, 
  UserItem, 
  PageResult,
  SearchResultItem,
  SearchPageResult,
  ReleaseDetail 
} from '@/types/domain'
import type {
  UnifiedDataService,
  SystemStats,
  DeveloperStats,
  Activity,
  DeveloperInfo,
  ProjectAssignment,
  SoftwareType,
  Version,
  ProjectSummary,
  QueryParams
} from './types'
import {
  mockData,
  mockSystemStats,
  getMockDeveloperStats
} from './mockData'

// 模拟API延迟
const DEFAULT_DELAY = 800

function delay(ms: number = DEFAULT_DELAY): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 分页工具函数
function paginate<T>(data: T[], page: number = 1, pageSize: number = 10): PageResult<T> {
  const start = (page - 1) * pageSize
  const end = start + pageSize
  return {
    list: data.slice(start, end),
    page,
    pageSize,
    total: data.length
  }
}

// 搜索工具函数
function searchItems<T extends Record<string, any>>(
  items: T[], 
  searchText: string, 
  searchFields: (keyof T)[]
): T[] {
  if (!searchText.trim()) return items
  
  const query = searchText.toLowerCase()
  return items.filter(item =>
    searchFields.some(field => {
      const value = item[field]
      if (typeof value === 'string') {
        return value.toLowerCase().includes(query)
      }
      return false
    })
  )
}

// 排序工具函数
function sortItems<T>(
  items: T[], 
  sortBy?: string, 
  order: 'asc' | 'desc' = 'desc'
): T[] {
  if (!sortBy) return items
  
  return [...items].sort((a, b) => {
    const aVal = (a as any)[sortBy]
    const bVal = (b as any)[sortBy]
    
    if (aVal === bVal) return 0
    
    let result: number
    if (typeof aVal === 'string' && typeof bVal === 'string') {
      result = aVal.localeCompare(bVal)
    } else if (typeof aVal === 'number' && typeof bVal === 'number') {
      result = aVal - bVal
    } else {
      result = String(aVal).localeCompare(String(bVal))
    }
    
    return order === 'asc' ? result : -result
  })
}

// Mock数据服务实现
export const mockDataService: UnifiedDataService = {
  // 系统统计
  async getSystemStats(): Promise<SystemStats> {
    await delay()
    return { ...mockSystemStats }
  },

  async getDeveloperStats(developerId: number): Promise<DeveloperStats> {
    await delay()
    return getMockDeveloperStats(developerId)
  },

  // 项目管理
  async getProjects(params?: QueryParams): Promise<PageResult<ProjectItem>> {
    await delay()
    let projects = [...mockData.projects]

    // 搜索
    if (params?.search) {
      projects = searchItems(projects, params.search, ['name', 'description'])
    }

    // 状态过滤
    if (params?.status) {
      projects = projects.filter(p => p.status === params.status)
    }

    // 类型过滤 (需要根据项目名称推断)
    if (params?.type) {
      projects = projects.filter(p => {
        const name = p.name.toLowerCase()
        if (params.type === 'firmware') return name.includes('固件') || name.includes('控制')
        if (params.type === 'app') return name.includes('app') || name.includes('应用')
        if (params.type === 'library') return name.includes('sdk') || name.includes('库')
        return false
      })
    }

    // 排序
    projects = sortItems(projects, params?.sortBy, params?.order)

    return paginate(projects, params?.page, params?.pageSize)
  },

  async getProjectDetail(id: number): Promise<ProjectItem> {
    await delay()
    const project = mockData.projects.find(p => p.id === id)
    if (!project) {
      throw new Error(`Project ${id} not found`)
    }
    return { ...project }
  },

  async createProject(data: Partial<ProjectItem>): Promise<ProjectItem> {
    await delay()
    const newProject: ProjectItem = {
      id: Math.max(...mockData.projects.map(p => p.id)) + 1,
      name: data.name || '',
      description: data.description,
      status: data.status || 'active',
      createdAt: Date.now(),
      updatedAt: Date.now()
    }
    
    mockData.projects.unshift(newProject)
    mockSystemStats.totalProjects = mockData.projects.length
    
    return { ...newProject }
  },

  async updateProject(id: number, data: Partial<ProjectItem>): Promise<ProjectItem> {
    await delay()
    const project = mockData.projects.find(p => p.id === id)
    if (!project) {
      throw new Error(`Project ${id} not found`)
    }
    
    Object.assign(project, data, { updatedAt: Date.now() })
    return { ...project }
  },

  async deleteProject(id: number): Promise<void> {
    await delay()
    const index = mockData.projects.findIndex(p => p.id === id)
    if (index === -1) {
      throw new Error(`Project ${id} not found`)
    }
    
    mockData.projects.splice(index, 1)
    mockSystemStats.totalProjects = mockData.projects.length
  },

  // 项目概览
  async getProjectSummaries(params?: QueryParams): Promise<ProjectSummary[]> {
    await delay()
    let summaries = [...mockData.projectSummaries]

    if (params?.search) {
      summaries = searchItems(summaries, params.search, ['name', 'description'])
    }

    summaries = sortItems(summaries, params?.sortBy, params?.order)

    if (params?.pageSize) {
      const start = ((params?.page || 1) - 1) * params.pageSize
      summaries = summaries.slice(start, start + params.pageSize)
    }

    return summaries
  },

  // 软件类型管理
  async getSoftwareTypes(projectId: number): Promise<SoftwareType[]> {
    await delay()
    return mockData.softwareTypes.filter(st => st.projectId === projectId)
  },

  async createSoftwareType(projectId: number, data: { name: string }): Promise<SoftwareType> {
    await delay()
    const newSoftwareType: SoftwareType = {
      id: Math.max(...mockData.softwareTypes.map(st => st.id)) + 1,
      projectId,
      name: data.name,
      updatedAt: Date.now()
    }
    
    mockData.softwareTypes.push(newSoftwareType)
    return { ...newSoftwareType }
  },

  async deleteSoftwareType(id: number): Promise<void> {
    await delay()
    const index = mockData.softwareTypes.findIndex(st => st.id === id)
    if (index === -1) {
      throw new Error(`Software type ${id} not found`)
    }
    
    mockData.softwareTypes.splice(index, 1)
    // 同时删除相关版本
    const versionIndices = mockData.versions
      .map((v, i) => v.softwareTypeId === id ? i : -1)
      .filter(i => i !== -1)
      .reverse() // 从后往前删除
    
    versionIndices.forEach(i => mockData.versions.splice(i, 1))
  },

  // 版本管理
  async getVersions(softwareTypeId: number): Promise<Version[]> {
    await delay()
    return mockData.versions
      .filter(v => v.softwareTypeId === softwareTypeId)
      .sort((a, b) => b.updatedAt - a.updatedAt)
  },

  async getVersionDetail(id: number): Promise<Version> {
    await delay()
    const version = mockData.versions.find(v => v.id === id)
    if (!version) {
      throw new Error(`Version ${id} not found`)
    }
    return { ...version }
  },

  async createVersion(data: Partial<Version>): Promise<Version> {
    await delay()
    const newVersion: Version = {
      id: Math.max(...mockData.versions.map(v => v.id)) + 1,
      softwareTypeId: data.softwareTypeId!,
      versionName: data.versionName || '',
      fileUrl: data.fileUrl,
      fileSize: data.fileSize,
      updatedAt: Date.now(),
      description: data.description,
      devices: data.devices || [],
      type: data.type || 'stable'
    }
    
    mockData.versions.push(newVersion)
    
    // 更新软件类型的最新版本
    const softwareType = mockData.softwareTypes.find(st => st.id === newVersion.softwareTypeId)
    if (softwareType) {
      softwareType.latestVersion = newVersion
      softwareType.updatedAt = Date.now()
    }
    
    return { ...newVersion }
  },

  async updateVersion(id: number, data: Partial<Version>): Promise<Version> {
    await delay()
    const version = mockData.versions.find(v => v.id === id)
    if (!version) {
      throw new Error(`Version ${id} not found`)
    }
    
    Object.assign(version, data, { updatedAt: Date.now() })
    
    // 如果更新的是最新版本，也更新软件类型
    const softwareType = mockData.softwareTypes.find(st => 
      st.latestVersion?.id === id
    )
    if (softwareType) {
      softwareType.latestVersion = { ...version }
      softwareType.updatedAt = Date.now()
    }
    
    return { ...version }
  },

  async deleteVersion(id: number): Promise<void> {
    await delay()
    const index = mockData.versions.findIndex(v => v.id === id)
    if (index === -1) {
      throw new Error(`Version ${id} not found`)
    }
    
    mockData.versions.splice(index, 1)
    
    // 如果删除的是最新版本，需要更新软件类型
    const softwareType = mockData.softwareTypes.find(st => 
      st.latestVersion?.id === id
    )
    if (softwareType) {
      const remainingVersions = mockData.versions.filter(v => 
        v.softwareTypeId === softwareType.id
      )
      if (remainingVersions.length > 0) {
        const newLatest = remainingVersions.sort((a, b) => b.updatedAt - a.updatedAt)[0]
        softwareType.latestVersion = newLatest
      } else {
        delete softwareType.latestVersion
      }
      softwareType.updatedAt = Date.now()
    }
    // 清理未使用的变量
    // const version = mockData.versions[index] // 已删除
  },

  // 版本发布
  async getReleases(params?: QueryParams): Promise<PageResult<ReleaseItem>> {
    await delay()
    let releases = [...mockData.releases]

    // 项目过滤
    if (params?.search) {
      releases = searchItems(releases, params.search, ['projectName', 'version', 'changelog'])
    }

    // 状态过滤
    if (params?.status) {
      releases = releases.filter(r => r.status === params.status)
    }

    // 排序
    releases = sortItems(releases, params?.sortBy, params?.order)

    return paginate(releases, params?.page, params?.pageSize)
  },

  async getReleaseDetail(id: number): Promise<ReleaseDetail> {
    await delay()
    const release = mockData.releases.find(r => r.id === id)
    if (!release) {
      throw new Error(`Release ${id} not found`)
    }

    // 模拟详情包含资产文件
    const detail: ReleaseDetail = {
      ...release,
      assets: [
        {
          id: `${id}-main`,
          name: `${release.projectName}-${release.version}.zip`,
          size: release.size || 0,
          url: release.downloadUrl || '',
          mime: 'application/zip'
        },
        {
          id: `${id}-readme`,
          name: 'README.md',
          size: 2048,
          url: `/preview/release-${id}/README.md`,
          mime: 'text/markdown'
        }
      ]
    }

    return detail
  },

  async createRelease(data: Partial<ReleaseItem>): Promise<ReleaseItem> {
    await delay()
    const newRelease: ReleaseItem = {
      id: Math.max(...mockData.releases.map(r => r.id)) + 1,
      projectId: data.projectId!,
      projectName: data.projectName || '',
      version: data.version || '',
      changelog: data.changelog || '',
      createdAt: Date.now(),
      status: 'success',
      size: data.size,
      type: data.type || 'stable',
      artifact: data.artifact || 'firmware',
      downloadUrl: data.downloadUrl
    }
    
    mockData.releases.unshift(newRelease)
    mockSystemStats.totalReleases = mockData.releases.length
    
    return { ...newRelease }
  },

  // 开发者管理
  async getDevelopers(params?: QueryParams): Promise<PageResult<DeveloperInfo>> {
    await delay()
    let developers = [...mockData.developers]

    if (params?.search) {
      developers = searchItems(developers, params.search, ['name', 'username'])
    }

    developers = sortItems(developers, params?.sortBy, params?.order)

    return paginate(developers, params?.page, params?.pageSize)
  },

  async getDeveloperDetail(id: number): Promise<DeveloperInfo> {
    await delay()
    const developer = mockData.developers.find(d => d.id === id)
    if (!developer) {
      throw new Error(`Developer ${id} not found`)
    }
    return { ...developer }
  },

  async createDeveloper(data: { username: string; password: string }): Promise<DeveloperInfo> {
    await delay()
    const newDeveloper: DeveloperInfo = {
      id: Math.max(...mockData.developers.map(d => d.id)) + 1,
      name: data.username, // 简单使用用户名作为显示名
      username: data.username,
      email: `${data.username}@company.com`,
      userType: 'developer',
      projectCount: 0,
      createdAt: Date.now(),
      permissions: ['project_read'],
      projects: [],
      assignedProjects: []
    }
    
    mockData.developers.push(newDeveloper)
    mockSystemStats.totalDevelopers = mockData.developers.length
    
    return { ...newDeveloper }
  },

  async updateDeveloper(id: number, data: Partial<DeveloperInfo>): Promise<DeveloperInfo> {
    await delay()
    const developer = mockData.developers.find(d => d.id === id)
    if (!developer) {
      throw new Error(`Developer ${id} not found`)
    }
    
    Object.assign(developer, data)
    return { ...developer }
  },

  async deleteDeveloper(id: number): Promise<void> {
    await delay()
    const index = mockData.developers.findIndex(d => d.id === id)
    if (index === -1) {
      throw new Error(`Developer ${id} not found`)
    }
    
    mockData.developers.splice(index, 1)
    mockSystemStats.totalDevelopers = mockData.developers.length
  },

  // 用户管理
  async getUsers(params?: QueryParams): Promise<PageResult<UserItem>> {
    await delay()
    let users = [...mockData.users]

    if (params?.search) {
      users = searchItems(users, params.search, ['name', 'username', 'email'])
    }

    users = sortItems(users, params?.sortBy, params?.order)

    return paginate(users, params?.page, params?.pageSize)
  },

  async getUserDetail(id: number): Promise<UserItem> {
    await delay()
    const user = mockData.users.find(u => u.id === id)
    if (!user) {
      throw new Error(`User ${id} not found`)
    }
    return { ...user }
  },

  async disableUser(id: number): Promise<void> {
    await delay()
    const user = mockData.users.find(u => u.id === id)
    if (!user) {
      throw new Error(`User ${id} not found`)
    }
    
    user.status = 'disabled'
  },

  // 活动记录
  async getSystemActivities(params?: QueryParams): Promise<Activity[]> {
    await delay()
    let activities = [...mockData.activities]

    activities = sortItems(activities, 'timestamp', 'desc')

    if (params?.pageSize) {
      const start = ((params?.page || 1) - 1) * params.pageSize
      activities = activities.slice(start, start + params.pageSize)
    }

    return activities
  },

  async getDeveloperActivities(developerId: number, params?: QueryParams): Promise<Activity[]> {
    await delay()
    let activities = mockData.activities.filter(a => a.userId === developerId)

    activities = sortItems(activities, 'timestamp', 'desc')

    if (params?.pageSize) {
      const start = ((params?.page || 1) - 1) * params.pageSize
      activities = activities.slice(start, start + params.pageSize)
    }

    return activities
  },

  // 权限管理
  async getPermissionAssignments(): Promise<ProjectAssignment[]> {
    await delay()
    const assignments: ProjectAssignment[] = []
    
    mockData.developers.forEach(dev => {
      assignments.push(...dev.assignedProjects)
    })
    
    return assignments
  },

  async assignProjectToDeveloper(developerId: number, projectId: number, permissions: string[]): Promise<void> {
    await delay()
    const developer = mockData.developers.find(d => d.id === developerId)
    const project = mockData.projects.find(p => p.id === projectId)
    
    if (!developer || !project) {
      throw new Error('Developer or project not found')
    }

    // 检查是否已经分配
    const existingIndex = developer.assignedProjects.findIndex(ap => ap.id === projectId)
    
    const assignment: ProjectAssignment = {
      id: projectId,
      name: project.name,
      type: project.name.includes('APP') ? 'app' : 
            project.name.includes('SDK') ? 'library' : 'firmware',
      assignedAt: Date.now(),
      permissions
    }

    if (existingIndex >= 0) {
      // 更新现有分配
      developer.assignedProjects[existingIndex] = assignment
    } else {
      // 新增分配
      developer.assignedProjects.push(assignment)
      developer.projectCount++
    if (!developer.projects) developer.projects = []
    developer.projects.push(projectId.toString())
    }
  },

  async revokeProjectFromDeveloper(developerId: number, projectId: number): Promise<void> {
    await delay()
    const developer = mockData.developers.find(d => d.id === developerId)
    if (!developer) {
      throw new Error('Developer not found')
    }

    const assignmentIndex = developer.assignedProjects.findIndex(ap => ap.id === projectId)
    if (assignmentIndex >= 0) {
      developer.assignedProjects.splice(assignmentIndex, 1)
      developer.projectCount = Math.max(0, developer.projectCount - 1)
      
      if (developer.projects) {
        const projectIndex = developer.projects.indexOf(projectId.toString())
        if (projectIndex >= 0) {
          developer.projects.splice(projectIndex, 1)
        }
      }
    }
  },

  // 搜索
  async search(query: string, params?: QueryParams): Promise<SearchPageResult> {
    await delay()
    const results: SearchResultItem[] = []

    // 搜索项目
    const projects = searchItems(mockData.projects, query, ['name', 'description'])
    projects.forEach(project => {
      results.push({
        kind: 'project',
        project: {
          id: project.id,
          name: project.name,
          description: project.description,
          status: project.status,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt
        }
      })
    })

    // 搜索版本发布
    const releases = searchItems(mockData.releases, query, ['projectName', 'version', 'changelog'])
    releases.forEach(release => {
      results.push({
        kind: 'release',
        release: {
          id: release.id,
          projectId: release.projectId,
          projectName: release.projectName,
          version: release.version,
          changelog: release.changelog,
          createdAt: release.createdAt,
          status: release.status,
          size: release.size,
          type: release.type,
          downloadUrl: release.downloadUrl
        }
      })
    })

    // 分页
    const paginated = paginate(results, params?.page, params?.pageSize)

    return {
      ...paginated,
        facets: {
          type: {
            'firmware': results.filter(r => {
              const release = mockData.releases.find(rel => rel.id === r.release?.id)
              return release?.artifact === 'firmware' || r.project?.name.includes('固件')
            }).length,
            'app': results.filter(r => {
              const release = mockData.releases.find(rel => rel.id === r.release?.id)
              return release?.artifact === 'android-demo' || r.project?.name.includes('APP')
            }).length,
            'library': results.filter(r => {
              const release = mockData.releases.find(rel => rel.id === r.release?.id)
              return release?.artifact === 'sdk' || r.project?.name.includes('SDK')
            }).length
          },
        device: {
          'ESP32': results.filter(r => r.project?.name.includes('ESP32')).length,
          'STM32': results.filter(r => r.project?.name.includes('STM32')).length,
          'Android': results.filter(r => r.project?.name.includes('Android')).length
        },
        versionType: {
          'stable': results.filter(r => r.release?.type === 'stable').length,
          'beta': results.filter(r => r.release?.type === 'beta').length,
          'alpha': results.filter(r => r.release?.type === 'alpha').length
        }
      }
    }
  }
}

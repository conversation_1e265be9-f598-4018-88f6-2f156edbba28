export function getHttpStatusMessage(status?: number): string {
  if (!status) return '网络异常，请检查网络连接'
  if (status === 400) return '请求无效，请检查参数'
  if (status === 401) return '未登录或登录已过期'
  if (status === 403) return '没有权限执行此操作'
  if (status === 404) return '接口不存在或地址错误'
  if (status === 408) return '请求超时，请稍后重试'
  if (status === 429) return '请求过于频繁，请稍后再试'
  if (status >= 500 && status < 600) return '服务暂时不可用，请稍后重试'
  return `请求失败（${status}）`
}

export function getBusinessCodeMessage(code?: number, fallback?: string): string {
  if (code === 0) return 'OK'
  if (code === 400) return '请求参数不正确'
  if (code === 401) return '未授权，请登录后再试'
  if (code === 403) return '没有权限'
  if (code === 404) return '资源不存在'
  if (code === 409) return '数据冲突，请刷新后重试'
  if (code === 429) return '操作过于频繁，请稍后再试'
  if (code === 500) return '服务器内部错误'
  return fallback || '请求失败'
}



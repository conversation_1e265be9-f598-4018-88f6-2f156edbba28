import type { Router } from 'vue-router'
import { useRouterProgress } from '@/composables/useLoading'

/**
 * 路由加载进度插件
 */
export function setupRouterProgress(router: Router) {
  const { startRouterProgress, finishRouterProgress, failRouterProgress } = useRouterProgress()

  // 路由开始导航
  router.beforeEach((to, from, next) => {
    // 只有在真正切换页面时才显示进度条
    if (to.path !== from.path) {
      startRouterProgress()
    }
    next()
  })

  // 路由导航完成
  router.afterEach((to, from, failure) => {
    if (to.path !== from.path) {
      if (failure) {
        failRouterProgress()
      } else {
        finishRouterProgress()
      }
    }
  })

  // 路由导航错误
  router.onError((error) => {
    console.error('Router navigation error:', error)
    failRouterProgress()
  })
}

/**
 * 页面加载状态管理
 * 可以在页面组件中使用
 */
export function usePageLoading() {
  const { startRouterProgress, finishRouterProgress } = useRouterProgress()

  /**
   * 手动触发页面加载进度
   */
  const triggerPageProgress = async (asyncOperation: () => Promise<void>) => {
    try {
      startRouterProgress()
      await asyncOperation()
    } catch (error) {
      console.error('Page loading error:', error)
      throw error
    } finally {
      finishRouterProgress()
    }
  }

  return {
    triggerPageProgress
  }
}

# 运维监控设计

## 📊 基础监控方案

### 1. 监控目标

#### 1.1 基本监控目标

**系统运行监控**
- 服务是否正常运行
- 基本的系统资源使用情况
- 简单的错误日志记录
- 基础的性能指标

**业务功能监控**
- 用户是否能正常登录
- 文件上传下载是否正常
- 核心功能是否可用

### 2. 系统资源监控

#### 2.1 基本资源监控

**监控指标**
- CPU使用率
- 内存使用情况
- 磁盘空间使用
- 网络连通性

**简单监控脚本**
```bash
#!/bin/bash
# 基础系统监控脚本

# 检查CPU使用率
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
if (( $(echo "$cpu_usage > 80" | bc -l) )); then
    echo "Warning: CPU usage is ${cpu_usage}%"
fi

# 检查内存使用率
mem_usage=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $mem_usage -gt 85 ]; then
    echo "Warning: Memory usage is ${mem_usage}%"
fi

# 检查磁盘空间
disk_usage=$(df -h / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
if [ $disk_usage -gt 85 ]; then
    echo "Warning: Disk usage is ${disk_usage}%"
fi
```

#### 2.2 网络监控

**网络连通性监控**
```bash
#!/bin/bash
# 网络监控脚本

TARGETS=(
    "db-server:3306"
    "redis-server:6379" 
    "external-api:443"
)

for target in "${TARGETS[@]}"; do
    IFS=':' read -r host port <<< "$target"
    
    # TCP连接测试
    if timeout 5 bash -c "</dev/tcp/$host/$port"; then
        echo "OK: $target 连接正常"
        echo "network_connectivity{target=\"$target\"} 1" >> /tmp/network_metrics.prom
    else
        echo "ERROR: $target 连接失败"
        echo "network_connectivity{target=\"$target\"} 0" >> /tmp/network_metrics.prom
    fi
    
    # 延迟测试
    latency=$(ping -c 1 -W 1000 $host | grep 'time=' | awk -F'time=' '{print $2}' | awk '{print $1}')
    if [ -n "$latency" ]; then
        echo "network_latency{target=\"$host\"} $latency" >> /tmp/network_metrics.prom
    fi
done
```

### 3. 应用监控

#### 3.1 应用状态监控

**Spring Boot健康检查**
```java
@RestController
public class HealthController {
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> status = new HashMap<>();
        
        try {
            // 检查数据库连接
            // 检查文件系统
            // 检查其他关键服务
            
            status.put("status", "UP");
            status.put("database", "UP");
            status.put("filesystem", "UP");
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            status.put("status", "DOWN");
            status.put("error", e.getMessage());
            return ResponseEntity.status(500).body(status);
        }
    }
}
```

#### 3.2 简单日志监控

**基本日志配置**
```properties
# application.properties
logging.level.com.company.swpublish=INFO
logging.file.name=logs/sw-publish.log
logging.file.max-size=100MB
logging.file.max-history=30
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
```

**关键操作日志**
```java
@Service
public class UserService {
    
    private static final Logger log = LoggerFactory.getLogger(UserService.class);
    
    public User login(String username, String password) {
        try {
            User user = authenticate(username, password);
            log.info("用户登录成功: username={}", username);
            return user;
        } catch (Exception e) {
            log.warn("用户登录失败: username={}, error={}", username, e.getMessage());
            throw e;
        }
    }
}
```

### 4. 数据库监控

#### 4.1 基本数据库监控

**数据库连接检查**
```java
@Component
public class DatabaseHealthCheck {
    
    @Autowired
    private DataSource dataSource;
    
    @Scheduled(fixedRate = 60000) // 每分钟检查
    public void checkDatabaseConnection() {
        try (Connection conn = dataSource.getConnection()) {
            conn.createStatement().execute("SELECT 1");
            log.debug("数据库连接正常");
        } catch (Exception e) {
            log.error("数据库连接失败: {}", e.getMessage());
            // 发送告警
        }
    }
}
```

**简单的慢查询检查**
```properties
# 开启慢查询日志
slow_query_log=1
long_query_time=2
slow_query_log_file=/var/log/mysql/slow.log
```

#### 4.2 连接池监控

**HikariCP监控配置**
```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    public DataSource dataSource(MeterRegistry meterRegistry) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("**************************************");
        config.setUsername("app_user");
        config.setPassword("password");
        
        // 连接池配置
        config.setMaximumPoolSize(50);
        config.setMinimumIdle(10);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        // 启用监控
        config.setMetricRegistry(meterRegistry);
        config.setHealthCheckRegistry(new HealthCheckRegistry());
        
        return new HikariDataSource(config);
    }
}
```

### 5. 告警机制

#### 5.1 简单告警

**邮件告警配置**
```properties
# application.properties
spring.mail.host=smtp.company.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=password

# 告警配置
app.alert.email.enabled=true
app.alert.email.recipients=<EMAIL>,<EMAIL>
```

**基本告警服务**
```java
@Service
public class SimpleAlertService {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${app.alert.email.recipients}")
    private String[] alertRecipients;
    
    public void sendAlert(String subject, String message) {
        try {
            SimpleMailMessage mail = new SimpleMailMessage();
            mail.setTo(alertRecipients);
            mail.setSubject("SW-Publish 系统告警: " + subject);
            mail.setText(message);
            mail.setFrom("<EMAIL>");
            
            mailSender.send(mail);
            log.info("告警邮件已发送: {}", subject);
        } catch (Exception e) {
            log.error("发送告警邮件失败: {}", e.getMessage());
        }
    }
}
```

这个简化的运维监控设计提供了基本的监控功能，满足内网1000用户规模的系统需求。

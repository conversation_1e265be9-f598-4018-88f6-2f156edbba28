<template>
  <div class="versions-section">
    <h2 class="section-title">版本列表</h2>
    <div class="versions-toolbar">
      <slot name="toolbar"></slot>
    </div>
    <div class="versions-list">
      <VersionItem 
        v-for="version in versions"
        :key="version.id"
        :version="version"
        @download="handleDownload"
      />
    </div>
    <div v-if="total && page && pageSize" class="pagination">
      <button class="btn btn-outline" :disabled="page <= 1" @click="$emit('change-page', page - 1)">上一页</button>
      <span class="page-info">第 {{ page }} / {{ Math.max(1, Math.ceil(total / pageSize)) }} 页（共 {{ total }} 条）</span>
      <button class="btn btn-outline" :disabled="page >= Math.max(1, Math.ceil(total / pageSize))" @click="$emit('change-page', page + 1)">下一页</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import VersionItem from './VersionItem.vue'

interface Version {
  id: string
  number: string
  description: string
  date: string
  size: string
  type: string
  status?: 'pending' | 'success' | 'failed'
  downloadUrl?: string
}

interface Props {
  versions: Version[]
  total?: number
  page?: number
  pageSize?: number
}

defineProps<Props>()

const emit = defineEmits<{
  download: [version: Version]
  'change-page': [page: number]
}>()

function handleDownload(version: Version) {
  emit('download', version)
}
</script>

<style scoped>
.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-6) 0;
}

.versions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.pagination { display: flex; align-items: center; justify-content: center; gap: var(--spacing-3); margin-top: var(--spacing-6); }
.page-info { color: var(--color-text-3); }
</style>

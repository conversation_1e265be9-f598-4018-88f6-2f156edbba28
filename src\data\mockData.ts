/**
 * 统一的示例数据定义
 * 
 * 整合所有分散在各个组件中的硬编码数据，
 * 提供一致性的数据源
 */

import type { 
  ProjectItem, 
  ReleaseItem, 
  UserItem
} from '@/types/domain'
import type {
  SystemStats,
  DeveloperStats,
  Activity,
  DeveloperInfo,
  SoftwareType,
  Version,
  ProjectSummary
} from './types'

// ===== 基础数据生成工具 =====
function daysAgo(days: number): number {
  return Date.now() - days * 24 * 60 * 60 * 1000
}

function hoursAgo(hours: number): number {
  return Date.now() - hours * 60 * 60 * 1000
}

// ===== 项目数据 =====
export const mockProjects: ProjectItem[] = [
  {
    id: 1,
    name: '智能家居控制系统',
    description: '基于ESP32的智能家居控制固件，支持Wi-Fi和蓝牙连接，可控制灯光、温度、安防等设备',
    status: 'active',
    createdAt: daysAgo(30),
    updatedAt: daysAgo(2)
  },
  {
    id: 2,
    name: '传感器数据采集固件',
    description: '多传感器数据采集与处理系统，支持温湿度、光照、气压等多种传感器',
    status: 'active',
    createdAt: daysAgo(20),
    updatedAt: daysAgo(5)
  },
  {
    id: 3,
    name: '设备管理工具',
    description: '用于设备固件更新和配置的PC应用程序，提供可视化的设备管理界面',
    status: 'inactive',
    createdAt: daysAgo(10),
    updatedAt: daysAgo(10)
  },
  {
    id: 4,
    name: '物联网网关固件',
    description: '支持多种通信协议的物联网网关固件，包括MQTT、HTTP、CoAP等',
    status: 'active',
    createdAt: daysAgo(15),
    updatedAt: daysAgo(1)
  },
  {
    id: 5,
    name: '移动端监控APP',
    description: 'Android和iOS移动端监控应用，实时查看设备状态和数据',
    status: 'active',
    createdAt: daysAgo(25),
    updatedAt: daysAgo(3)
  },
  {
    id: 6,
    name: '工业控制器固件',
    description: '工业级PLC控制器固件，支持Modbus、CAN总线等工业协议',
    status: 'active',
    createdAt: daysAgo(40),
    updatedAt: daysAgo(7)
  },
  {
    id: 7,
    name: '云平台SDK',
    description: '设备接入云平台的软件开发包，支持多种编程语言',
    status: 'active',
    createdAt: daysAgo(35),
    updatedAt: daysAgo(4)
  },
  {
    id: 8,
    name: '数据可视化面板',
    description: 'Web端数据可视化Dashboard，实时显示设备数据和统计图表',
    status: 'active',
    createdAt: daysAgo(18),
    updatedAt: daysAgo(1)
  }
]

// ===== 软件类型数据 =====
export const mockSoftwareTypes: SoftwareType[] = [
  { id: 101, projectId: 1, name: 'ESP32固件', updatedAt: hoursAgo(2) },
  { id: 102, projectId: 1, name: 'Android APP', updatedAt: hoursAgo(6) },
  { id: 103, projectId: 1, name: 'iOS APP', updatedAt: hoursAgo(12) },
  { id: 201, projectId: 2, name: '主控固件', updatedAt: hoursAgo(4) },
  { id: 202, projectId: 2, name: '配置工具', updatedAt: hoursAgo(8) },
  { id: 301, projectId: 3, name: 'Windows版本', updatedAt: hoursAgo(10) },
  { id: 302, projectId: 3, name: 'macOS版本', updatedAt: hoursAgo(14) },
  { id: 401, projectId: 4, name: '网关固件', updatedAt: hoursAgo(1) },
  { id: 501, projectId: 5, name: 'Android版本', updatedAt: hoursAgo(3) },
  { id: 502, projectId: 5, name: 'iOS版本', updatedAt: hoursAgo(5) }
]

// ===== 版本数据 =====
export const mockVersions: Version[] = [
  // 智能家居控制系统的版本
  {
    id: 1001, softwareTypeId: 101, versionName: 'v2.1.0',
    fileUrl: '/downloads/smart-home-esp32-v2.1.0.bin',
    fileSize: 1024 * 1024 * 2.5, // 2.5MB
    updatedAt: hoursAgo(2),
    description: '添加新的传感器支持和稳定性改进',
    devices: ['ESP32-DevKitC', 'ESP32-WROOM'],
    type: 'stable'
  },
  {
    id: 1002, softwareTypeId: 101, versionName: 'v2.0.3',
    fileUrl: '/downloads/smart-home-esp32-v2.0.3.bin',
    fileSize: 1024 * 1024 * 2.3,
    updatedAt: daysAgo(7),
    description: '修复Wi-Fi连接问题',
    devices: ['ESP32-DevKitC', 'ESP32-WROOM'],
    type: 'stable'
  },
  {
    id: 1003, softwareTypeId: 102, versionName: 'v1.5.2',
    fileUrl: '/downloads/smart-home-android-v1.5.2.apk',
    fileSize: 1024 * 1024 * 15.2,
    updatedAt: hoursAgo(6),
    description: '优化用户界面和增加离线模式',
    devices: ['Android 8.0+'],
    type: 'stable'
  },
  // 传感器数据采集固件的版本
  {
    id: 2001, softwareTypeId: 201, versionName: 'v1.8.1',
    fileUrl: '/downloads/sensor-firmware-v1.8.1.hex',
    fileSize: 1024 * 512, // 512KB
    updatedAt: hoursAgo(4),
    description: '新增气压传感器支持，优化数据传输',
    devices: ['STM32F103', 'STM32F401'],
    type: 'stable'
  },
  {
    id: 2002, softwareTypeId: 202, versionName: 'v2.0.0',
    fileUrl: '/downloads/sensor-config-tool-v2.0.0.exe',
    fileSize: 1024 * 1024 * 8.5,
    updatedAt: hoursAgo(8),
    description: '全新UI设计，支持批量设备配置',
    devices: ['Windows 10+'],
    type: 'stable'
  }
]

// 为软件类型添加最新版本信息
mockSoftwareTypes.forEach(softwareType => {
  const versions = mockVersions.filter(v => v.softwareTypeId === softwareType.id)
  if (versions.length > 0) {
    const latest = versions.sort((a, b) => b.updatedAt - a.updatedAt)[0]
    softwareType.latestVersion = latest
  }
})

// ===== 版本发布数据 =====
export const mockReleases: ReleaseItem[] = [
  {
    id: 1,
    projectId: 1,
    projectName: '智能家居控制系统',
    version: 'v2.1.0',
    changelog: '新增传感器支持：温湿度传感器DHT22、光照传感器BH1750\n优化Wi-Fi连接稳定性\n修复蓝牙配对问题\n改进OTA更新机制',
    createdAt: hoursAgo(2),
    status: 'success',
    size: 1024 * 1024 * 2.5,
    type: 'stable',
    artifact: 'firmware',
    downloadUrl: '/downloads/smart-home-esp32-v2.1.0.bin'
  },
  {
    id: 2,
    projectId: 5,
    projectName: '移动端监控APP',
    version: 'v1.5.2',
    changelog: '全新Material Design界面\n增加离线模式支持\n优化数据加载速度\n修复Android 14兼容性问题',
    createdAt: hoursAgo(6),
    status: 'success',
    size: 1024 * 1024 * 15.2,
    type: 'stable',
    artifact: 'android-demo',
    downloadUrl: '/downloads/smart-home-android-v1.5.2.apk'
  },
  {
    id: 3,
    projectId: 2,
    projectName: '传感器数据采集固件',
    version: 'v1.8.1',
    changelog: '新增气压传感器BMP280支持\n优化数据传输协议\n降低功耗30%\n修复内存泄漏问题',
    createdAt: hoursAgo(4),
    status: 'success',
    size: 1024 * 512,
    type: 'stable',
    artifact: 'firmware',
    downloadUrl: '/downloads/sensor-firmware-v1.8.1.hex'
  },
  {
    id: 4,
    projectId: 7,
    projectName: '云平台SDK',
    version: 'v3.2.0',
    changelog: '支持新的消息推送功能\n增加Python 3.11支持\n优化JavaScript SDK性能\n新增设备批量管理API',
    createdAt: daysAgo(1),
    status: 'success',
    size: 1024 * 1024 * 1.2,
    type: 'stable',
    artifact: 'sdk',
    downloadUrl: '/downloads/cloud-sdk-v3.2.0.zip'
  },
  {
    id: 5,
    projectId: 4,
    projectName: '物联网网关固件',
    version: 'v1.6.3',
    changelog: '修复MQTT重连问题\n优化CoAP协议支持\n增加设备状态监控\n提升系统稳定性',
    createdAt: daysAgo(1),
    status: 'success',
    size: 1024 * 1024 * 3.8,
    type: 'stable',
    artifact: 'firmware',
    downloadUrl: '/downloads/gateway-firmware-v1.6.3.bin'
  },
  {
    id: 6,
    projectId: 1,
    projectName: '智能家居控制系统',
    version: 'v2.1.0-beta.2',
    changelog: '测试版本：新功能预览\n实验性语音控制功能\n新的设备联动逻辑\n注意：仅供测试使用',
    createdAt: daysAgo(3),
    status: 'success',
    size: 1024 * 1024 * 2.7,
    type: 'beta',
    artifact: 'firmware',
    downloadUrl: '/downloads/smart-home-esp32-v2.1.0-beta.2.bin'
  }
]

// ===== 开发者数据 =====
export const mockDevelopers: DeveloperInfo[] = [
  {
    id: 1,
    name: '张三',
    username: 'zhangsan',
    email: '<EMAIL>',
    userType: 'developer',
    projectCount: 3,
    createdAt: daysAgo(30),
    permissions: ['project_read', 'project_write', 'version_publish'],
    projects: ['1', '2', '5'],
    assignedProjects: [
      {
        id: 1, name: '智能家居控制系统', type: 'firmware',
        assignedAt: daysAgo(30), permissions: ['read', 'write', 'publish']
      },
      {
        id: 2, name: '传感器数据采集固件', type: 'firmware',
        assignedAt: daysAgo(25), permissions: ['read', 'write']
      },
      {
        id: 5, name: '移动端监控APP', type: 'app',
        assignedAt: daysAgo(20), permissions: ['read', 'write', 'publish']
      }
    ]
  },
  {
    id: 2,
    name: '李四',
    username: 'lisi',
    email: '<EMAIL>',
    userType: 'developer',
    projectCount: 2,
    createdAt: daysAgo(20),
    permissions: ['project_read', 'project_write'],
    projects: ['4', '6'],
    assignedProjects: [
      {
        id: 4, name: '物联网网关固件', type: 'firmware',
        assignedAt: daysAgo(15), permissions: ['read', 'write', 'publish']
      },
      {
        id: 6, name: '工业控制器固件', type: 'firmware',
        assignedAt: daysAgo(12), permissions: ['read', 'write']
      }
    ]
  },
  {
    id: 3,
    name: '王五',
    username: 'wangwu',
    email: '<EMAIL>',
    userType: 'developer',
    projectCount: 0,
    createdAt: daysAgo(10),
    permissions: ['project_read'],
    projects: [],
    assignedProjects: []
  },
  {
    id: 4,
    name: '赵六',
    username: 'zhaoliu',
    email: '<EMAIL>',
    userType: 'developer',
    projectCount: 2,
    createdAt: daysAgo(45),
    permissions: ['project_read', 'project_write', 'version_publish'],
    projects: ['7', '8'],
    assignedProjects: [
      {
        id: 7, name: '云平台SDK', type: 'library',
        assignedAt: daysAgo(40), permissions: ['read', 'write', 'publish']
      },
      {
        id: 8, name: '数据可视化面板', type: 'app',
        assignedAt: daysAgo(18), permissions: ['read', 'write', 'publish']
      }
    ]
  }
]

// ===== 用户数据 =====
export const mockUsers: UserItem[] = [
  ...mockDevelopers.map(dev => ({
    id: dev.id as number,
    name: dev.name,
    username: dev.username,
    email: dev.email,
    roles: ['developer'],
    status: 'active' as const
  })),
  {
    id: 100 as number,
    name: '超级管理员',
    username: 'admin',
    email: '<EMAIL>',
    roles: ['admin', 'super'],
    status: 'active'
  },
  {
    id: 101 as number,
    name: '系统管理员',
    username: 'system',
    email: '<EMAIL>',
    roles: ['admin'],
    status: 'active'
  }
]

// ===== 活动记录 =====
export const mockActivities: Activity[] = [
  {
    id: 1,
    type: 'version_published',
    description: '发布了智能家居控制系统 v2.1.0',
    timestamp: hoursAgo(2),
    projectId: 1,
    projectName: '智能家居控制系统',
    userId: 1,
    userName: '张三',
    actionable: true
  },
  {
    id: 2,
    type: 'project_created',
    description: '创建了新项目：数据可视化面板',
    timestamp: daysAgo(1),
    projectId: 8,
    projectName: '数据可视化面板',
    userId: 4,
    userName: '赵六',
    actionable: true
  },
  {
    id: 3,
    type: 'permission_granted',
    description: '为李四分配了"物联网网关固件"的管理权限',
    timestamp: daysAgo(2),
    projectId: 4,
    projectName: '物联网网关固件',
    userId: 2,
    userName: '李四',
    actionable: false
  },
  {
    id: 4,
    type: 'version_uploaded',
    description: '上传了传感器数据采集固件 v1.8.1',
    timestamp: hoursAgo(4),
    projectId: 2,
    projectName: '传感器数据采集固件',
    userId: 1,
    userName: '张三',
    actionable: true
  },
  {
    id: 5,
    type: 'developer_created',
    description: '创建了开发者账号：王五',
    timestamp: daysAgo(10),
    userId: 3,
    userName: '王五',
    actionable: false
  }
]

// ===== 统计数据 =====
export const mockSystemStats: SystemStats = {
  totalDevelopers: mockDevelopers.length,
  totalProjects: mockProjects.length,
  totalReleases: mockReleases.length,
  totalActivities: mockActivities.length,
  activeDevelopersCount: mockDevelopers.filter(d => d.projectCount > 0).length,
  assignedProjectsCount: mockProjects.filter(p => p.status === 'active').length,
  totalPermissionsCount: mockDevelopers.reduce((sum, d) => sum + d.assignedProjects.length, 0),
  developersGrowth: 2,
  projectsGrowth: 1,
  releasesGrowth: 3,
  activitiesGrowth: 8
}

// 获取开发者统计数据
export function getMockDeveloperStats(developerId: number): DeveloperStats {
  const developer = mockDevelopers.find(d => d.id === developerId)
  if (!developer) {
    return {
      myProjectsCount: 0,
      totalReleases: 0,
      newProjectsThisMonth: 0,
      releasesThisMonth: 0,
      activitiesThisMonth: 0
    }
  }

  const myReleases = mockReleases.filter(r => 
    developer.projects?.includes(String(r.projectId))
  )

  const thisMonth = Date.now() - 30 * 24 * 60 * 60 * 1000
  const recentReleases = myReleases.filter(r => r.createdAt > thisMonth)
  const recentActivities = mockActivities.filter(a => 
    a.userId === developerId && a.timestamp > thisMonth
  )

  return {
    myProjectsCount: developer.projectCount,
    totalReleases: myReleases.length,
    newProjectsThisMonth: 0,
    releasesThisMonth: recentReleases.length,
    activitiesThisMonth: recentActivities.length
  }
}

// 生成项目概览数据
export const mockProjectSummaries: ProjectSummary[] = mockProjects.map(project => {
  const projectReleases = mockReleases.filter(r => r.projectId === project.id)
  const latestRelease = projectReleases.length > 0 
    ? projectReleases.sort((a, b) => b.createdAt - a.createdAt)[0]
    : undefined

  // 简单的项目类型推断
  let type: 'firmware' | 'app' | 'library' = 'firmware'
  if (project.name.includes('APP') || project.name.includes('应用') || project.name.includes('面板')) {
    type = 'app'
  } else if (project.name.includes('SDK') || project.name.includes('库')) {
    type = 'library'
  }

  return {
    ...project,
    type,
    releaseCount: projectReleases.length,
    latestRelease: latestRelease ? {
      version: latestRelease.version,
      releaseTime: latestRelease.createdAt
    } : undefined
  }
})

// 导出所有数据
export const mockData = {
  projects: mockProjects,
  softwareTypes: mockSoftwareTypes,
  versions: mockVersions,
  releases: mockReleases,
  developers: mockDevelopers,
  users: mockUsers,
  activities: mockActivities,
  systemStats: mockSystemStats,
  projectSummaries: mockProjectSummaries
}

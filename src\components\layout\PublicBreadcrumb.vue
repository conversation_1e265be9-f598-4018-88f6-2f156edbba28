<template>
  <nav class="public-breadcrumb" aria-label="页面路径导航">
    <ol class="breadcrumb-list">
      <li 
        v-for="(item, index) in breadcrumbItems" 
        :key="index"
        class="breadcrumb-item"
        :class="{ 'is-last': index === breadcrumbItems.length - 1 }"
      >
        <!-- 可点击的面包屑项 -->
        <RouterLink 
          v-if="item.to && index !== breadcrumbItems.length - 1"
          :to="item.to"
          class="breadcrumb-link"
        >
          <BaseIcon 
            v-if="item.icon && index === 0" 
            :name="item.icon" 
            size="14" 
            class="breadcrumb-icon"
          />
          <span>{{ item.label }}</span>
        </RouterLink>
        
        <!-- 当前页面（不可点击） -->
        <span v-else class="breadcrumb-current">
          <BaseIcon 
            v-if="item.icon && index === 0" 
            :name="item.icon" 
            size="14" 
            class="breadcrumb-icon"
          />
          <span>{{ item.label }}</span>
        </span>

        <!-- 分隔符 -->
        <BaseIcon 
          v-if="index !== breadcrumbItems.length - 1"
          name="chevron-right"
          size="12"
          class="breadcrumb-separator"
        />
      </li>
    </ol>

    <!-- 页面操作按钮（可选） -->
    <div v-if="showActions" class="breadcrumb-actions">
      <slot name="actions"></slot>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import BaseIcon from '@base/atoms/BaseIcon.vue'

interface BreadcrumbItem {
  label: string
  to?: string
  icon?: string
}

interface Props {
  items?: BreadcrumbItem[]
  showActions?: boolean
  maxItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  items: undefined,
  showActions: false,
  maxItems: 5
})

const route = useRoute()

// 获取面包屑数据
const breadcrumbItems = computed(() => {
  // 如果传入了自定义items，使用自定义的
  if (props.items && props.items.length > 0) {
    return props.items
  }

  // 否则从路由meta中获取
  const routeBreadcrumb = route.meta.breadcrumb || []
  const items: BreadcrumbItem[] = routeBreadcrumb.map((item, index) => {
    // 处理路径参数替换
    let resolvedPath = item.path
    if (resolvedPath && resolvedPath.includes(':')) {
      // 替换路径参数
      resolvedPath = resolvedPath.replace(/:(\w+)/g, (match, paramName) => {
        const paramValue = route.params[paramName]
        return Array.isArray(paramValue) ? paramValue[0] : (paramValue || match)
      })
    }
    
    return {
      label: item.name,
      to: resolvedPath,
      icon: index === 0 ? 'home' : undefined
    }
  })

  // 限制最大显示数量
  if (items.length > props.maxItems) {
    const firstItem = items[0]
    const lastItems = items.slice(-(props.maxItems - 2))
    return [
      firstItem,
      { label: '...', to: undefined },
      ...lastItems
    ]
  }

  return items
})
</script>

<style scoped>
.public-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  margin-bottom: var(--spacing-2);
  min-height: 32px;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-1);
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  max-width: 200px;
}

.breadcrumb-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  text-decoration: none;
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.breadcrumb-link:hover {
  background: var(--bg-2);
  color: var(--color-primary);
}

.breadcrumb-current {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.breadcrumb-icon {
  flex-shrink: 0;
  color: inherit;
}

.breadcrumb-separator {
  color: var(--color-text-4);
  flex-shrink: 0;
  margin: 0 var(--spacing-1);
}

.breadcrumb-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-left: var(--spacing-3);
}

/* 当面包屑过长时的处理 */
.breadcrumb-item.is-last {
  flex-shrink: 0;
  max-width: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .public-breadcrumb {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .breadcrumb-list {
    width: 100%;
  }

  .breadcrumb-item {
    max-width: 150px;
  }

  .breadcrumb-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .public-breadcrumb {
    padding: var(--spacing-2) 0;
  }

  .breadcrumb-item {
    max-width: 120px;
  }

  .breadcrumb-link,
  .breadcrumb-current {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1);
  }

  .breadcrumb-separator {
    margin: 0;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .breadcrumb-link:hover {
  background: var(--bg-3);
}

/* 动画效果 */
.breadcrumb-item {
  animation: breadcrumbFadeIn 0.3s ease-out;
}

@keyframes breadcrumbFadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 可访问性增强 */
.breadcrumb-link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.breadcrumb-link:focus:not(:focus-visible) {
  outline: none;
}

/* 打印样式 */
@media print {
  .public-breadcrumb {
    border-bottom: 1px solid #ccc;
    padding-bottom: var(--spacing-2);
  }

  .breadcrumb-actions {
    display: none;
  }
}
</style>

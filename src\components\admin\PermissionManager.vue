<template>
  <div class="permission-manager">
    <div class="section-header">
      <h2 class="section-title">权限分配管理</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="showAssignModal = true">
          分配权限
        </button>
      </div>
    </div>

    <!-- 权限概览 -->
    <div class="permissions-overview">
      <div class="overview-card card">
        <h3>权限统计</h3>
        <div class="stats-row">
          <div class="stat-item">
            <span class="stat-label">活跃开发者：</span>
            <span class="stat-value">{{ activeDevelopersCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已分配项目：</span>
            <span class="stat-value">{{ assignedProjectsCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">权限分配数：</span>
            <span class="stat-value">{{ totalPermissionsCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filters-bar">
      <div class="search-box">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜索开发者或项目..." 
          class="search-input"
          @input="handleSearch"
        >
      </div>
      <div class="filter-options">
        <select v-model="viewMode" class="filter-select" @change="handleViewModeChange">
          <option value="by-developer">按开发者查看</option>
          <option value="by-project">按项目查看</option>
        </select>
      </div>
    </div>

    <!-- 权限列表 - 按开发者 -->
    <div v-if="viewMode === 'by-developer'" class="permissions-by-developer">
      <div v-if="loading" class="loading-state">
        <p>正在加载权限数据...</p>
      </div>
      <div v-else-if="filteredDevelopers.length === 0" class="empty-state">
        <p>暂无符合条件的开发者</p>
      </div>
      <div v-else class="developers-list">
        <div 
          v-for="developer in filteredDevelopers" 
          :key="developer.id"
          class="developer-card card"
        >
          <div class="developer-header">
            <div class="developer-info">
              <h3 class="developer-name">{{ developer.name }}</h3>
              <span class="developer-username">@{{ developer.username }}</span>
            </div>
            <div class="developer-stats">
              <span class="project-count">{{ developer.assignedProjects.length }} 个项目</span>
            </div>
          </div>

          <div class="assigned-projects">
            <div v-if="developer.assignedProjects.length === 0" class="no-projects">
              <p>暂未分配任何项目</p>
              <button 
                class="btn btn-sm btn-primary" 
                @click="assignProjectToDeveloper(developer)"
              >
                分配项目
              </button>
            </div>
            <div v-else class="projects-list">
              <div 
                v-for="project in developer.assignedProjects" 
                :key="project.id"
                class="project-item"
              >
                <div class="project-info">
                  <span class="project-name">{{ project.name }}</span>
                  <span :class="['project-type', project.type]">
                    {{ getProjectTypeLabel(project.type) }}
                  </span>
                </div>
                <div class="project-actions">
                  <span class="assignment-time">
                    {{ formatTime(project.assignedAt) }}
                  </span>
                  <button 
                    class="btn btn-sm btn-outline btn-danger" 
                    @click="revokePermission(developer.id, project.id)"
                  >
                    取消权限
                  </button>
                </div>
              </div>
              <div class="add-project-action">
                <button 
                  class="btn btn-sm btn-outline" 
                  @click="assignProjectToDeveloper(developer)"
                >
                  + 分配更多项目
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限列表 - 按项目 -->
    <div v-else class="permissions-by-project">
      <div v-if="loading" class="loading-state">
        <p>正在加载权限数据...</p>
      </div>
      <div v-else-if="filteredProjects.length === 0" class="empty-state">
        <p>暂无符合条件的项目</p>
      </div>
      <div v-else class="projects-list">
        <div 
          v-for="project in filteredProjects" 
          :key="project.id"
          class="project-card card"
        >
          <div class="project-header">
            <div class="project-info">
              <h3 class="project-name">{{ project.name }}</h3>
              <span :class="['project-type', project.type]">
                {{ getProjectTypeLabel(project.type) }}
              </span>
              <span :class="['status-badge', project.status]">
                {{ project.status === 'active' ? '活跃' : '暂停' }}
              </span>
            </div>
            <div class="project-stats">
              <span class="developer-count">{{ project.assignedDevelopers.length }} 个开发者</span>
            </div>
          </div>

          <div class="assigned-developers">
            <div v-if="project.assignedDevelopers.length === 0" class="no-developers">
              <p>暂未分配任何开发者</p>
              <button 
                class="btn btn-sm btn-primary" 
                @click="assignDeveloperToProject(project)"
              >
                分配开发者
              </button>
            </div>
            <div v-else class="developers-list">
              <div 
                v-for="developer in project.assignedDevelopers" 
                :key="developer.id"
                class="developer-item"
              >
                <div class="developer-info">
                  <span class="developer-name">{{ developer.name }}</span>
                  <span class="developer-username">@{{ developer.username }}</span>
                </div>
                <div class="developer-actions">
                  <span class="assignment-time">
                    {{ formatTime(developer.assignedAt) }}
                  </span>
                  <button 
                    class="btn btn-sm btn-outline btn-danger" 
                    @click="revokePermission(developer.id, project.id)"
                  >
                    取消权限
                  </button>
                </div>
              </div>
              <div class="add-developer-action">
                <button 
                  class="btn btn-sm btn-outline" 
                  @click="assignDeveloperToProject(project)"
                >
                  + 分配更多开发者
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分配权限弹窗 -->
    <div v-if="showAssignModal" class="modal-overlay" @click="closeAssignModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>分配项目权限</h3>
          <button class="modal-close" @click="closeAssignModal">×</button>
        </div>
        <div class="assign-form">
          <div class="form-group">
            <label class="form-label">选择开发者 *</label>
            <select v-model="assignForm.developerId" class="form-select" required>
              <option value="">请选择开发者</option>
              <option 
                v-for="developer in availableDevelopers" 
                :key="developer.id"
                :value="developer.id"
              >
                {{ developer.name }} (@{{ developer.username }})
              </option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">选择项目 *</label>
            <div class="projects-selection">
              <div 
                v-for="project in availableProjects" 
                :key="project.id"
                class="project-option"
              >
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    :value="project.id"
                    v-model="assignForm.projectIds"
                  >
                  <span class="checkbox-text">
                    {{ project.name }}
                    <span :class="['project-type-small', project.type]">
                      {{ getProjectTypeLabel(project.type) }}
                    </span>
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div v-if="assignError" class="error-message">
            {{ assignError }}
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeAssignModal">取消</button>
            <button 
              type="button" 
              class="btn btn-primary" 
              @click="confirmAssign"
              :disabled="assigning || !assignForm.developerId || assignForm.projectIds.length === 0"
            >
              {{ assigning ? '分配中...' : '确认分配' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

interface Developer {
  id: number
  username: string
  name: string
  assignedProjects: ProjectPermission[]
}

interface Project {
  id: number
  name: string
  type: 'firmware' | 'app' | 'library'
  status: 'active' | 'inactive'
  assignedDevelopers: DeveloperPermission[]
}

interface ProjectPermission {
  id: number
  name: string
  type: 'firmware' | 'app' | 'library'
  assignedAt: number
}

interface DeveloperPermission {
  id: number
  username: string
  name: string
  assignedAt: number
}

interface AssignForm {
  developerId: number | string
  projectIds: number[]
}

// 响应式数据
const loading = ref(false)
const assigning = ref(false)
const developers = ref<Developer[]>([])
const projects = ref<Project[]>([])
const searchQuery = ref('')
const viewMode = ref<'by-developer' | 'by-project'>('by-developer')

// 弹窗状态
const showAssignModal = ref(false)
const assignForm = reactive<AssignForm>({
  developerId: '',
  projectIds: []
})
const assignError = ref('')

// 计算属性
const activeDevelopersCount = computed(() => {
  return developers.value.length
})

const assignedProjectsCount = computed(() => {
  return projects.value.filter(p => p.assignedDevelopers.length > 0).length
})

const totalPermissionsCount = computed(() => {
  return developers.value.reduce((sum, dev) => sum + dev.assignedProjects.length, 0)
})

const filteredDevelopers = computed(() => {
  if (!searchQuery.value) return developers.value
  
  const query = searchQuery.value.toLowerCase()
  return developers.value.filter(dev =>
    dev.name.toLowerCase().includes(query) ||
    dev.username.toLowerCase().includes(query) ||
    dev.assignedProjects.some(p => p.name.toLowerCase().includes(query))
  )
})

const filteredProjects = computed(() => {
  if (!searchQuery.value) return projects.value
  
  const query = searchQuery.value.toLowerCase()
  return projects.value.filter(proj =>
    proj.name.toLowerCase().includes(query) ||
    proj.assignedDevelopers.some(d => 
      d.name.toLowerCase().includes(query) || 
      d.username.toLowerCase().includes(query)
    )
  )
})

const availableDevelopers = computed(() => {
  return developers.value
})

const availableProjects = computed(() => {
  return projects.value.filter(p => p.status === 'active')
})

// 项目类型标签
function getProjectTypeLabel(type: string) {
  const typeMap: Record<string, string> = {
    'firmware': '固件',
    'app': '应用程序',
    'library': '库文件'
  }
  return typeMap[type] || type
}

// 时间格式化
function formatTime(timestamp: number) {
  const now = Date.now()
  const diff = now - timestamp
  const days = Math.floor(diff / 86400000)
  
  if (days === 0) return '今天分配'
  if (days === 1) return '昨天分配'
  if (days < 7) return `${days}天前分配`
  return new Date(timestamp).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
}

// 搜索处理
function handleSearch() {
  // 实时搜索，无需额外处理
}

// 视图模式切换
function handleViewModeChange() {
  searchQuery.value = ''
}

// 加载权限数据
async function loadPermissions() {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    developers.value = [
      {
        id: 1,
        username: 'zhangsan',
        name: '张三',
        assignedProjects: [
          { id: 1, name: '智能家居控制系统', type: 'firmware', assignedAt: Date.now() - 86400000 * 5 },
          { id: 2, name: '传感器数据采集', type: 'firmware', assignedAt: Date.now() - 86400000 * 10 }
        ]
      },
      {
        id: 2,
        username: 'lisi',
        name: '李四',
        assignedProjects: [
          { id: 1, name: '智能家居控制系统', type: 'firmware', assignedAt: Date.now() - 86400000 * 3 }
        ]
      },
      {
        id: 3,
        username: 'wangwu',
        name: '王五',
        assignedProjects: []
      }
    ]

    projects.value = [
      {
        id: 1,
        name: '智能家居控制系统',
        type: 'firmware',
        status: 'active',
        assignedDevelopers: [
          { id: 1, username: 'zhangsan', name: '张三', assignedAt: Date.now() - 86400000 * 5 },
          { id: 2, username: 'lisi', name: '李四', assignedAt: Date.now() - 86400000 * 3 }
        ]
      },
      {
        id: 2,
        name: '传感器数据采集',
        type: 'firmware',
        status: 'active',
        assignedDevelopers: [
          { id: 1, username: 'zhangsan', name: '张三', assignedAt: Date.now() - 86400000 * 10 }
        ]
      },
      {
        id: 3,
        name: '设备管理工具',
        type: 'app',
        status: 'active',
        assignedDevelopers: []
      }
    ]
  } catch (error) {
    console.error('加载权限数据失败:', error)
  } finally {
    loading.value = false
  }
}


// 为开发者分配项目
function assignProjectToDeveloper(developer: Developer) {
  assignForm.developerId = developer.id
  assignForm.projectIds = []
  showAssignModal.value = true
}

// 为项目分配开发者
function assignDeveloperToProject(project: Project) {
  assignForm.developerId = ''
  assignForm.projectIds = [project.id]
  showAssignModal.value = true
}

// 取消权限
async function revokePermission(developerId: number, projectId: number) {
  if (!confirm('确定要取消该权限分配吗？')) return
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新开发者数据
    const developer = developers.value.find(d => d.id === developerId)
    if (developer) {
      const projectIndex = developer.assignedProjects.findIndex(p => p.id === projectId)
      if (projectIndex > -1) {
        developer.assignedProjects.splice(projectIndex, 1)
      }
    }
    
    // 更新项目数据
    const project = projects.value.find(p => p.id === projectId)
    if (project) {
      const developerIndex = project.assignedDevelopers.findIndex(d => d.id === developerId)
      if (developerIndex > -1) {
        project.assignedDevelopers.splice(developerIndex, 1)
      }
    }
  } catch (error) {
    console.error('取消权限失败:', error)
  }
}

// 关闭分配弹窗
function closeAssignModal() {
  showAssignModal.value = false
  assignError.value = ''
  assignForm.developerId = ''
  assignForm.projectIds = []
}

// 确认分配权限
async function confirmAssign() {
  assignError.value = ''
  
  if (!assignForm.developerId || assignForm.projectIds.length === 0) {
    assignError.value = '请选择开发者和项目'
    return
  }
  
  assigning.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const developer = developers.value.find(d => d.id === assignForm.developerId)
    if (!developer) return
    
    const now = Date.now()
    
    // 为每个选中的项目分配权限
    for (const projectId of assignForm.projectIds) {
      const project = projects.value.find(p => p.id === projectId)
      if (!project) continue
      
      // 检查是否已经分配
      const alreadyAssigned = developer.assignedProjects.some(p => p.id === projectId)
      if (alreadyAssigned) continue
      
      // 添加到开发者的项目列表
      developer.assignedProjects.push({
        id: project.id,
        name: project.name,
        type: project.type,
        assignedAt: now
      })
      
      // 添加到项目的开发者列表
      const alreadyInProject = project.assignedDevelopers.some(d => d.id === developer.id)
      if (!alreadyInProject) {
        project.assignedDevelopers.push({
          id: developer.id,
          username: developer.username,
          name: developer.name,
          assignedAt: now
        })
      }
    }
    
    closeAssignModal()
  } catch (error) {
    console.error('分配权限失败:', error)
    assignError.value = '分配失败，请重试'
  } finally {
    assigning.value = false
  }
}

// 初始化
onMounted(() => {
  loadPermissions()
})
</script>

<style scoped>
.permission-manager {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 权限概览 */
.permissions-overview {
  margin-bottom: var(--spacing-6);
}

.overview-card {
  padding: var(--spacing-5);
}

.overview-card h3 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-lg);
  color: var(--color-text-1);
}

.stats-row {
  display: flex;
  gap: var(--spacing-6);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

/* 搜索和过滤栏 */
.filters-bar {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.filter-select {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
  min-width: 150px;
}

/* 通用状态 */
.loading-state,
.empty-state {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-8);
  font-size: var(--font-size-lg);
}

/* 状态标签 */
.status-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.small {
  padding: 2px 6px;
  font-size: 10px;
}

.status-badge.active {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-badge.inactive {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
}

/* 按开发者查看 */
.developers-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.developer-card {
  padding: var(--spacing-5);
  border: 1px solid var(--border-color);
}

.developer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.developer-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.developer-name {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
}

.developer-username {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.developer-stats {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.project-count {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.no-projects {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-4);
}

.no-projects p {
  margin-bottom: var(--spacing-3);
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--bg-2);
  border-radius: var(--radius-md);
}

.project-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.project-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-1);
}

.project-type {
  display: inline-block;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
}

.project-type.firmware {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.project-type.app {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.project-type.library {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.project-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.assignment-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
}

.add-project-action {
  padding: var(--spacing-3);
  text-align: center;
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-4);
}

/* 按项目查看 */
.projects-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.project-card {
  padding: var(--spacing-5);
  border: 1px solid var(--border-color);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.project-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.project-stats {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.developer-count {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.no-developers {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-4);
}

.no-developers p {
  margin-bottom: var(--spacing-3);
}

.developer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--bg-2);
  border-radius: var(--radius-md);
}

.add-developer-action {
  padding: var(--spacing-3);
  text-align: center;
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-4);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 560px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.assign-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-select {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.projects-selection {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-2);
}

.project-option {
  padding: var(--spacing-2);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
}

.checkbox-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.project-type-small {
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
}

.project-type-small.firmware {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.project-type-small.app {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.project-type-small.library {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .filters-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
  
  .stats-row {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .developer-header,
  .project-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .project-item,
  .developer-item {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
  }
  
  .project-actions,
  .developer-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>

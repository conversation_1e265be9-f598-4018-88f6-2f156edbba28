{"name": "sw_publish_qian", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.11.0", "element-plus": "^2.11.2", "pinia": "^3.0.3", "sass": "^1.92.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/lodash-es": "^4.17.12", "@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "c8": "^10.1.3", "dayjs": "^1.11.18", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "husky": "^9.1.7", "lint-staged": "^16.1.6", "lodash-es": "^4.17.21", "prettier": "^3.6.2", "typescript": "~5.8.3", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.2", "vitest": "^3.2.4", "vue-tsc": "^3.0.5"}}
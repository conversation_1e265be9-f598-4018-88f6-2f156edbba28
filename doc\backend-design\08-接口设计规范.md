# 接口设计规范

## 🔌 API设计原则

### 1. RESTful设计原则

#### 1.1 REST约束条件

**统一接口约束**
- **资源标识**: 使用URI唯一标识资源
- **资源操作**: 通过HTTP方法操作资源
- **自描述消息**: 消息包含足够的信息描述如何处理
- **超媒体驱动**: 通过超链接导航应用状态

**无状态约束**
- 每个请求必须包含足够的信息
- 服务器不保存客户端状态
- 提高系统可扩展性和可靠性

#### 1.2 资源设计原则

**资源命名规范**
```http
# 正确的资源命名
GET    /api/projects              # 获取项目列表
GET    /api/projects/{id}         # 获取特定项目
POST   /api/projects              # 创建新项目
PUT    /api/projects/{id}         # 更新整个项目
PATCH  /api/projects/{id}         # 部分更新项目
DELETE /api/projects/{id}         # 删除项目

# 嵌套资源
GET    /api/projects/{id}/releases          # 获取项目的版本列表
POST   /api/projects/{id}/releases          # 在项目下创建新版本
GET    /api/projects/{id}/releases/{vid}    # 获取特定版本
PUT    /api/projects/{id}/releases/{vid}    # 更新版本
DELETE /api/projects/{id}/releases/{vid}    # 删除版本
```

**HTTP方法语义**
| 方法 | 语义 | 幂等性 | 安全性 | 用途 |
|------|------|---------|---------|------|
| GET | 获取资源 | ✓ | ✓ | 查询操作 |
| POST | 创建资源 | ✗ | ✗ | 创建操作 |
| PUT | 更新/创建资源 | ✓ | ✗ | 完整更新 |
| PATCH | 部分更新资源 | ✗ | ✗ | 部分更新 |
| DELETE | 删除资源 | ✓ | ✗ | 删除操作 |
| HEAD | 获取资源头信息 | ✓ | ✓ | 检查资源 |
| OPTIONS | 获取资源支持的方法 | ✓ | ✓ | 协商能力 |

### 2. URL设计规范

#### 2.1 URL结构设计

**URL结构模式**
```
https://{domain}/api/{version}/{resource}/{id}/{sub-resource}
```

**具体示例**
```http
# 基础API结构
https://sw-publish.ggec.com/api/v1/projects
https://sw-publish.ggec.com/api/v1/projects/123
https://sw-publish.ggec.com/api/v1/projects/123/releases

# 认证相关
https://sw-publish.ggec.com/api/v1/auth/login
https://sw-publish.ggec.com/api/v1/auth/logout
https://sw-publish.ggec.com/api/v1/auth/refresh

# 用户管理
https://sw-publish.ggec.com/api/v1/users
https://sw-publish.ggec.com/api/v1/users/123/permissions

# 公共接口
https://sw-publish.ggec.com/api/v1/public/projects
https://sw-publish.ggec.com/api/v1/public/releases/search
```

#### 2.2 URL命名约定

**命名规则**
- 使用名词复数形式表示资源集合
- 使用连字符（-）分隔单词，避免下划线
- 使用小写字母
- 避免在URL中使用动词

```http
# 正确示例
GET /api/v1/user-projects
GET /api/v1/software-releases
GET /api/v1/download-logs

# 错误示例
GET /api/v1/getUserProjects     # 包含动词
GET /api/v1/user_projects       # 使用下划线
GET /api/v1/UserProjects        # 使用大写字母
```

#### 2.3 查询参数设计

**分页参数**
```http
GET /api/v1/projects?page=1&size=20&sort=name,asc&sort=createdAt,desc
```

**过滤参数**
```http
GET /api/v1/projects?category=FIRMWARE&status=ACTIVE&keyword=bluetooth
GET /api/v1/releases?projectId=123&versionType=STABLE&deviceType=headphones
```

**字段选择**
```http
GET /api/v1/projects?fields=id,name,description,createdAt
GET /api/v1/releases?include=project&exclude=fileContent
```

### 3. 请求响应格式

#### 3.1 请求格式规范

**Content-Type规范**
```http
# JSON请求
POST /api/v1/projects
Content-Type: application/json

{
  "name": "蓝牙耳机固件",
  "description": "支持降噪功能的蓝牙耳机固件项目",
  "category": "FIRMWARE"
}

# 文件上传
POST /api/v1/projects/123/releases
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="file"; filename="firmware.bin"
Content-Type: application/octet-stream

[文件内容]
--boundary
Content-Disposition: form-data; name="metadata"
Content-Type: application/json

{
  "version": "v1.2.0.0",
  "title": "修复连接稳定性问题",
  "deviceType": "headphones"
}
```

#### 3.2 响应格式规范

**统一响应结构**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": 1640995200000,
  "requestId": "req_123456789"
}
```

**分页响应结构**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "项目1",
        "description": "项目描述"
      }
    ],
    "pagination": {
      "current": 1,
      "size": 20,
      "total": 100,
      "pages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "timestamp": 1640995200000
}
```

**错误响应结构**
```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "name",
        "message": "项目名称不能为空",
        "rejectedValue": ""
      }
    ]
  },
  "timestamp": 1640995200000,
  "requestId": "req_123456789"
}
```

### 4. HTTP状态码规范

#### 4.1 状态码使用指南

**成功状态码 (2xx)**
```http
200 OK              # 请求成功，返回数据
201 Created         # 资源创建成功
202 Accepted        # 请求已接受，异步处理
204 No Content      # 操作成功，无返回内容
```

**重定向状态码 (3xx)**
```http
301 Moved Permanently     # 资源永久重定向
302 Found                # 资源临时重定向
304 Not Modified         # 资源未修改，使用缓存
```

**客户端错误状态码 (4xx)**
```http
400 Bad Request          # 请求参数错误
401 Unauthorized         # 未认证
403 Forbidden           # 认证成功但无权限
404 Not Found           # 资源不存在
405 Method Not Allowed  # HTTP方法不允许
409 Conflict            # 资源冲突
422 Unprocessable Entity # 请求格式正确但语义错误
429 Too Many Requests   # 请求频率限制
```

**服务器错误状态码 (5xx)**
```http
500 Internal Server Error # 服务器内部错误
502 Bad Gateway          # 上游服务错误
503 Service Unavailable  # 服务不可用
504 Gateway Timeout      # 上游服务超时
```

#### 4.2 状态码使用示例

```java
@RestController
@RequestMapping("/api/v1/projects")
public class ProjectController {
    
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponse<ProjectDto>>> getProjects() {
        // 返回 200 OK
        return ResponseEntity.ok(response);
    }
    
    @PostMapping
    public ResponseEntity<ApiResponse<ProjectDto>> createProject(@RequestBody ProjectCreateRequest request) {
        ProjectDto project = projectService.createProject(request);
        // 返回 201 Created
        return ResponseEntity.status(HttpStatus.CREATED).body(success(project));
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ProjectDto>> updateProject(@PathVariable Long id, 
                                                               @RequestBody ProjectUpdateRequest request) {
        ProjectDto project = projectService.updateProject(id, request);
        // 返回 200 OK
        return ResponseEntity.ok(success(project));
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProject(@PathVariable Long id) {
        projectService.deleteProject(id);
        // 返回 204 No Content
        return ResponseEntity.noContent().build();
    }
}
```

### 5. 错误处理规范

#### 5.1 错误码设计

**错误码结构**
```
错误码格式: {模块}{类型}{序号}
- 模块: 01-系统, 02-认证, 03-业务, 04-文件
- 类型: 01-客户端错误, 02-服务器错误
- 序号: 001-999
```

**错误码定义**
```java
public enum ErrorCode {
    // 系统错误 (01xx)
    SYSTEM_ERROR("010201", "系统内部错误"),
    SYSTEM_BUSY("010202", "系统繁忙，请稍后重试"),
    
    // 认证错误 (02xx)
    UNAUTHORIZED("020101", "用户未登录"),
    ACCESS_DENIED("020102", "权限不足"),
    TOKEN_EXPIRED("020103", "Token已过期"),
    INVALID_CREDENTIALS("020104", "用户名或密码错误"),
    
    // 业务错误 (03xx)
    RESOURCE_NOT_FOUND("030101", "资源不存在"),
    RESOURCE_CONFLICT("030102", "资源冲突"),
    BUSINESS_RULE_VIOLATION("030103", "业务规则违反"),
    VALIDATION_ERROR("030104", "参数验证失败"),
    
    // 文件错误 (04xx)
    FILE_NOT_FOUND("040101", "文件不存在"),
    FILE_TOO_LARGE("040102", "文件大小超过限制"),
    INVALID_FILE_TYPE("040103", "不支持的文件类型"),
    FILE_UPLOAD_FAILED("040201", "文件上传失败");
    
    private final String code;
    private final String message;
}
```

#### 5.2 全局异常处理

**异常处理器**
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(ValidationException e) {
        log.warn("参数验证失败: {}", e.getMessage());
        
        ErrorResponse response = ErrorResponse.builder()
            .code(ErrorCode.VALIDATION_ERROR.getCode())
            .message(ErrorCode.VALIDATION_ERROR.getMessage())
            .error(ErrorDetail.builder()
                .type("VALIDATION_ERROR")
                .details(e.getValidationErrors())
                .build())
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
            
        return ResponseEntity.badRequest().body(response);
    }
    
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.warn("资源不存在: {}", e.getMessage());
        
        ErrorResponse response = ErrorResponse.builder()
            .code(ErrorCode.RESOURCE_NOT_FOUND.getCode())
            .message(e.getMessage())
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
            
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponse> handleAccessDeniedException(AccessDeniedException e) {
        log.warn("权限不足: {}", e.getMessage());
        
        ErrorResponse response = ErrorResponse.builder()
            .code(ErrorCode.ACCESS_DENIED.getCode())
            .message("权限不足")
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
            
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGeneralException(Exception e) {
        log.error("系统异常", e);
        
        ErrorResponse response = ErrorResponse.builder()
            .code(ErrorCode.SYSTEM_ERROR.getCode())
            .message("系统内部错误")
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
            
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
```

### 6. 版本控制策略

#### 6.1 API版本化方案

**URL版本化（推荐）**
```http
# 版本在URL路径中
GET /api/v1/projects
GET /api/v2/projects

# 优势：简单直观，易于缓存
# 劣势：URL变化，需要维护多套路由
```

**Header版本化**
```http
GET /api/projects
Accept: application/vnd.swpublish.v1+json

# 优势：URL不变，RESTful纯粹
# 劣势：对缓存不友好，调试困难
```

#### 6.2 版本兼容性策略

**向后兼容原则**
```json
// v1版本响应
{
  "id": 1,
  "name": "项目名称",
  "description": "项目描述"
}

// v2版本响应（向后兼容）
{
  "id": 1,
  "name": "项目名称",
  "description": "项目描述",
  "category": "FIRMWARE",        // 新增字段
  "tags": ["bluetooth", "noise"] // 新增字段
}
```

**版本废弃策略**
```http
# 废弃警告头
Warning: 299 - "API v1 将在2024-06-01废弃，请升级到v2"
Sunset: Sat, 01 Jun 2024 00:00:00 GMT
```

### 7. 请求验证规范

#### 7.1 输入验证

**参数验证注解**
```java
public class ProjectCreateRequest {
    
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 2, max = 100, message = "项目名称长度必须在2-100字符之间")
    @Pattern(regexp = "^[\\w\\s\\-\\u4e00-\\u9fa5]+$", message = "项目名称包含非法字符")
    private String name;
    
    @Size(max = 1000, message = "项目描述不能超过1000字符")
    private String description;
    
    @NotNull(message = "项目分类不能为空")
    @EnumValue(enumClass = ProjectCategory.class, message = "项目分类值无效")
    private String category;
    
    @Valid
    @NotEmpty(message = "项目标签不能为空")
    private List<@NotBlank(message = "标签不能为空") String> tags;
}
```

**自定义验证器**
```java
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumValueValidator.class)
public @interface EnumValue {
    String message() default "枚举值无效";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    Class<? extends Enum<?>> enumClass();
}

public class EnumValueValidator implements ConstraintValidator<EnumValue, String> {
    
    private Class<? extends Enum<?>> enumClass;
    
    @Override
    public void initialize(EnumValue constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 使用@NotNull检查空值
        }
        
        Enum<?>[] enumConstants = enumClass.getEnumConstants();
        for (Enum<?> enumConstant : enumConstants) {
            if (enumConstant.name().equals(value)) {
                return true;
            }
        }
        return false;
    }
}
```

#### 7.2 业务验证

**业务规则验证**
```java
@Component
public class ProjectBusinessValidator {
    
    public void validateProjectCreation(ProjectCreateRequest request, User currentUser) {
        // 检查项目名称唯一性
        if (projectRepository.existsByName(request.getName())) {
            throw new BusinessValidationException("项目名称已存在");
        }
        
        // 检查用户权限
        if (currentUser.getRole() != UserRole.ADMIN) {
            throw new AccessDeniedException("只有管理员可以创建项目");
        }
        
        // 检查项目数量限制
        long projectCount = projectRepository.countByCreatedBy(currentUser.getId());
        if (projectCount >= MAX_PROJECTS_PER_USER) {
            throw new BusinessValidationException("项目数量已达上限");
        }
    }
}
```

### 8. 接口文档规范

#### 8.1 OpenAPI/Swagger配置

**Swagger配置**
```java
@Configuration
@OpenAPIDefinition(
    info = @Info(
        title = "软件发布管理系统API",
        version = "v1.0.0",
        description = "用于管理软件版本发布的RESTful API",
        contact = @Contact(
            name = "开发团队",
            email = "<EMAIL>"
        ),
        license = @License(
            name = "MIT License",
            url = "https://opensource.org/licenses/MIT"
        )
    ),
    servers = {
        @Server(url = "https://sw-publish.company.com", description = "生产环境"),
        @Server(url = "https://sw-publish-test.company.com", description = "测试环境")
    }
)
public class OpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .components(new Components()
                .addSecuritySchemes("bearer-jwt",
                    new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")))
            .addSecurityItem(new SecurityRequirement().addList("bearer-jwt"));
    }
}
```

#### 8.2 接口文档注解

**控制器文档注解**
```java
@RestController
@RequestMapping("/api/v1/projects")
@Tag(name = "项目管理", description = "项目的创建、查询、更新和删除操作")
public class ProjectController {
    
    @GetMapping
    @Operation(
        summary = "获取项目列表",
        description = "分页获取项目列表，支持按分类和关键字筛选",
        responses = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                content = @Content(schema = @Schema(implementation = PageResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
        }
    )
    public ResponseEntity<PageResponse<ProjectDto>> getProjects(
            @Parameter(description = "页码，从1开始", example = "1")
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            
            @Parameter(description = "页大小，最大100", example = "20")
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            
            @Parameter(description = "项目分类", schema = @Schema(allowableValues = {"FIRMWARE", "APP"}))
            @RequestParam(required = false) String category,
            
            @Parameter(description = "搜索关键字")
            @RequestParam(required = false) String keyword) {
        
        return ResponseEntity.ok(projectService.getProjects(page, size, category, keyword));
    }
    
    @PostMapping
    @Operation(
        summary = "创建项目",
        description = "创建新项目，只有管理员可以执行此操作"
    )
    @SecurityRequirement(name = "bearer-jwt")
    public ResponseEntity<ProjectDto> createProject(
            @RequestBody @Valid @Schema(description = "项目创建请求") ProjectCreateRequest request) {
        
        ProjectDto project = projectService.createProject(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(project);
    }
}
```

**DTO文档注解**
```java
@Schema(description = "项目信息")
public class ProjectDto {
    
    @Schema(description = "项目ID", example = "1")
    private Long id;
    
    @Schema(description = "项目名称", example = "蓝牙耳机固件")
    private String name;
    
    @Schema(description = "项目描述", example = "支持降噪功能的蓝牙耳机固件项目")
    private String description;
    
    @Schema(description = "项目分类", allowableValues = {"FIRMWARE", "APP"})
    private String category;
    
    @Schema(description = "项目状态", allowableValues = {"ACTIVE", "PAUSED", "DEPRECATED"})
    private String status;
    
    @Schema(description = "总下载量", example = "1250")
    private Long totalDownloads;
    
    @Schema(description = "创建时间", example = "2024-01-15T10:30:00")
    private LocalDateTime createdAt;
}
```

### 9. 接口测试规范

#### 9.1 单元测试

**Controller测试**
```java
@WebMvcTest(ProjectController.class)
class ProjectControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private ProjectService projectService;
    
    @Test
    @DisplayName("获取项目列表 - 成功")
    void getProjects_Success() throws Exception {
        // Given
        PageResponse<ProjectDto> response = createMockPageResponse();
        when(projectService.getProjects(1, 20, null, null)).thenReturn(response);
        
        // When & Then
        mockMvc.perform(get("/api/v1/projects")
                .param("page", "1")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.pagination.current").value(1))
                .andExpect(jsonPath("$.data.pagination.size").value(20));
    }
    
    @Test
    @DisplayName("创建项目 - 参数验证失败")
    void createProject_ValidationFailed() throws Exception {
        // Given
        ProjectCreateRequest request = new ProjectCreateRequest();
        request.setName(""); // 空名称触发验证失败
        
        // When & Then
        mockMvc.perform(post("/api/v1/projects")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("030104"))
                .andExpect(jsonPath("$.message").value("参数验证失败"));
    }
}
```

#### 9.2 集成测试

**API集成测试**
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
class ProjectIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Test
    @DisplayName("项目完整生命周期测试")
    void projectLifecycleTest() {
        // 1. 创建项目
        ProjectCreateRequest createRequest = ProjectCreateRequest.builder()
            .name("测试项目")
            .description("这是一个测试项目")
            .category("FIRMWARE")
            .build();
            
        ResponseEntity<ProjectDto> createResponse = restTemplate.postForEntity(
            "/api/v1/projects", createRequest, ProjectDto.class);
        
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(createResponse.getBody().getName()).isEqualTo("测试项目");
        
        Long projectId = createResponse.getBody().getId();
        
        // 2. 查询项目
        ResponseEntity<ProjectDto> getResponse = restTemplate.getForEntity(
            "/api/v1/projects/" + projectId, ProjectDto.class);
        
        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(getResponse.getBody().getName()).isEqualTo("测试项目");
        
        // 3. 更新项目
        ProjectUpdateRequest updateRequest = ProjectUpdateRequest.builder()
            .description("更新后的项目描述")
            .build();
            
        restTemplate.put("/api/v1/projects/" + projectId, updateRequest);
        
        // 4. 验证更新
        ResponseEntity<ProjectDto> updatedResponse = restTemplate.getForEntity(
            "/api/v1/projects/" + projectId, ProjectDto.class);
        
        assertThat(updatedResponse.getBody().getDescription()).isEqualTo("更新后的项目描述");
        
        // 5. 删除项目
        restTemplate.delete("/api/v1/projects/" + projectId);
        
        // 6. 验证删除
        ResponseEntity<ProjectDto> deletedResponse = restTemplate.getForEntity(
            "/api/v1/projects/" + projectId, ProjectDto.class);
        
        assertThat(deletedResponse.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }
}
```

这个接口设计规范为API开发提供了全面的设计指导，确保接口的一致性、可维护性和易用性。

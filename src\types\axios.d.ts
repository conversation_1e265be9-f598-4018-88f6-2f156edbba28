import 'axios'
declare module 'axios' {
  export interface AxiosRequestConfig {
    _retry?: boolean
    _retryCount?: number
    _startAt?: number
    _metricName?: string
    _loadingKey?: string
    /** 静默请求：不触发全局错误提示 */
    silent?: boolean
    /** 简单可配置重试 */
    retry?: {
      times?: number
      delayMs?: number
      /** 针对特定HTTP状态码重试，默认对5xx与网络错误重试 */
      retryOn?: number[]
    }
    /** 可选响应缓存（仅GET生效） */
    cache?: {
      enabled?: boolean
      ttlMs?: number
      key?: string
      tags?: string[]
    }
  }
}



/**
 * 统一数据服务类型定义
 */

import type { 
  ProjectItem, 
  ReleaseItem, 
  UserItem, 
  PageResult,
  SearchResultItem,
  SearchPageResult,
  DeveloperUser,
  ReleaseDetail
} from '@/types/domain'

// 统计数据类型
export interface SystemStats {
  totalDevelopers: number
  totalProjects: number
  totalReleases: number
  totalActivities: number
  activeDevelopersCount: number
  assignedProjectsCount: number
  totalPermissionsCount: number
  developersGrowth?: number
  projectsGrowth?: number
  releasesGrowth?: number
  activitiesGrowth?: number
}

// 开发者统计
export interface DeveloperStats {
  myProjectsCount: number
  totalReleases: number
  newProjectsThisMonth?: number
  releasesThisMonth?: number
  activitiesThisMonth?: number
}

// 活动记录
export interface Activity {
  id: number
  type: 'developer_created' | 'developer_deleted' | 'project_created' | 'project_deleted' | 
        'permission_granted' | 'permission_revoked' | 'version_uploaded' | 'version_published'
  description: string
  timestamp: number
  projectId?: number
  projectName?: string
  userId?: number
  userName?: string
  actionable?: boolean
}

// 开发者信息（扩展）
export interface DeveloperInfo extends DeveloperUser {
  projectCount: number
  createdAt: number
  assignedProjects: ProjectAssignment[]
}

// 项目分配信息
export interface ProjectAssignment {
  id: number
  name: string
  type: 'firmware' | 'app' | 'library'
  assignedAt: number
  permissions: string[]
}

// 软件类型
export interface SoftwareType {
  id: number
  projectId: number
  name: string
  latestVersion?: Version
  updatedAt: number
}

// 版本信息
export interface Version {
  id: number
  softwareTypeId: number
  versionName: string
  fileUrl?: string
  fileSize?: number
  updatedAt: number
  description?: string
  devices?: string[]
  type?: 'development' | 'beta' | 'stable' | 'release'
}

// 项目概览（用于首页和开发者概览）
export interface ProjectSummary extends ProjectItem {
  type: 'firmware' | 'app' | 'library'
  releaseCount: number
  latestRelease?: {
    version: string
    releaseTime: number
  }
}

// 查询参数
export interface QueryParams {
  page?: number
  pageSize?: number
  search?: string
  type?: string
  status?: string
  sortBy?: string
  order?: 'asc' | 'desc'
}

// 统一数据服务接口
export interface UnifiedDataService {
  // 系统统计
  getSystemStats(): Promise<SystemStats>
  getDeveloperStats(developerId: number): Promise<DeveloperStats>
  
  // 项目管理
  getProjects(params?: QueryParams): Promise<PageResult<ProjectItem>>
  getProjectDetail(id: number): Promise<ProjectItem>
  createProject(data: Partial<ProjectItem>): Promise<ProjectItem>
  updateProject(id: number, data: Partial<ProjectItem>): Promise<ProjectItem>
  deleteProject(id: number): Promise<void>
  
  // 项目概览
  getProjectSummaries(params?: QueryParams): Promise<ProjectSummary[]>
  
  // 软件类型管理
  getSoftwareTypes(projectId: number): Promise<SoftwareType[]>
  createSoftwareType(projectId: number, data: { name: string }): Promise<SoftwareType>
  deleteSoftwareType(id: number): Promise<void>
  
  // 版本管理
  getVersions(softwareTypeId: number): Promise<Version[]>
  getVersionDetail(id: number): Promise<Version>
  createVersion(data: Partial<Version>): Promise<Version>
  updateVersion(id: number, data: Partial<Version>): Promise<Version>
  deleteVersion(id: number): Promise<void>
  
  // 版本发布
  getReleases(params?: QueryParams): Promise<PageResult<ReleaseItem>>
  getReleaseDetail(id: number): Promise<ReleaseDetail>
  createRelease(data: Partial<ReleaseItem>): Promise<ReleaseItem>
  
  // 开发者管理
  getDevelopers(params?: QueryParams): Promise<PageResult<DeveloperInfo>>
  getDeveloperDetail(id: number): Promise<DeveloperInfo>
  createDeveloper(data: { username: string; password: string }): Promise<DeveloperInfo>
  updateDeveloper(id: number, data: Partial<DeveloperInfo>): Promise<DeveloperInfo>
  deleteDeveloper(id: number): Promise<void>
  
  // 用户管理
  getUsers(params?: QueryParams): Promise<PageResult<UserItem>>
  getUserDetail(id: number): Promise<UserItem>
  disableUser(id: number): Promise<void>
  
  // 活动记录
  getSystemActivities(params?: QueryParams): Promise<Activity[]>
  getDeveloperActivities(developerId: number, params?: QueryParams): Promise<Activity[]>
  
  // 权限管理
  getPermissionAssignments(): Promise<ProjectAssignment[]>
  assignProjectToDeveloper(developerId: number, projectId: number, permissions: string[]): Promise<void>
  revokeProjectFromDeveloper(developerId: number, projectId: number): Promise<void>
  
  // 搜索
  search(query: string, params?: QueryParams): Promise<SearchPageResult>
}

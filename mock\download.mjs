let downloadLog = []

export default [
  {
    method: 'POST',
    path: '/download/authorize',
    handler: async ({ body, headers }) => {
      // 简单校验：如果带 Authorization 就放行；无 token 也放行（公共下载演示）
      const authorized = true
      return { code: 0, message: 'OK', data: { authorized } }
    },
  },
  {
    method: 'POST',
    path: '/download/track',
    handler: async ({ body }) => {
      const payload = body || {}
      downloadLog.push({ ...payload, time: Date.now() })
      // 控制大小
      if (downloadLog.length > 500) downloadLog = downloadLog.slice(-200)
      return { code: 0, message: 'OK', data: true }
    },
  },
]



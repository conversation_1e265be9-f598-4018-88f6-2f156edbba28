import { defineStore } from 'pinia'
import { getDataService } from '@/data'
import type { UserItem } from '@/types/domain'

const dataService = getDataService()

interface QueryState { page: number; pageSize: number }

interface UsersState {
  list: UserItem[]
  total: number
  query: QueryState
  loading: boolean
  error: string | null
  detail: Record<number, UserItem | undefined>
}

export const useUsersStore = defineStore('users', {
  state: (): UsersState => ({
    list: [],
    total: 0,
    query: { page: 1, pageSize: 10 },
    loading: false,
    error: null,
    detail: {},
  }),
  actions: {
    async loadList(params?: Partial<QueryState>) {
      if (params) this.query = { ...this.query, ...params }
      const { withLoading } = await import('@/utils/async')
      await withLoading(this, async () => {
        const res = await dataService.getUsers({
          page: this.query.page,
          pageSize: this.query.pageSize
        })
        this.list = res.list
        this.total = res.total
      })
    },
    async getDetail(id: number) {
      if (this.detail[id]) return this.detail[id]
      const res = await dataService.getUserDetail(id)
      this.detail[id] = res
      return res
    },
    async disable(id: number) {
      await dataService.disableUser(id)
      const item = this.list.find((x) => x.id === id)
      if (item) item.status = 'disabled'
      if (this.detail[id]) this.detail[id]!.status = 'disabled'
    },
  },
})



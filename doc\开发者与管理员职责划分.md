# 软件/固件发布管理系统 - 开发者与管理员职责划分文档

## 文档说明

**开发阶段说明**：当前处于基础功能开发阶段，本文档专注于核心功能实现，复杂的安全机制和权限控制将在后续版本中实现。

**优先级**：功能实现 > 安全权限，先确保系统基本可用，再完善安全机制。

---

## 1. 用户角色定义

### 1.1 管理员（Admin）
- **定位**：系统的宏观管理者
- **数量**：当前硬编码一个，后续可能增加
- **权限级别**：所有管理员权限相同，无需区分层级

### 1.2 开发者（Developer）
- **定位**：项目的具体执行者和内容管理者
- **数量**：由管理员创建和管理
- **权限范围**：仅限于被分配的项目

### 1.3 普通员工
- **定位**：系统的终端用户
- **访问方式**：无需注册登录，直接浏览和下载

---

## 2. 管理员职责范围

### 2.1 核心职责概述
管理员主要承担两大职责：**管理开发者账号** 和 **管理项目**

### 2.2 开发者账号管理

#### 2.2.1 账号生命周期管理
- **新增开发者账号**
  - 创建开发者用户名和密码
  - 设置开发者基本信息
  - 初始状态为无项目权限

- **删除开发者账号**
  - 删除开发者账号
  - 自动取消其所有项目权限
  - 清理相关数据

#### 2.2.2 项目权限分配
- **开通项目权限**
  - 为指定开发者分配特定项目的管理权限
  - 支持一个开发者管理多个项目
  - 支持一个项目分配给多个开发者

- **取消项目权限**
  - 取消开发者对特定项目的管理权限
  - 不影响开发者对其他项目的权限

### 2.3 项目管理

#### 2.3.1 项目生命周期管理
- **新建项目**
  - 创建项目基本信息（名称、描述、分类等）
  - 设置项目初始状态
  - 项目创建后可分配给开发者管理

- **删除项目**
  - 删除整个项目及其所有版本文件
  - 自动清理相关权限分配
  - 删除相关操作日志

### 2.4 管理员不负责的功能
- 不直接管理项目内的具体版本和文件
- 不参与日常的文件上传和版本发布
- 不需要审核开发者的操作

---

## 3. 开发者职责范围

### 3.1 核心职责概述
开发者负责被分配项目的**具体内容管理**和**版本维护**

### 3.2 项目内容管理

#### 3.2.1 项目信息维护
- **编辑项目基本信息**
  - 修改项目描述和详细介绍
  - 更新项目分类和标签
  - 维护项目状态信息

- **项目展示优化**
  - 上传项目截图或图标
  - 编写项目使用说明
  - 维护项目更新历史

### 3.3 版本发布管理

#### 3.3.1 文件上传功能
- **上传新版本文件**
  - 支持任意文件格式（固件、APP等）
  - 文件大小限制：200MB以内
  - 自动计算文件大小和哈希值

- **版本信息填写**
  - 版本号（格式：v1.0.0.0）
  - 版本类型（开发版/稳定版/发布版本/测试版本）
  - 发布日期
  - 更新说明
  - 适用设备类型

#### 3.3.2 版本维护功能
- **编辑版本信息**
  - 修改版本描述和更新说明
  - 更新设备适用性信息
  - 调整版本类型

- **版本管理操作**
  - 查看版本列表和详细信息
  - 删除错误或过时的版本
  - 查看版本下载统计

### 3.4 开发者访问权限
- **多项目管理**：可以同时管理多个被分配的项目
- **独立操作**：无需其他开发者协作或审核
- **直接发布**：上传的文件可以直接发布，无需等待审核

### 3.5 开发者不具备的功能
- 无法查看其他开发者管理的项目（除非也被分配权限）
- 无法邀请或管理其他开发者
- 无法创建新项目（只能管理被分配的项目）
- 无法修改自己的账号权限

---

## 4. 基础功能清单

### 4.1 管理员功能清单
- [ ] Admin登录功能
- [ ] 开发者账号管理界面
  - [ ] 新增开发者账号
  - [ ] 删除开发者账号
  - [ ] 查看开发者列表
- [ ] 项目管理界面
  - [ ] 新建项目
  - [ ] 删除项目
  - [ ] 查看项目列表
- [ ] 权限分配界面
  - [ ] 为开发者分配项目权限
  - [ ] 取消开发者项目权限
  - [ ] 查看权限分配情况

### 4.2 开发者功能清单
- [ ] 开发者登录功能
- [ ] 项目管理界面
  - [ ] 查看我的项目列表
  - [ ] 编辑项目基本信息
- [ ] 版本管理界面
  - [ ] 查看项目版本列表
  - [ ] 上传新版本文件
  - [ ] 编辑版本信息
  - [ ] 删除版本
- [ ] 文件上传功能
  - [ ] 文件选择和上传
  - [ ] 上传进度显示
  - [ ] 版本信息填写表单

### 4.3 公共功能清单
- [ ] 项目浏览界面（首页）
- [ ] 项目详情页
- [ ] 版本列表页
- [ ] 文件下载功能
- [ ] 搜索功能（可选）

---

## 5. 开发优先级

### 第一优先级（MVP核心功能）
1. **基础认证系统**
   - Admin硬编码登录
   - 开发者登录功能

2. **项目基础管理**
   - Admin创建/删除项目
   - 开发者查看分配的项目

3. **文件上传发布**
   - 开发者上传文件
   - 填写版本信息
   - 直接发布功能

4. **公开浏览下载**
   - 项目列表展示
   - 文件下载功能

### 第二优先级（功能完善）
1. **账号权限管理**
   - Admin管理开发者账号
   - 项目权限分配

2. **版本管理优化**
   - 版本编辑功能
   - 版本删除功能
   - 历史版本查看

3. **界面用户体验**
   - 响应式设计
   - 操作反馈优化

### 第三优先级（后续扩展）
1. **安全机制完善**
2. **权限控制细化**
3. **操作日志记录**
4. **数据统计分析**

---

## 6. 技术实现要点

### 6.1 简化原则
- 当前阶段不实现复杂的权限控制系统
- 不开发审核流程
- 权限检查只做基础的登录状态验证
- 数据操作以功能实现为主，安全机制后续补充

### 6.2 数据库设计简化
- 用户表只需基础字段
- 项目权限表采用简单的用户-项目关联
- 暂不考虑角色细分和复杂权限

### 6.3 前端路由简化
- 按角色划分基础路由
- 暂不实现复杂的路由权限守卫
- 重点保证功能页面的正常跳转

---

**总结**：本文档明确了管理员负责"管人管项目"，开发者负责"管内容管版本"的基本分工，为当前开发阶段提供清晰的功能边界指导。

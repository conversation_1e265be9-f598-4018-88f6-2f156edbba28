<template>
  <Teleport to="body">
    <Transition name="page-loading">
      <div v-if="visible" class="page-loading">
        <div class="page-loading__backdrop" />
        <div class="page-loading__container">
          <div class="page-loading__content">
            <!-- 加载动画 -->
            <div class="loading-spinner">
              <div class="spinner-circle"></div>
              <div class="spinner-circle"></div>
              <div class="spinner-circle"></div>
            </div>
            
            <!-- 加载文本 -->
            <div class="loading-text">
              <div class="loading-title">{{ title }}</div>
              <div v-if="description" class="loading-description">{{ description }}</div>
            </div>
            
            <!-- 进度条（可选） -->
            <div v-if="showProgress" class="loading-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ width: `${progress}%` }"
                ></div>
              </div>
              <div class="progress-text">{{ progress }}%</div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  /** 是否显示 */
  visible?: boolean
  /** 标题 */
  title?: string
  /** 描述 */
  description?: string
  /** 是否显示进度条 */
  showProgress?: boolean
  /** 进度百分比 */
  progress?: number
}

withDefaults(defineProps<Props>(), {
  visible: false,
  title: '加载中...',
  description: '',
  showProgress: false,
  progress: 0
})
</script>

<style scoped>
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-loading__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

[data-theme="dark"] .page-loading__backdrop {
  background: rgba(0, 0, 0, 0.8);
}

.page-loading__container {
  position: relative;
  z-index: 1;
}

.page-loading__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-6);
  padding: var(--spacing-8);
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  min-width: 320px;
  text-align: center;
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  gap: var(--spacing-2);
}

.spinner-circle {
  width: 12px;
  height: 12px;
  background: var(--color-primary);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.spinner-circle:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-circle:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 加载文本 */
.loading-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.loading-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
}

.loading-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  max-width: 280px;
}

/* 进度条 */
.loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--bg-3);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-2);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

/* 过渡动画 */
.page-loading-enter-active,
.page-loading-leave-active {
  transition: all 0.3s ease;
}

.page-loading-enter-from,
.page-loading-leave-to {
  opacity: 0;
}

.page-loading-enter-from .page-loading__content,
.page-loading-leave-to .page-loading__content {
  transform: scale(0.9) translateY(20px);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .page-loading__content {
    margin: var(--spacing-4);
    min-width: auto;
    width: calc(100% - var(--spacing-8));
  }
}
</style>

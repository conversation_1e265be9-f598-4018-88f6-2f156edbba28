<template>
  <div class="page page-search">
    <div class="container-xl">
      <div class="search-header section-header">
        <h1 class="page-title">搜索项目</h1>
        <p class="page-description">快速找到您需要的软件项目和版本</p>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form card">
        <div class="search-input-group">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入项目名称、版本号或关键词..."
            class="search-input"
            @keyup.enter="performSearch"
          />
          <button @click="performSearch" class="btn btn-primary">
            <span>搜索</span>
          </button>
        </div>
        
        <!-- 高级筛选 -->
        <div class="search-filters">
          <div class="filter-group">
            <label class="filter-label">版本类型</label>
            <select v-model="filters.versionType" class="filter-select">
              <option value="">全部版本</option>
              <option value="stable">稳定版</option>
              <option value="beta">测试版</option>
              <option value="alpha">开发版</option>
            </select>
          </div>
          
          <button @click="clearFilters" class="btn btn-outline">清除筛选</button>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <!-- Facets 统计 -->
        <div v-if="store.facets && store.facets.versionType" class="facets card">
          <div class="facets-grid">
            <div class="facet">
              <div class="facet-title">版本类型</div>
              <div class="facet-items">
                <button v-for="(count, k) in store.facets.versionType" :key="k" class="facet-item" @click="filters.versionType = String(k); performSearch()">
                  <span>{{ mapVersionType(String(k)) }}</span>
                  <span class="facet-count">{{ count }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isSearching" class="search-loading">
          <div class="loading-spinner"></div>
          <span>搜索中...</span>
        </div>
        
        <div v-else-if="results.length > 0" class="results-container">
          <div class="results-header">
            <h2 class="results-title">搜索结果</h2>
            <span class="results-count">找到 {{ total }} 个结果</span>
          </div>
          
          <div class="results-list">
            <div 
              v-for="result in results" 
              :key="result._key"
              class="result-item card"
            >
              <div class="result-content">
                <h3 class="result-title">
                  <RouterLink :to="`/project/${result.projectId}`">{{ result.title }}</RouterLink>
                </h3>
                <p class="result-description">{{ result.description }}</p>
                <div class="result-meta">
                  <span class="result-type">{{ result.typeLabel }}</span>
                  <span v-if="result.latestVersion" class="result-version">最新版本: {{ result.latestVersion }}</span>
                  <span class="result-date">更新时间: {{ result.updatedAt }}</span>
                </div>
              </div>
              <div class="result-actions">
                <RouterLink :to="`/project/${result.projectId}`" class="btn btn-secondary">查看详情</RouterLink>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else-if="hasSearched && !isSearching" class="no-results">
          <div class="no-results-icon">🔍</div>
          <h3 class="no-results-title">未找到相关结果</h3>
          <p class="no-results-description">
            请尝试使用不同的关键词或调整筛选条件
          </p>
          <button @click="clearSearch" class="btn btn-secondary">清除搜索</button>
        </div>
        
        <div v-else class="search-prompt">
          <div class="prompt-icon">💡</div>
          <h3 class="prompt-title">开始搜索</h3>
          <p class="prompt-description">
            输入关键词搜索项目，或使用下方的热门标签快速查找
          </p>
          
          <!-- 热门标签 -->
          <div class="popular-tags">
            <h4 class="tags-title">热门搜索</h4>
            <div class="tags-list">
              <button 
                v-for="tag in popularTags" 
                :key="tag"
                @click="searchByTag(tag)"
                class="tag-btn"
              >
                {{ tag }}
              </button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination">
          <button class="btn btn-outline" :disabled="store.page <= 1" @click="changePage(store.page - 1)">上一页</button>
          <span class="page-info">第 {{ store.page }} / {{ totalPages }} 页（共 {{ total }} 条）</span>
          <button class="btn btn-outline" :disabled="store.page >= totalPages" @click="changePage(store.page + 1)">下一页</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { useSearchStore } from '@/stores/search'

const store = useSearchStore()
const route = useRoute()
const router = useRouter()

// 搜索状态
const searchQuery = ref('')
const isSearching = ref(false)
const hasSearched = ref(false)

// 筛选条件
const filters = reactive({
  versionType: ''
})

const results = computed(() =>
  store.list.map((it, idx) => {
    if (it.kind === 'project' && it.project) {
      return {
        _key: `p-${it.project.id}-${idx}`,
        projectId: it.project.id,
        title: it.project.name,
        description: it.project.description || '',
        latestVersion: it.project.latestRelease?.version,
        updatedAt: it.project.updatedAt ? new Date(it.project.updatedAt).toLocaleDateString() : '—',
      }
    }
    const r = it.release!
    return {
      _key: `r-${r.id}-${idx}`,
      projectId: r.projectId,
      title: `${r.projectName || '项目'} - ${r.version}`,
      description: r.changelog || '',
      typeLabel: mapVersionType(r.type),
      latestVersion: r.version,
      updatedAt: new Date(r.createdAt).toLocaleDateString(),
    }
  })
)

const total = computed(() => store.total)
const totalPages = computed(() => Math.max(1, Math.ceil(store.total / store.pageSize)))
const popularTags = computed(() => {
  const defaults = ['ESP32', '固件', 'Android', '稳定版', 'IoT', '传感器']
  const history = store.history || []
  const combined = [...history, ...defaults]
  return Array.from(new Set(combined)).slice(0, 12)
})

function mapType(t?: string) {
  if (t === 'firmware') return '固件'
  if (t === 'app') return '应用程序'
  if (t === 'library') return '库文件'
  return '—'
}

function mapVersionType(t?: string) {
  if (t === 'stable') return '稳定版'
  if (t === 'beta') return '测试版'
  if (t === 'alpha') return '开发版'
  return '—'
}

function syncFromRoute() {
  const q = route.query
  store.q = (q.q as string) || ''
  store.versionType = (q.versionType as string) || ''
  store.page = q.page ? Number(q.page) : 1
  store.pageSize = q.pageSize ? Number(q.pageSize) : 10

  searchQuery.value = store.q
  filters.versionType = store.versionType
}

function pushRoute() {
  const q = {
    q: store.q || undefined,
    versionType: store.versionType || undefined,
    page: String(store.page),
    pageSize: String(store.pageSize),
  }
  router.replace({ query: q })
}

// 执行搜索（带简单防抖）
let timer: number | null = null
const performSearch = async () => {
  const run = async () => {
    if (!searchQuery.value.trim()) return
    isSearching.value = true
    hasSearched.value = true
    store.q = searchQuery.value
    store.versionType = filters.versionType
    store.page = 1
    pushRoute()
    await store.search()
    store.saveHistory(searchQuery.value)
    isSearching.value = false
  }
  if (timer) window.clearTimeout(timer)
  timer = window.setTimeout(run, 300)
}

// 清除筛选条件
const clearFilters = () => {
  filters.versionType = ''
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  hasSearched.value = false
  clearFilters()
  store.clear()
  pushRoute()
}

// 通过标签搜索
const searchByTag = (tag: string) => {
  searchQuery.value = tag
  performSearch()
}

function changePage(page: number) {
  store.page = Math.max(1, Math.min(page, totalPages.value))
  pushRoute()
  store.search()
}

watch(() => route.query, () => {
  syncFromRoute()
  if (store.q) {
    hasSearched.value = true
    store.search()
  }
})

syncFromRoute()
store.restoreHistory()
</script>

<style scoped>
.page { padding: var(--spacing-6) 0; }

.search-header { margin-bottom: var(--spacing-8); }
.page-title { font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--color-text-1); margin: 0 0 var(--spacing-3) 0; }
.page-description { font-size: var(--font-size-lg); color: var(--color-text-2); margin: 0; }

/* 搜索表单 */
.search-form { padding: var(--spacing-6); margin-bottom: var(--spacing-8); }
.search-input-group { display: flex; gap: var(--spacing-3); margin-bottom: var(--spacing-6); }
.search-input {
  flex: 1;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  background: var(--bg-1);
  color: var(--color-text-1);
}
.search-input:focus { outline: none; border-color: var(--color-primary); box-shadow: 0 0 0 2px var(--color-primary-light); }

.search-filters { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-4); align-items: end; }
.filter-group { display: flex; flex-direction: column; gap: var(--spacing-2); }
.filter-label { font-size: var(--font-size-sm); font-weight: var(--font-weight-medium); color: var(--color-text-2); }
.filter-select { padding: var(--spacing-2) var(--spacing-3); border: 1px solid var(--border-color); border-radius: var(--radius-md); background: var(--bg-1); color: var(--color-text-1); font-size: var(--font-size-sm); }

/* facets */
.facets { padding: var(--spacing-4); margin-bottom: var(--spacing-6); }
.facets-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-4); }
.facet-title { font-size: var(--font-size-sm); color: var(--color-text-3); margin-bottom: var(--spacing-2); }
.facet-items { display: flex; flex-wrap: wrap; gap: var(--spacing-2); }
.facet-item { display: inline-flex; align-items: center; gap: var(--spacing-2); padding: 6px 10px; border: 1px solid var(--border-color); border-radius: var(--radius-full); background: var(--bg-1); color: var(--color-text-2); cursor: pointer; }
.facet-item:hover { border-color: var(--color-primary); color: var(--color-primary); }
.facet-count { font-size: var(--font-size-xs); color: var(--color-text-3); }

/* 搜索结果 */
.search-loading { display: flex; align-items: center; justify-content: center; gap: var(--spacing-3); padding: var(--spacing-8); color: var(--color-text-2); }
.loading-spinner { width: 20px; height: 20px; border: 2px solid var(--border-color); border-top-color: var(--color-primary); border-radius: 50%; animation: spin 1s linear infinite; }
@keyframes spin { to { transform: rotate(360deg); } }

.results-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-6); }
.results-title { font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); color: var(--color-text-1); margin: 0; }
.results-count { color: var(--color-text-3); font-size: var(--font-size-sm); }

.results-list { display: flex; flex-direction: column; gap: var(--spacing-4); }
.result-item { display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-4); }
.result-title { font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); margin: 0 0 var(--spacing-2) 0; }
.result-title a { color: var(--color-text-1); text-decoration: none; }
.result-title a:hover { color: var(--color-primary); }
.result-description { color: var(--color-text-2); margin: 0 0 var(--spacing-3) 0; }
.result-meta { display: flex; gap: var(--spacing-3); }
.result-type, .result-version, .result-date { font-size: var(--font-size-sm); color: var(--color-text-3); }
.result-actions { display: flex; gap: var(--spacing-2); }

/* 无结果状态 */
.no-results, .search-prompt { text-align: center; padding: var(--spacing-8); color: var(--color-text-2); }
.no-results-icon, .prompt-icon { font-size: 48px; margin-bottom: var(--spacing-4); }
.no-results-title, .prompt-title { font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); color: var(--color-text-1); margin: 0 0 var(--spacing-3) 0; }
.no-results-description, .prompt-description { margin: 0 0 var(--spacing-6) 0; line-height: var(--line-height-relaxed); }

/* 热门标签 */
.popular-tags { margin-top: var(--spacing-6); }
.tags-title { font-size: var(--font-size-md); font-weight: var(--font-weight-medium); color: var(--color-text-1); margin: 0 0 var(--spacing-4) 0; }
.tags-list { display: flex; justify-content: center; gap: var(--spacing-2); flex-wrap: wrap; }
.tag-btn { padding: var(--spacing-2) var(--spacing-4); background: var(--color-primary-light); color: var(--color-primary); border: none; border-radius: var(--radius-full); font-size: var(--font-size-sm); font-weight: var(--font-weight-medium); cursor: pointer; transition: all 0.2s ease; }
.tag-btn:hover { background: var(--color-primary); color: white; }

/* 响应式设计 */
@media (max-width: 768px) {
  .search-input-group { flex-direction: column; }
  .search-filters { grid-template-columns: 1fr; }
  .results-header { flex-direction: column; align-items: flex-start; gap: var(--spacing-2); }
  .result-item { flex-direction: column; align-items: flex-start; gap: var(--spacing-3); }
  .result-actions { width: 100%; justify-content: flex-end; }
  .tags-list { gap: var(--spacing-1); }
}

.pagination { display: flex; align-items: center; justify-content: center; gap: var(--spacing-3); margin-top: var(--spacing-6); }
.page-info { color: var(--color-text-3); }
</style>

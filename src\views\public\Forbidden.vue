<template>
  <div class="error-page">
    <div class="error-container">
      <!-- 错误图标和代码 -->
      <div class="error-visual">
        <div class="error-icon">🔒</div>
        <div class="error-code">403</div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-title">访问被拒绝</h1>
        <p class="error-description">
          很抱歉，您没有访问此页面的权限。
          这可能是因为您的权限级别不足或者需要进行身份验证。
        </p>
        
        <!-- 建议操作 -->
        <div class="error-suggestions">
          <h3 class="suggestions-title">您可以尝试：</h3>
          <ul class="suggestions-list">
            <li>检查您是否已经登录</li>
            <li>确认您的账户具有相应权限</li>
            <li>联系管理员申请访问权限</li>
            <li>返回公开页面继续浏览</li>
          </ul>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <RouterLink to="/login" class="btn btn-primary">
            <BaseIcon name="user" size="16" />
            登录账户
          </RouterLink>
          <RouterLink to="/" class="btn btn-secondary">
            <BaseIcon name="home" size="16" />
            返回首页
          </RouterLink>
          <button @click="goBack" class="btn btn-outline">
            <BaseIcon name="arrow-left" size="16" />
            返回上页
          </button>
        </div>
      </div>
      
      <!-- 权限说明 -->
      <div class="permission-info">
        <h3 class="info-title">权限说明</h3>
        <div class="info-grid">
          <div class="info-item">
            <BaseIcon name="eye" size="24" />
            <div class="info-content">
              <h4>普通访问</h4>
              <p>可以浏览公开内容和下载文件</p>
            </div>
          </div>
          <div class="info-item">
            <BaseIcon name="code" size="24" />
            <div class="info-content">
              <h4>开发者权限</h4>
              <p>可以上传和管理分配的项目</p>
            </div>
          </div>
          <div class="info-item">
            <BaseIcon name="shield" size="24" />
            <div class="info-content">
              <h4>管理员权限</h4>
              <p>拥有完整的系统管理权限</p>
            </div>
          </div>
          <div class="info-item">
            <BaseIcon name="mail" size="24" />
            <div class="info-content">
              <h4>申请权限</h4>
              <p>联系管理员申请相应权限</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterLink, useRouter } from 'vue-router'
import BaseIcon from '@base/atoms/BaseIcon.vue'

const router = useRouter()

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) var(--spacing-4);
  background: linear-gradient(135deg, var(--color-danger-light) 0%, var(--bg-1) 50%, var(--bg-2) 100%);
}

.error-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

/* 错误视觉元素 */
.error-visual {
  margin-bottom: var(--spacing-8);
}

.error-icon {
  font-size: 120px;
  margin-bottom: var(--spacing-4);
  opacity: 0.8;
  filter: hue-rotate(15deg);
}

.error-code {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-danger);
  line-height: 1;
  text-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

/* 错误内容 */
.error-content {
  margin-bottom: var(--spacing-8);
}

.error-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-4) 0;
}

.error-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-2);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-6) 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 建议操作 */
.error-suggestions {
  background: var(--bg-1);
  border: 1px solid var(--color-danger-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.suggestions-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-3) 0;
}

.suggestions-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestions-list li {
  padding: var(--spacing-2) 0;
  color: var(--color-text-2);
  position: relative;
  padding-left: var(--spacing-6);
}

.suggestions-list li::before {
  content: '•';
  color: var(--color-danger);
  font-weight: bold;
  position: absolute;
  left: var(--spacing-2);
}

/* 操作按钮 */
.error-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-10);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-md);
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--bg-2);
  color: var(--color-text-1);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-3);
  border-color: var(--color-primary);
}

.btn-outline {
  background: transparent;
  color: var(--color-text-2);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  border-color: var(--color-danger);
  color: var(--color-danger);
}

/* 权限说明 */
.permission-info {
  max-width: 600px;
  margin: 0 auto;
}

.info-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-6) 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  text-align: left;
  transition: all 0.2s ease;
}

.info-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.info-content h4 {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-1) 0;
}

.info-content p {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-page {
    padding: var(--spacing-6) var(--spacing-3);
  }
  
  .error-icon {
    font-size: 80px;
  }
  
  .error-code {
    font-size: var(--font-size-4xl);
  }
  
  .error-title {
    font-size: var(--font-size-2xl);
  }
  
  .error-description {
    font-size: var(--font-size-md);
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .error-suggestions {
    text-align: center;
  }
  
  .suggestions-list li {
    padding-left: 0;
  }
  
  .suggestions-list li::before {
    display: none;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .error-page {
  background: linear-gradient(135deg, var(--color-danger-dark) 0%, var(--bg-1) 50%, var(--bg-2) 100%);
}

[data-theme="dark"] .error-suggestions {
  background: var(--bg-2);
  border-color: var(--color-danger-dark);
}

[data-theme="dark"] .info-item {
  background: var(--bg-2);
  border-color: var(--border-color);
}

/* 动画效果 */
.error-container {
  animation: errorFadeIn 0.6s ease-out;
}

@keyframes errorFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-visual {
  animation: errorShake 3s ease-in-out infinite;
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

.info-item {
  animation: infoFadeIn 0.8s ease-out;
  animation-fill-mode: both;
}

.info-item:nth-child(1) { animation-delay: 0.1s; }
.info-item:nth-child(2) { animation-delay: 0.2s; }
.info-item:nth-child(3) { animation-delay: 0.3s; }
.info-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes infoFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>



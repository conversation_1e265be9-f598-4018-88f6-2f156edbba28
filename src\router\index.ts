import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import { useAdminAuthStore } from '@/stores/adminAuth'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    public?: boolean
    requiresDeveloper?: boolean
    requiresAdmin?: boolean
    layout?: 'default' | 'blank' | 'public'
    breadcrumb?: Array<{ name: string; path?: string }>
  }
}

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/public/Home.vue'),
    meta: { title: '首页', public: true, layout: 'public', breadcrumb: [{ name: '首页', path: '/' }] },
  },
  {
    path: '/projects',
    name: 'Projects',
    component: () => import('@/views/public/Projects.vue'),
    meta: { title: '项目列表', public: true, layout: 'public', breadcrumb: [{ name: '首页', path: '/' }, { name: '项目列表' }] },
  },
  {
    path: '/project/:id',
    name: 'ProjectDetail',
    component: () => import('@/views/public/ProjectDetail.vue'),
    meta: { title: '项目详情', public: true, layout: 'public', breadcrumb: [{ name: '首页', path: '/' }, { name: '项目列表', path: '/projects' }, { name: '项目详情' }] },
  },
  {
    path: '/project/:projectId/software/:artifact',
    name: 'SoftwareDetail',
    component: () => import('@/views/public/SoftwareDetail.vue'),
    meta: { title: '历史版本', public: true, layout: 'public', breadcrumb: [{ name: '首页', path: '/' }, { name: '项目列表', path: '/projects' }, { name: '项目详情', path: '/project/:projectId' }, { name: '历史版本' }] },
  },
  {
    path: '/preview',
    name: 'FilePreview',
    component: () => import('@/views/public/FilePreview.vue'),
    meta: { title: '文件预览', public: true, layout: 'public', breadcrumb: [{ name: '首页', path: '/' }, { name: '文件预览' }] },
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/public/Search.vue'),
    meta: { title: '搜索', public: true, layout: 'public', breadcrumb: [{ name: '首页', path: '/' }, { name: '搜索' }] },
  },
  // 开发者登录
  {
    path: '/dev/login',
    name: 'DeveloperLogin',
    component: () => import('@/views/auth/DeveloperLogin.vue'),
    meta: { title: '开发者登录', public: true, layout: 'blank' },
  },
  // 管理员登录
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: () => import('@/views/auth/AdminLogin.vue'),
    meta: { title: '管理员登录', public: true, layout: 'blank' },
  },
  // 向后兼容的通用登录（重定向到开发者登录）
  {
    path: '/login',
    redirect: '/dev/login'
  },
  
  // 开发者中心（完全独立）
  {
    path: '/dev',
    redirect: '/developer/projects'
  },
  {
    path: '/developer',
    redirect: '/developer/projects'
  },
  // 开发者项目列表
  {
    path: '/developer/projects',
    name: 'DeveloperProjects',
    component: () => import('@/views/developer/DeveloperDashboard.vue'),
    meta: {
      title: '开发者中心',
      requiresDeveloper: true,
      breadcrumb: [
        { name: '首页', path: '/' },
        { name: '开发者中心' },
      ],
    },
  },
  // 开发者项目详情
  {
    path: '/developer/projects/:projectId',
    name: 'DeveloperProjectDetail',
    component: () => import('@/views/developer/DeveloperProjectDetail.vue'),
    meta: {
      title: '项目详情',
      requiresDeveloper: true,
      breadcrumb: [
        { name: '首页', path: '/' },
        { name: '开发者中心', path: '/developer/projects' },
        { name: '项目详情' },
      ],
    },
  },
  
  // 管理员后台（完全独立）
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/admin/AdminDashboard.vue'),
    meta: {
      title: '管理后台',
      requiresAdmin: true,
      breadcrumb: [
        { name: '首页', path: '/' },
        { name: '管理后台' },
      ],
    },
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/public/Forbidden.vue'),
    meta: { title: '无权限', public: true, layout: 'blank' },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/public/NotFound.vue'),
    meta: { title: '未找到', public: true, layout: 'blank' },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior() {
    return { top: 0 }
  },
})

router.beforeEach(async (to, _from, next) => {
  const developerAuth = useDeveloperAuthStore()
  const adminAuth = useAdminAuthStore()

  // 初始化认证状态
  if (!developerAuth.token && !developerAuth.user) developerAuth.initialize()
  if (!adminAuth.token && !adminAuth.user) adminAuth.initialize()

  // 设置页面标题
  document.title = (to.meta.title ? `${to.meta.title} - ` : '') + '软件发布系统'

  // 公共页面，直接放行
  if (to.meta.public) return next()

  // 开发者认证检查
  if (to.meta.requiresDeveloper) {
    if (!developerAuth.isAuthenticated) {
      return next({ 
        name: 'DeveloperLogin', 
        query: { redirect: to.fullPath } 
      })
    }
    // 可以在这里添加更多开发者权限检查
    return next()
  }

  // 管理员认证检查
  if (to.meta.requiresAdmin) {
    if (!adminAuth.isAuthenticated) {
      return next({ 
        name: 'AdminLogin', 
        query: { redirect: to.fullPath } 
      })
    }
    // 可以在这里添加更多管理员权限检查
    return next()
  }

  next()
})

export default router



<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { RouterView, useRoute } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { useAppStore } from '@/stores/app'
import PublicLayout from '@/layouts/PublicLayout.vue'
import GlobalLoading from '@/components/app/GlobalLoading.vue'

const route = useRoute()
const developerAuth = useDeveloperAuthStore()
const adminAuth = useAdminAuthStore()
const app = useAppStore()

// 根据路由meta确定布局类型
const layoutType = computed(() => {
  return route.meta.layout || 'default'
})

// 计算当前认证用户
const currentAuth = computed(() => {
  if (developerAuth.isAuthenticated) return developerAuth
  if (adminAuth.isAuthenticated) return adminAuth
  return null
})

const currentUser = computed(() => currentAuth.value?.user || null)

// 退出登录处理
function handleLogout() {
  if (developerAuth.isAuthenticated) {
    developerAuth.logout()
  }
  if (adminAuth.isAuthenticated) {
    adminAuth.logout()
  }
}

// 初始化应用状态
onMounted(() => {
  app.initialize()
  developerAuth.initialize()
  adminAuth.initialize()
})
</script>

<template>
  <div id="app" :data-theme="app.isDark ? 'dark' : 'light'">
    <!-- 全局加载组件 -->
    <GlobalLoading />
    
    <!-- 公开页面布局 -->
    <PublicLayout v-if="layoutType === 'public'">
      <RouterView />
    </PublicLayout>
    
    <!-- 空白布局（登录页、错误页等） -->
    <div v-else-if="layoutType === 'blank'" class="blank-layout">
      <RouterView />
    </div>
    
    <!-- 默认布局（管理后台等） -->
    <div v-else class="default-layout">
      <!-- 简单的默认布局头部 -->
      <header class="default-header">
        <div class="header-content">
          <div class="header-nav">
            <RouterLink to="/" class="nav-link">返回首页</RouterLink>
            <RouterLink v-if="adminAuth.isAuthenticated" to="/admin" class="nav-link">管理后台</RouterLink>
            <RouterLink v-if="developerAuth.isAuthenticated" to="/dev" class="nav-link">开发者中心</RouterLink>
          </div>
          <div class="header-user">
            <!-- 主题切换 -->
            <select 
              :value="app.theme" 
              @change="app.setTheme(($event.target as HTMLSelectElement).value as any)"
              class="theme-select"
            >
              <option value="light">浅色</option>
              <option value="dark">深色</option>
              <option value="system">跟随系统</option>
            </select>
            
            <!-- 用户信息 -->
            <div v-if="currentAuth" class="user-info">
              <span>{{ currentUser?.name || currentUser?.username }}</span>
              <span class="user-type">({{ currentUser?.userType === 'admin' ? '管理员' : '开发者' }})</span>
              <button @click="handleLogout" class="logout-btn">退出</button>
            </div>
            <div v-else class="login-links">
              <RouterLink to="/dev/login" class="login-link">开发者登录</RouterLink>
              <RouterLink to="/admin/login" class="login-link admin">管理员</RouterLink>
            </div>
          </div>
        </div>
      </header>
      
      <!-- 默认布局内容 -->
      <main class="default-main">
        <RouterView />
      </main>
    </div>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 空白布局 */
.blank-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-1);
}

/* 默认布局 */
.default-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.default-header {
  background: var(--bg-1);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-3) var(--spacing-4);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-nav {
  display: flex;
  gap: var(--spacing-4);
}

.nav-link {
  color: var(--color-text-2);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color 0.2s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--color-primary);
}

.header-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.theme-select {
  padding: var(--spacing-1) var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-1);
  color: var(--color-text-2);
  font-size: var(--font-size-sm);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.logout-btn {
  padding: var(--spacing-1) var(--spacing-2);
  background: none;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--color-text-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.logout-btn:hover {
  background: var(--bg-2);
}

.login-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.login-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  transition: background-color 0.2s ease;
}

.login-link:hover {
  background: var(--bg-2);
}

.login-link.admin {
  color: var(--color-warning-dark);
}

.user-type {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  margin-left: var(--spacing-1);
}

.default-main {
  flex: 1;
  padding: var(--spacing-4);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .default-header {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .header-nav {
    order: 2;
    gap: var(--spacing-2);
  }

  .header-user {
    order: 1;
  }

  .default-main {
    padding: var(--spacing-3);
  }
}
</style>

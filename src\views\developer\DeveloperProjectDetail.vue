<template>
  <div class="project-detail">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <p>正在加载项目信息...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <h2>加载失败</h2>
      <p>{{ error }}</p>
      <button class="btn btn-primary" @click="loadProjectDetail">重试</button>
    </div>

    <!-- Project Detail -->
    <div v-else-if="project" class="project-content">
      <!-- Header -->
      <div class="project-header">
        <div class="header-content">
          <div class="breadcrumb">
            <router-link to="/developer/projects" class="breadcrumb-item">项目管理</router-link>
            <span class="breadcrumb-separator">></span>
            <span class="breadcrumb-current">{{ project.name }}</span>
          </div>
          
          <div class="project-title-section">
            <h1 class="project-title">{{ project.name }}</h1>
            <p class="project-description">{{ project.description || '暂无描述' }}</p>
          </div>
        </div>
        
        <div class="header-actions">
          <button class="btn btn-primary" @click="showAddSoftwareModal = true">
            新增软件类型
          </button>
        </div>
      </div>

      <!-- Software Types Section -->
      <div class="software-section">
        <div class="section-header">
          <h2>软件类型 ({{ softwareTypes.length }})</h2>
        </div>

        <div v-if="softwareLoading" class="loading-state">
          <p>正在加载软件类型...</p>
        </div>

        <div v-else-if="softwareTypes.length === 0" class="empty-state">
          <div class="empty-content">
            <h3>暂无软件类型</h3>
            <p>还没有添加任何软件类型</p>
            <button class="btn btn-primary" @click="showAddSoftwareModal = true">
              添加第一个软件类型
            </button>
          </div>
        </div>

        <div v-else class="software-grid">
          <div 
            v-for="software in softwareTypes" 
            :key="software.id"
            class="software-card card"
          >
            <div class="software-header">
              <div class="software-info">
                <h3 class="software-name">{{ software.name }}</h3>
                <div class="software-meta" v-if="software.latestVersion">
                  <span class="version-info">
                    最新版本: {{ software.latestVersion.versionName }}
                  </span>
                  <span class="update-time">
                    {{ formatTime(software.latestVersion.updatedAt) }}
                  </span>
                </div>
                <div class="software-meta" v-else>
                  <span class="no-version">暂无版本</span>
                </div>
              </div>
              <div class="software-actions">
                <button 
                  class="btn btn-sm btn-outline"
                  @click="addVersion(software)"
                >
                  新增版本
                </button>
                <button 
                  class="btn btn-sm btn-outline"
                  @click="viewAllVersions(software)"
                  v-if="software.latestVersion"
                >
                  查看全部版本
                </button>
                <button 
                  class="btn btn-sm btn-outline"
                  @click="editSoftware(software)"
                >
                  重命名
                </button>
                <button 
                  class="btn btn-sm btn-danger"
                  @click="deleteSoftware(software)"
                >
                  删除
                </button>
              </div>
            </div>

            <!-- 最新版本信息 -->
            <div v-if="software.latestVersion" class="version-info-card">
              <div class="version-details">
                <div class="version-meta">
                  <span class="file-size" v-if="software.latestVersion.fileSize">
                    文件大小: {{ formatSize(software.latestVersion.fileSize) }}
                  </span>
                </div>
                <div class="version-actions">
                  <button 
                    class="btn btn-sm btn-primary"
                    @click="downloadVersion(software.latestVersion)"
                    v-if="software.latestVersion.fileUrl"
                  >
                    下载
                  </button>
                  <button 
                    class="btn btn-sm btn-outline"
                    @click="editVersion(software.latestVersion)"
                  >
                    编辑
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Software Modal -->
    <div v-if="showAddSoftwareModal" class="modal-overlay" @click="closeAddSoftwareModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>新增软件类型</h3>
          <button class="modal-close" @click="closeAddSoftwareModal">×</button>
        </div>
        <form class="software-form" @submit.prevent="handleAddSoftware">
          <div class="form-group">
            <label class="form-label">软件类型名称 *</label>
            <input
              v-model="softwareForm.name"
              type="text"
              class="form-input"
              placeholder="例如：Android APP, iOS APP, 固件"
              required
            />
          </div>
          <div v-if="softwareError" class="error-message">
            {{ softwareError }}
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeAddSoftwareModal">
              取消
            </button>
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="softwareSubmitting || !softwareForm.name.trim()"
            >
              {{ softwareSubmitting ? '添加中...' : '确认添加' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Add Version Modal -->
    <div v-if="showAddVersionModal" class="modal-overlay" @click="closeAddVersionModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>新增版本 - {{ currentSoftware?.name }}</h3>
          <button class="modal-close" @click="closeAddVersionModal">×</button>
        </div>
        <form class="version-form" @submit.prevent="handleAddVersion">
          <div class="form-group">
            <label class="form-label">版本号 *</label>
            <input
              v-model="versionForm.versionName"
              type="text"
              class="form-input"
              placeholder="例如：v1.0.1"
              required
            />
          </div>
          <div class="form-group">
            <label class="form-label">文件上传 *</label>
            <div class="file-upload-area" @click="() => fileInput?.click()">
              <input
                ref="fileInput"
                type="file"
                style="display: none"
                @change="handleFileSelect"
              />
              <div v-if="!versionForm.file" class="upload-placeholder">
                <p>点击选择文件或拖拽文件到此处</p>
              </div>
              <div v-else class="file-info">
                <p>{{ versionForm.file.name }}</p>
                <p class="file-size">{{ formatSize(versionForm.file.size) }}</p>
              </div>
            </div>
          </div>
          <div v-if="versionError" class="error-message">
            {{ versionError }}
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeAddVersionModal">
              取消
            </button>
            <button 
              type="submit" 
              class="btn btn-primary" 
              :disabled="versionSubmitting || !versionForm.versionName.trim() || !versionForm.file"
            >
              {{ versionSubmitting ? '上传中...' : '确认上传' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Version History Modal -->
    <div v-if="showVersionHistoryModal" class="modal-overlay" @click="closeVersionHistoryModal">
      <div class="modal-content version-history-modal" @click.stop>
        <div class="modal-header">
          <h3>版本历史 - {{ currentSoftwareForHistory?.name }}</h3>
          <button class="modal-close" @click="closeVersionHistoryModal">×</button>
        </div>
        
        <div class="version-history-content">
          <div v-if="versionHistoryLoading" class="loading-state">
            <p>正在加载版本历史...</p>
          </div>
          
          <div v-else-if="versionHistory.length === 0" class="empty-state">
            <div class="empty-content">
              <h4>暂无版本历史</h4>
              <p>该软件类型还没有任何版本记录</p>
            </div>
          </div>
          
          <div v-else class="version-history-list">
            <div class="version-history-header">
              <span class="version-count">共 {{ versionHistory.length }} 个版本</span>
            </div>
            
            <div 
              v-for="version in versionHistory" 
              :key="version.id"
              class="version-history-item"
            >
              <div class="version-item-header">
                <div class="version-info">
                  <h4 class="version-name">{{ version.versionName }}</h4>
                  <div class="version-meta">
                    <span class="version-size" v-if="version.fileSize">
                      {{ formatSize(version.fileSize) }}
                    </span>
                    <span class="version-time">
                      {{ formatTime(version.updatedAt) }}
                    </span>
                  </div>
                </div>
                
                <div class="version-actions">
                  <button 
                    class="btn btn-sm btn-primary"
                    @click="downloadVersion(version)"
                    v-if="version.fileUrl"
                  >
                    下载
                  </button>
                  <button 
                    class="btn btn-sm btn-outline"
                    @click="editVersion(version)"
                  >
                    编辑
                  </button>
                  <button 
                    class="btn btn-sm btn-danger"
                    @click="deleteVersionFromHistory(version)"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getDataService } from '@/data'
import type { ProjectItem, SoftwareType, Version } from '@/data'

const dataService = getDataService()

const route = useRoute()

// 项目ID从路由获取
const projectId = ref(Number(route.params.projectId))

// 状态
const loading = ref(true)
const error = ref('')
const project = ref<ProjectItem | null>(null)
const softwareTypes = ref<SoftwareType[]>([])
const softwareLoading = ref(false)

// 新增软件类型
const showAddSoftwareModal = ref(false)
const softwareSubmitting = ref(false)
const softwareError = ref('')
const softwareForm = reactive({
  name: ''
})

// 新增版本
const showAddVersionModal = ref(false)
const currentSoftware = ref<SoftwareType | null>(null)
const versionSubmitting = ref(false)
const versionError = ref('')
const versionForm = reactive({
  versionName: '',
  file: null as File | null
})

// 版本历史
const showVersionHistoryModal = ref(false)
const versionHistoryLoading = ref(false)
const versionHistory = ref<Version[]>([])
const currentSoftwareForHistory = ref<SoftwareType | null>(null)

// 文件输入引用
const fileInput = ref<HTMLInputElement>()

// 格式化文件大小
function formatSize(size?: number) {
  if (!size) return '-'
  const units = ['B', 'KB', 'MB', 'GB']
  let unitIndex = 0
  let sizeValue = size
  
  while (sizeValue >= 1024 && unitIndex < units.length - 1) {
    sizeValue /= 1024
    unitIndex++
  }
  
  return `${sizeValue.toFixed(1)} ${units[unitIndex]}`
}

// 格式化时间
function formatTime(timestamp?: number) {
  if (!timestamp) return '-'
  const now = Date.now()
  const diff = now - timestamp
  const days = Math.floor(diff / 86400000)
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

// 加载项目详情
async function loadProjectDetail() {
  loading.value = true
  error.value = ''
  
  try {
    const result = await dataService.getProjectDetail(projectId.value)
    
    if (result && result.id && result.name) {
      project.value = result
    } else {
      error.value = '项目数据格式错误'
      return
    }
    
    await loadSoftwareTypes()
  } catch (err: any) {
    console.error('加载项目详情失败:', err)
    error.value = err?.message || '加载项目失败'
  } finally {
    loading.value = false
  }
}

// 加载软件类型列表
async function loadSoftwareTypes() {
  softwareLoading.value = true
  
  try {
    const result = await dataService.getSoftwareTypes(projectId.value)
    
    if (Array.isArray(result)) {
      softwareTypes.value = result
    } else {
      softwareTypes.value = []
    }
  } catch (err) {
    console.error('加载软件类型失败:', err)
    softwareTypes.value = []
  } finally {
    softwareLoading.value = false
  }
}

// 新增软件类型相关函数
function closeAddSoftwareModal() {
  showAddSoftwareModal.value = false
  softwareForm.name = ''
  softwareError.value = ''
}

async function handleAddSoftware() {
  if (!softwareForm.name.trim()) return
  
  softwareSubmitting.value = true
  softwareError.value = ''
  
  try {
    const result = await dataService.createSoftwareType(projectId.value, {
      name: softwareForm.name.trim()
    })
    
    softwareTypes.value.push(result)
    closeAddSoftwareModal()
  } catch (err: any) {
    softwareError.value = err?.message || '添加失败'
  } finally {
    softwareSubmitting.value = false
  }
}

// 新增版本相关函数
function addVersion(software: SoftwareType) {
  currentSoftware.value = software
  showAddVersionModal.value = true
}

function closeAddVersionModal() {
  showAddVersionModal.value = false
  currentSoftware.value = null
  versionForm.versionName = ''
  versionForm.file = null
  versionError.value = ''
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    versionForm.file = target.files[0]
  }
}

async function handleAddVersion() {
  if (!versionForm.versionName.trim() || !versionForm.file || !currentSoftware.value) return
  
  versionSubmitting.value = true
  versionError.value = ''
  
  try {
    // 模拟文件上传，实际项目中应该先上传文件获取URL
    const fileUrl = `/downloads/${versionForm.file.name}`
    
    const result = await dataService.createVersion({
      softwareTypeId: currentSoftware.value.id,
      versionName: versionForm.versionName.trim(),
      fileUrl: fileUrl,
      fileSize: versionForm.file.size
    })
    
      // 更新软件类型的最新版本
      const software = softwareTypes.value.find(s => s.id === currentSoftware.value!.id)
      if (software) {
        software.latestVersion = result
        software.updatedAt = Date.now()
      }
    
    closeAddVersionModal()
  } catch (err: any) {
    versionError.value = err?.message || '上传失败'
  } finally {
    versionSubmitting.value = false
  }
}

// 其他操作函数
async function viewAllVersions(software: SoftwareType) {
  currentSoftwareForHistory.value = software
  showVersionHistoryModal.value = true
  await loadVersionHistory(software.id)
}

// 加载版本历史
async function loadVersionHistory(softwareTypeId: number) {
  versionHistoryLoading.value = true
  try {
    const result = await dataService.getVersions(softwareTypeId, { 
      page: 1, 
      pageSize: 100 
    })
    if (result && Array.isArray(result.list)) {
      versionHistory.value = result.list
    } else if (Array.isArray(result)) {
      versionHistory.value = result
    } else {
      versionHistory.value = []
    }
  } catch (err) {
    console.error('加载版本历史失败:', err)
    versionHistory.value = []
  } finally {
    versionHistoryLoading.value = false
  }
}

// 关闭版本历史模态框
function closeVersionHistoryModal() {
  showVersionHistoryModal.value = false
  currentSoftwareForHistory.value = null
  versionHistory.value = []
}

async function editSoftware(software: SoftwareType) {
  // 弹窗编辑软件类型名称
  const newName = prompt('请输入新的软件类型名称:', software.name)
  if (newName && newName.trim() && newName.trim() !== software.name) {
    try {
      const result = await dataService.updateSoftwareType(software.id, {
        name: newName.trim()
      })
      
      // 更新本地数据
      Object.assign(software, result)
    } catch (err: any) {
      alert(`更新失败: ${err?.message || '未知错误'}`)
    }
  }
}

async function deleteSoftware(software: SoftwareType) {
  if (confirm(`确定要删除软件类型"${software.name}"吗？这将同时删除该类型下的所有版本，此操作不可撤销。`)) {
    try {
      await dataService.deleteSoftwareType(software.id)
      
      // 从本地列表中移除
      softwareTypes.value = softwareTypes.value.filter(s => s.id !== software.id)
    } catch (err: any) {
      alert(`删除失败: ${err?.message || '未知错误'}`)
    }
  }
}

function downloadVersion(version: Version) {
  if (version.fileUrl) {
    window.open(version.fileUrl, '_blank')
  }
}

async function editVersion(version: Version) {
  // 编辑版本信息 - 暂时只支持版本号修改
  const newVersionName = prompt('请输入新的版本号:', version.versionName)
  if (newVersionName && newVersionName.trim() && newVersionName.trim() !== version.versionName) {
    try {
      const result = await dataService.updateVersion(version.id, {
        versionName: newVersionName.trim()
      })
      
      // 更新本地数据
      Object.assign(version, result)
      
      // 如果这是最新版本，同步更新软件类型的最新版本信息
      const software = softwareTypes.value.find(s => s.latestVersion?.id === version.id)
      if (software) {
        software.latestVersion = result
        software.updatedAt = Date.now()
      }
      
      // 如果版本历史模态框打开，也更新版本历史列表
      if (showVersionHistoryModal.value) {
        const historyVersion = versionHistory.value.find(v => v.id === version.id)
        if (historyVersion) {
          Object.assign(historyVersion, result)
        }
      }
    } catch (err: any) {
      alert(`更新失败: ${err?.message || '未知错误'}`)
    }
  }
}

// 从版本历史中删除版本
async function deleteVersionFromHistory(version: Version) {
  if (confirm(`确定要删除版本"${version.versionName}"吗？此操作不可撤销。`)) {
    try {
      await dataService.deleteVersion(version.id)
      
      // 从版本历史列表中移除
      versionHistory.value = versionHistory.value.filter(v => v.id !== version.id)
      
      // 如果这是最新版本，需要更新软件类型的最新版本信息
      const software = softwareTypes.value.find(s => s.latestVersion?.id === version.id)
      if (software) {
        // 找到剩余版本中最新的一个
        const remainingVersions = versionHistory.value
        if (remainingVersions.length > 0) {
          const latestRemaining = remainingVersions.sort((a, b) => b.updatedAt - a.updatedAt)[0]
          software.latestVersion = latestRemaining
        } else {
          software.latestVersion = undefined
        }
        software.updatedAt = Date.now()
      }
    } catch (err: any) {
      alert(`删除失败: ${err?.message || '未知错误'}`)
    }
  }
}

// 初始化
onMounted(() => {
  loadProjectDetail()
})
</script>

<style scoped>
.project-detail {
  padding: var(--spacing-6);
  max-width: 1400px;
  margin: 0 auto;
}

/* Loading & Error States */
.loading-container,
.error-container {
  padding: var(--spacing-8);
  text-align: center;
}

.error-container h2 {
  color: var(--color-danger);
  margin-bottom: var(--spacing-2);
}

/* Project Header */
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
}

.breadcrumb-item {
  color: var(--color-primary);
  text-decoration: none;
}

.breadcrumb-item:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--color-text-3);
}

.breadcrumb-current {
  color: var(--color-text-2);
}

.project-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-2) 0;
  color: var(--color-text-1);
}

.project-description {
  color: var(--color-text-3);
  font-size: var(--font-size-lg);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* Software Section */
.software-section {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.section-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--color-text-1);
}

/* Loading & Empty States */
.loading-state,
.empty-state {
  padding: var(--spacing-8);
  text-align: center;
  color: var(--color-text-3);
}

.empty-content h3 {
  color: var(--color-text-2);
  margin-bottom: var(--spacing-2);
}

/* Software Grid */
.software-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-5);
}

.software-card {
  padding: var(--spacing-5);
  transition: all var(--transition-base);
}

.software-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-light);
}

.software-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.software-info {
  flex: 1;
}

.software-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-2) 0;
  color: var(--color-text-1);
}

.software-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.version-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-2);
  font-weight: var(--font-weight-medium);
}

.update-time,
.no-version {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
}

.software-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-left: var(--spacing-3);
}

/* Version Info Card */
.version-info-card {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-3);
}

.version-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.file-size {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.version-actions {
  display: flex;
  gap: var(--spacing-2);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 500px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.software-form,
.version-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input {
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-6);
  text-align: center;
  cursor: pointer;
  transition: border-color var(--transition-base);
}

.file-upload-area:hover {
  border-color: var(--color-primary);
}

.upload-placeholder {
  color: var(--color-text-3);
}

.file-info {
  color: var(--color-text-1);
}

.file-info .file-size {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  margin-top: var(--spacing-1);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

.btn-danger {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.btn-danger:hover {
  background: var(--color-danger-dark);
}

/* Version History Modal */
.version-history-modal {
  max-width: 800px;
  max-height: 80vh;
}

.version-history-content {
  padding: var(--spacing-5);
  max-height: 60vh;
  overflow-y: auto;
}

.version-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.version-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  font-weight: var(--font-weight-medium);
}

.version-history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.version-history-item {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  transition: all var(--transition-base);
}

.version-history-item:hover {
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-sm);
}

.version-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.version-info {
  flex: 1;
}

.version-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-2) 0;
  color: var(--color-text-1);
}

.version-meta {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
}

.version-size,
.version-time {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.version-actions {
  display: flex;
  gap: var(--spacing-2);
  margin-left: var(--spacing-3);
}

@media (max-width: 768px) {
  .project-detail {
    padding: var(--spacing-4);
  }
  
  .project-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .software-grid {
    grid-template-columns: 1fr;
  }
  
  .software-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .software-actions {
    width: 100%;
    margin-left: 0;
  }
  
  .version-details {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .version-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  /* Version History Modal - Mobile */
  .version-history-modal {
    max-width: 95vw;
    margin: var(--spacing-2);
  }
  
  .version-history-content {
    padding: var(--spacing-3);
    max-height: 70vh;
  }
  
  .version-item-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .version-actions {
    width: 100%;
    margin-left: 0;
    justify-content: flex-end;
  }
  
  .version-meta {
    flex-direction: column;
    gap: var(--spacing-1);
    align-items: flex-start;
  }
}
</style>

<template>
  <div class="project-content-manager">
    <div class="section-header">
      <h2 class="section-title">项目内容管理</h2>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-bar">
      <div class="search-box">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜索项目..." 
          class="search-input"
          @input="handleSearch"
        >
      </div>
      <div class="filter-options">
        <select v-model="statusFilter" class="filter-select" @change="handleFilter">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">已暂停</option>
        </select>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="projects-list">
      <div v-if="loading && projects.length === 0" class="loading-state">
        <p>正在加载项目...</p>
      </div>
      <div v-else-if="filteredProjects.length === 0" class="empty-state">
        <p>暂无符合条件的项目</p>
      </div>
      <div v-else class="projects-grid">
        <div 
          v-for="project in filteredProjects" 
          :key="project.id"
          class="project-card card"
        >
          <div class="project-header">
            <div class="project-title">
              <h3 class="project-name">{{ project.name }}</h3>
            </div>
            <div class="project-status">
              <span :class="['status-badge', project.status]">
                {{ project.status === 'active' ? '活跃' : '已暂停' }}
              </span>
            </div>
          </div>

          <div class="project-description">
            <p>{{ project.description || '暂无描述' }}</p>
          </div>

          <div class="project-meta">
            <div class="meta-item">
              <span class="meta-label">最后更新</span>
              <span class="meta-value">{{ formatTime(project.updatedAt) }}</span>
            </div>
          </div>

          <div class="project-actions">
            <button 
              class="btn btn-sm btn-primary" 
              @click="editProjectInfo(project)"
            >
              编辑信息
            </button>
            <button 
              class="btn btn-sm btn-outline" 
              @click="viewProjectDetail(project)"
            >
              查看详情
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目信息编辑弹窗 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑项目信息</h3>
          <button class="modal-close" @click="closeEditModal">×</button>
        </div>
        
        <form class="project-form" @submit.prevent="saveProjectInfo">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="form-section-title">基本信息</h4>
            
            <div class="form-group">
              <label class="form-label">项目名称</label>
              <input
                v-model="projectForm.name"
                type="text"
                class="form-input"
                :disabled="true"
                readonly
              />
              <div class="form-hint">项目名称不可修改</div>
            </div>

            <div class="form-group">
              <label class="form-label">项目描述</label>
              <textarea
                v-model="projectForm.description"
                class="form-textarea"
                placeholder="请输入项目描述..."
                rows="4"
              ></textarea>
            </div>
          </div>

          <div v-if="formError" class="error-message">
            {{ formError }}
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeEditModal">
              取消
            </button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? '保存中...' : '保存更改' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface Project {
  id: number
  name: string
  description?: string
  status: 'active' | 'inactive'
  createdAt: number
  updatedAt: number
}

interface ProjectForm {
  name: string
  description?: string
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const projects = ref<Project[]>([])
const searchQuery = ref('')
const statusFilter = ref('')

// 弹窗状态
const showEditModal = ref(false)
const editingProject = ref<Project | null>(null)

// 表单数据
const projectForm = reactive<ProjectForm>({
  name: '',
  description: ''
})

const formError = ref('')

// 计算属性
const filteredProjects = computed(() => {
  let result = projects.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(project => 
      project.name.toLowerCase().includes(query) ||
      (project.description && project.description.toLowerCase().includes(query))
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(project => project.status === statusFilter.value)
  }

  return result
})



// 时间格式化
function formatTime(timestamp: number) {
  const now = Date.now()
  const diff = now - timestamp
  const days = Math.floor(diff / 86400000)
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  
  return new Date(timestamp).toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  })
}

// 搜索处理
function handleSearch() {
  // 实时搜索，无需额外处理
}

// 过滤处理
function handleFilter() {
  // 重置搜索会在计算属性中处理
}

// 加载项目列表
async function loadProjects() {
  loading.value = true
  try {
    // 模拟API调用 - 获取开发者管理的项目
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    projects.value = [
      {
        id: 1,
        name: '智能家居控制系统',
        description: '一个综合的智能家居解决方案，包含多种设备固件和配套应用程序',
        status: 'active',
        createdAt: Date.now() - 86400000 * 60,
        updatedAt: Date.now() - 86400000 * 2
      },
      {
        id: 2,
        name: '传感器数据采集',
        description: '多传感器数据采集与处理系统，提供固件和数据分析工具',
        status: 'active',
        createdAt: Date.now() - 86400000 * 40,
        updatedAt: Date.now() - 86400000 * 5
      },
      {
        id: 3,
        name: '工业自动化控制',
        description: '工业自动化控制解决方案，包含PLC固件、上位机软件等',
        status: 'active',
        createdAt: Date.now() - 86400000 * 80,
        updatedAt: Date.now() - 86400000 * 10
      },
      {
        id: 4,
        name: '移动端监控平台',
        description: '设备监控移动应用开发项目，支持Android和iOS',
        status: 'inactive',
        createdAt: Date.now() - 86400000 * 120,
        updatedAt: Date.now() - 86400000 * 15
      }
    ]
  } catch (error) {
    console.error('加载项目列表失败:', error)
  } finally {
    loading.value = false
  }
}


// 编辑项目信息
function editProjectInfo(project: Project) {
  editingProject.value = project
  Object.assign(projectForm, {
    name: project.name,
    description: project.description || ''
  })
  showEditModal.value = true
}

// 查看项目详情
function viewProjectDetail(project: Project) {
  router.push(`/developer/projects/${project.id}`)
}



// 关闭编辑弹窗
function closeEditModal() {
  showEditModal.value = false
  editingProject.value = null
  formError.value = ''
  Object.assign(projectForm, {
    name: '',
    description: ''
  })
}

// 保存项目信息
async function saveProjectInfo() {
  formError.value = ''
  
  if (!editingProject.value) return
  
  // 基础验证
  if (!projectForm.description?.trim()) {
    formError.value = '请填写项目描述'
    return
  }
  
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新项目信息
    Object.assign(editingProject.value, {
      description: projectForm.description,
      updatedAt: Date.now()
    })
    
    closeEditModal()
  } catch (error) {
    console.error('保存失败:', error)
    formError.value = '保存失败，请重试'
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.project-content-manager {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 搜索和过滤栏 */
.filters-bar {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.filter-options {
  display: flex;
  gap: var(--spacing-3);
}

.filter-select {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* 项目列表 */
.loading-state,
.empty-state {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-8);
  font-size: var(--font-size-lg);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--spacing-6);
}

.project-card {
  padding: var(--spacing-5);
  transition: all var(--transition-base);
  border: 1px solid var(--border-color);
}

.project-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-light);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.project-title {
  flex: 1;
}

.project-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-2) 0;
}

.project-type {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.project-type.firmware {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.project-type.app {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.project-type.library {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.status-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.active {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-badge.inactive {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.project-description {
  margin-bottom: var(--spacing-4);
}

.project-description p {
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.project-meta {
  margin-bottom: var(--spacing-5);
}

.meta-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.meta-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  font-weight: var(--font-weight-medium);
}

.meta-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-1);
  font-weight: var(--font-weight-medium);
}

.project-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 800px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--bg-1);
  z-index: 1;
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-section-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input,
.form-textarea {
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input {
  height: 40px;
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-input:disabled {
  background: var(--bg-2);
  color: var(--color-text-3);
}

.form-hint {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  margin-top: var(--spacing-1);
}

.devices-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.devices-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.device-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.remove-device {
  background: none;
  border: none;
  color: var(--color-primary-dark);
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
}

.icon-upload {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md);
}

.current-icon img {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.no-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
}

.screenshots-upload {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.screenshots-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
}

.screenshot-item {
  position: relative;
  width: 120px;
  height: 80px;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.screenshot-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-screenshot {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: var(--font-size-xs);
}

.add-screenshot {
  align-self: flex-start;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  position: sticky;
  bottom: 0;
  background: var(--bg-1);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .filters-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .project-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .meta-grid {
    grid-template-columns: 1fr;
  }
  
  .project-actions {
    width: 100%;
  }
  
  .modal-content {
    max-width: none;
    margin: var(--spacing-2);
    max-height: 95vh;
  }
  
  .project-form {
    padding: var(--spacing-4);
  }
}
</style>

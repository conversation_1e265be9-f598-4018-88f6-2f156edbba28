<template>
  <div class="page page-projects">
    <div class="container-xl">
      <div class="page-header section-header">
        <h1 class="page-title">项目列表</h1>
        <p class="page-description">浏览和搜索所有可用的软件项目</p>
      </div>

      <div class="filters card">
        <div class="filter-row">
          <input v-model.trim="local.search" @keyup.enter="applyFilters" class="search-input" placeholder="搜索项目名称、关键词..." />
          <select v-model="local.sortBy" class="select">
            <option value="updatedAt">按更新时间</option>
            <option value="name">按名称</option>
          </select>
          <select v-model="local.order" class="select">
            <option value="desc">降序</option>
            <option value="asc">升序</option>
          </select>
          <button class="btn btn-primary" @click="applyFilters">查询</button>
          <button class="btn btn-outline" @click="resetFilters">重置</button>
        </div>
      </div>

      <div class="page-content">
        <div v-if="projects.loading" class="loading">加载中...</div>
        <div v-else class="projects-grid">
          <div class="project-card card" v-for="p in projects.list" :key="p.id">
            <div class="project-info">
              <h3 class="project-name">{{ p.name }}</h3>
              <p class="project-description">{{ p.description || '—' }}</p>
              <div class="project-meta">
                <span class="project-versions">{{ p.stats?.versionCount || 0 }} 个版本</span>
              </div>
            </div>
            <div class="project-actions">
              <RouterLink :to="`/project/${p.id}`" class="btn btn-secondary">查看详情</RouterLink>
            </div>
          </div>
        </div>

        <div class="pagination" v-if="projects.total > 0">
          <button class="btn btn-outline" :disabled="projects.query.page <= 1" @click="changePage(projects.query.page - 1)">上一页</button>
          <span class="page-info">第 {{ projects.query.page }} / {{ totalPages }} 页（共 {{ projects.total }} 条）</span>
          <button class="btn btn-outline" :disabled="projects.query.page >= totalPages" @click="changePage(projects.query.page + 1)">下一页</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, computed, watch } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'

const projects = useProjectsStore()
const route = useRoute()
const router = useRouter()

const local = reactive({
  search: '',
  sortBy: 'updatedAt' as 'updatedAt' | 'name',
  order: 'desc' as 'asc' | 'desc',
})

const totalPages = computed(() => Math.max(1, Math.ceil(projects.total / projects.query.pageSize)))

function syncFromRoute() {
  const q = route.query
  projects.query.page = q.page ? Number(q.page) : 1
  projects.query.pageSize = q.pageSize ? Number(q.pageSize) : 10
  projects.query.search = (q.search as string) || ''
  projects.query.sortBy = ((q.sortBy as string) || 'updatedAt') as any
  projects.query.order = ((q.order as string) || 'desc') as any

  local.search = projects.query.search
  local.sortBy = (projects.query.sortBy || 'updatedAt') as any
  local.order = (projects.query.order || 'desc') as any
}

function pushRoute() {
  const q = {
    page: String(projects.query.page),
    pageSize: String(projects.query.pageSize),
    search: projects.query.search || undefined,
    sortBy: projects.query.sortBy || undefined,
    order: projects.query.order || undefined,
  }
  router.replace({ query: q })
}

async function load() {
  await projects.loadList()
}

function applyFilters() {
  projects.query = {
    ...projects.query,
    page: 1,
    search: local.search,
    sortBy: local.sortBy,
    order: local.order,
  }
  pushRoute()
  load()
}

function resetFilters() {
  local.search = ''
  local.sortBy = 'updatedAt'
  local.order = 'desc'
  applyFilters()
}

function changePage(page: number) {
  projects.query.page = Math.max(1, Math.min(page, totalPages.value))
  pushRoute()
  load()
}

function mapType(t?: string) {
  if (t === 'firmware') return '固件'
  if (t === 'app') return '应用'
  if (t === 'library') return '库'
  return '—'
}

onMounted(async () => {
  syncFromRoute()
  await load()
})

watch(() => route.query, () => {
  syncFromRoute()
  load()
})
</script>

<style scoped>
.page { padding: var(--spacing-6) 0; }

.page-header { text-align: center; margin-bottom: var(--spacing-8); }
.page-title { font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--color-text-1); margin: 0 0 var(--spacing-3) 0; }
.page-description { font-size: var(--font-size-lg); color: var(--color-text-2); margin: 0; }

.filters { padding: var(--spacing-4); margin-bottom: var(--spacing-6); }
.filter-row { display: flex; gap: var(--spacing-3); flex-wrap: wrap; align-items: center; }
.search-input { flex: 1; padding: var(--spacing-2) var(--spacing-3); border: 1px solid var(--border-color); border-radius: var(--radius-md); }
.select { padding: var(--spacing-2) var(--spacing-3); border: 1px solid var(--border-color); border-radius: var(--radius-md); }
.loading { padding: var(--spacing-8); text-align: center; color: var(--color-text-3); }
.projects-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: var(--spacing-6); }
.project-card { padding: var(--spacing-6); }
.project-info { margin-bottom: var(--spacing-4); }
.project-name { font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); color: var(--color-text-1); margin: 0 0 var(--spacing-2) 0; }
.project-description { color: var(--color-text-2); margin: 0 0 var(--spacing-3) 0; }
.project-meta { display: flex; gap: var(--spacing-2); flex-wrap: wrap; }
.project-type, .project-versions { font-size: var(--font-size-sm); padding: var(--spacing-1) var(--spacing-2); border-radius: var(--radius-sm); background: var(--bg-2); color: var(--color-text-3); }
.project-downloads { font-size: var(--font-size-sm); padding: var(--spacing-1) var(--spacing-2); border-radius: var(--radius-sm); background: var(--bg-2); color: var(--color-text-3); }
.project-actions { display: flex; justify-content: flex-end; }
.pagination { display: flex; align-items: center; justify-content: center; gap: var(--spacing-3); margin-top: var(--spacing-6); }
.page-info { color: var(--color-text-3); }

@media (max-width: 768px) {
  .filter-row { flex-direction: column; align-items: stretch; }
  .search-input { width: 100%; }
}
</style>

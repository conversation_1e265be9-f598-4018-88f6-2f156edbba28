import { defineStore } from 'pinia'
import { developer<PERSON><PERSON><PERSON>, developer<PERSON><PERSON><PERSON>, developer<PERSON><PERSON><PERSON><PERSON> } from '@/api/developerAuth'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, DeveloperLoginResponse } from '@/types/domain'

interface DeveloperAuthState {
  token: string | null
  refreshToken: string | null
  expiresAt: number | null
  user: DeveloperUser | null
  _refreshTimer?: number | null
}

const STORAGE_KEY = 'developer_auth_v1'

function persist(state: Partial<DeveloperAuthState>) {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
  } catch {}
}

function restore(): Partial<DeveloperAuthState> {
  try {
    const raw = localStorage.getItem(STORAGE_KEY)
    if (!raw) return {}
    return JSON.parse(raw) as Partial<DeveloperAuthState>
  } catch {
    return {}
  }
}

export const useDeveloperAuthStore = defineStore('developerAuth', {
  state: (): DeveloperAuthState => ({
    token: null,
    refreshToken: null,
    expiresAt: null,
    user: null,
    _refreshTimer: null,
  }),
  
  getters: {
    isAuthenticated(state): boolean {
      if (!state.token || !state.user) return false
      if (state.expiresAt && Date.now() > state.expiresAt) return false
      return state.user.userType === 'developer'
    },
    
    userPermissions(state): string[] {
      return state.user?.permissions || []
    },
    
    accessibleProjects(state): string[] {
      return state.user?.projects || []
    }
  },
  
  actions: {
    initialize(): void {
      const restored = restore()
      if (restored.token) this.token = restored.token
      if (restored.refreshToken) this.refreshToken = restored.refreshToken
      if (restored.expiresAt) this.expiresAt = restored.expiresAt
      if (restored.user) this.user = restored.user as DeveloperUser
      
      // 同步到 axios 拦截器
      if (this.token && this.user?.userType === 'developer') {
        localStorage.setItem('developer_token', this.token)
      }
      
      this.setupAutoRefresh()
    },
    
    async login(username: string, password: string): Promise<void> {
      const res = await developerLogin(username, password)
      const ttl = res.expiresIn ? res.expiresIn * 1000 : 2 * 60 * 60 * 1000
      
      this.token = res.token
      this.refreshToken = res.refreshToken || null
      this.expiresAt = Date.now() + ttl
      this.user = res.user
      
      // 存储到本地
      localStorage.setItem('developer_token', this.token)
      persist({ 
        token: this.token, 
        refreshToken: this.refreshToken, 
        expiresAt: this.expiresAt,
        user: this.user 
      })
      
      this.setupAutoRefresh()
    },
    
    async fetchProfile(): Promise<void> {
      const user = await developerProfile()
      this.user = user
      persist({ 
        user: this.user, 
        token: this.token, 
        refreshToken: this.refreshToken, 
        expiresAt: this.expiresAt 
      })
    },
    
    async refresh(): Promise<void> {
      if (!this.refreshToken) return
      
      const res = await developerRefresh(this.refreshToken)
      const ttl = res.expiresIn ? res.expiresIn * 1000 : 2 * 60 * 60 * 1000
      
      this.token = res.token
      this.refreshToken = res.refreshToken || this.refreshToken
      this.expiresAt = Date.now() + ttl
      
      localStorage.setItem('developer_token', this.token)
      persist({ 
        token: this.token, 
        refreshToken: this.refreshToken, 
        expiresAt: this.expiresAt, 
        user: this.user 
      })
      
      this.setupAutoRefresh()
    },
    
    logout(): void {
      this.token = null
      this.refreshToken = null
      this.expiresAt = null
      this.user = null
      
      localStorage.removeItem('developer_token')
      persist({})
      
      if (this._refreshTimer) {
        window.clearTimeout(this._refreshTimer)
        this._refreshTimer = null
      }
    },
    
    setupAutoRefresh(): void {
      if (!this.expiresAt) return
      
      if (this._refreshTimer) {
        window.clearTimeout(this._refreshTimer)
        this._refreshTimer = null
      }
      
      const msLeft = this.expiresAt - Date.now()
      // 在过期前5分钟刷新，最少提前30秒
      const refreshIn = Math.max(msLeft - 5 * 60 * 1000, 30 * 1000)
      
      this._refreshTimer = window.setTimeout(() => {
        this.refresh().catch(() => {})
      }, refreshIn)
    },
    
    hasPermission(permission: string): boolean {
      return this.userPermissions.includes(permission)
    },
    
    hasProjectAccess(projectId: string): boolean {
      return this.accessibleProjects.includes(projectId) || this.accessibleProjects.includes('*')
    }
  },
})

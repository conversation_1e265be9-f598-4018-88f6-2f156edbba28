/// <reference types="vite/client" />

// Ensure ImportMeta has env in all TS tools
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE?: string
  readonly VITE_USE_MOCK?: string
  readonly VITE_REPORT_ERROR?: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Vue SFC shim for TS tooling
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

/**
 * 真实API数据服务实现
 * 
 * 当切换到生产环境时，这里将调用真实的API接口
 */

import type { 
  ProjectItem, 
  ReleaseItem, 
  UserItem, 
  PageResult,
  SearchPageResult,
  ReleaseDetail 
} from '@/types/domain'
import type {
  UnifiedDataService,
  SystemStats,
  DeveloperStats,
  Activity,
  DeveloperInfo,
  ProjectAssignment,
  SoftwareType,
  Version,
  ProjectSummary,
  QueryParams
} from './types'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// API请求封装
async function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers
    },
    ...options
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }))
    throw new Error(error.message || `API Error: ${response.status}`)
  }

  const result = await response.json()
  
  // 假设API返回格式为 { code: 0, message: 'OK', data: T }
  if (result.code !== 0) {
    throw new Error(result.message || 'API Error')
  }

  return result.data
}

// 构建查询字符串
function buildQueryString(params: QueryParams = {}): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// 真实API数据服务实现
export const liveDataService: UnifiedDataService = {
  // 系统统计
  async getSystemStats(): Promise<SystemStats> {
    return apiRequest<SystemStats>('/admin/stats')
  },

  async getDeveloperStats(developerId: number): Promise<DeveloperStats> {
    return apiRequest<DeveloperStats>(`/developer/${developerId}/stats`)
  },

  // 项目管理
  async getProjects(params?: QueryParams): Promise<PageResult<ProjectItem>> {
    const query = buildQueryString(params)
    return apiRequest<PageResult<ProjectItem>>(`/projects${query}`)
  },

  async getProjectDetail(id: number): Promise<ProjectItem> {
    return apiRequest<ProjectItem>(`/projects/${id}`)
  },

  async createProject(data: Partial<ProjectItem>): Promise<ProjectItem> {
    return apiRequest<ProjectItem>('/projects', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  async updateProject(id: number, data: Partial<ProjectItem>): Promise<ProjectItem> {
    return apiRequest<ProjectItem>(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  async deleteProject(id: number): Promise<void> {
    await apiRequest<void>(`/projects/${id}`, {
      method: 'DELETE'
    })
  },

  // 项目概览
  async getProjectSummaries(params?: QueryParams): Promise<ProjectSummary[]> {
    const query = buildQueryString(params)
    return apiRequest<ProjectSummary[]>(`/projects/summaries${query}`)
  },

  // 软件类型管理
  async getSoftwareTypes(projectId: number): Promise<SoftwareType[]> {
    return apiRequest<SoftwareType[]>(`/projects/${projectId}/software-types`)
  },

  async createSoftwareType(projectId: number, data: { name: string }): Promise<SoftwareType> {
    return apiRequest<SoftwareType>(`/projects/${projectId}/software-types`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  async deleteSoftwareType(id: number): Promise<void> {
    await apiRequest<void>(`/software-types/${id}`, {
      method: 'DELETE'
    })
  },

  // 版本管理
  async getVersions(softwareTypeId: number): Promise<Version[]> {
    return apiRequest<Version[]>(`/software-types/${softwareTypeId}/versions`)
  },

  async getVersionDetail(id: number): Promise<Version> {
    return apiRequest<Version>(`/versions/${id}`)
  },

  async createVersion(data: Partial<Version>): Promise<Version> {
    return apiRequest<Version>('/versions', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  async updateVersion(id: number, data: Partial<Version>): Promise<Version> {
    return apiRequest<Version>(`/versions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  async deleteVersion(id: number): Promise<void> {
    await apiRequest<void>(`/versions/${id}`, {
      method: 'DELETE'
    })
  },

  // 版本发布
  async getReleases(params?: QueryParams): Promise<PageResult<ReleaseItem>> {
    const query = buildQueryString(params)
    return apiRequest<PageResult<ReleaseItem>>(`/releases${query}`)
  },

  async getReleaseDetail(id: number): Promise<ReleaseDetail> {
    return apiRequest<ReleaseDetail>(`/releases/${id}`)
  },

  async createRelease(data: Partial<ReleaseItem>): Promise<ReleaseItem> {
    return apiRequest<ReleaseItem>('/releases', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 开发者管理
  async getDevelopers(params?: QueryParams): Promise<PageResult<DeveloperInfo>> {
    const query = buildQueryString(params)
    return apiRequest<PageResult<DeveloperInfo>>(`/admin/developers${query}`)
  },

  async getDeveloperDetail(id: number): Promise<DeveloperInfo> {
    return apiRequest<DeveloperInfo>(`/admin/developers/${id}`)
  },

  async createDeveloper(data: { username: string; password: string }): Promise<DeveloperInfo> {
    return apiRequest<DeveloperInfo>('/admin/developers', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  async updateDeveloper(id: number, data: Partial<DeveloperInfo>): Promise<DeveloperInfo> {
    return apiRequest<DeveloperInfo>(`/admin/developers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  async deleteDeveloper(id: number): Promise<void> {
    await apiRequest<void>(`/admin/developers/${id}`, {
      method: 'DELETE'
    })
  },

  // 用户管理
  async getUsers(params?: QueryParams): Promise<PageResult<UserItem>> {
    const query = buildQueryString(params)
    return apiRequest<PageResult<UserItem>>(`/users${query}`)
  },

  async getUserDetail(id: number): Promise<UserItem> {
    return apiRequest<UserItem>(`/users/${id}`)
  },

  async disableUser(id: number): Promise<void> {
    await apiRequest<void>(`/users/${id}/disable`, {
      method: 'POST'
    })
  },

  // 活动记录
  async getSystemActivities(params?: QueryParams): Promise<Activity[]> {
    const query = buildQueryString(params)
    return apiRequest<Activity[]>(`/admin/activities${query}`)
  },

  async getDeveloperActivities(developerId: number, params?: QueryParams): Promise<Activity[]> {
    const query = buildQueryString(params)
    return apiRequest<Activity[]>(`/developer/${developerId}/activities${query}`)
  },

  // 权限管理
  async getPermissionAssignments(): Promise<ProjectAssignment[]> {
    return apiRequest<ProjectAssignment[]>('/admin/permissions/assignments')
  },

  async assignProjectToDeveloper(developerId: number, projectId: number, permissions: string[]): Promise<void> {
    await apiRequest<void>('/admin/permissions/assign', {
      method: 'POST',
      body: JSON.stringify({ developerId, projectId, permissions })
    })
  },

  async revokeProjectFromDeveloper(developerId: number, projectId: number): Promise<void> {
    await apiRequest<void>('/admin/permissions/revoke', {
      method: 'POST',
      body: JSON.stringify({ developerId, projectId })
    })
  },

  // 搜索
  async search(query: string, params?: QueryParams): Promise<SearchPageResult> {
    const searchParams = buildQueryString({ ...params, q: query })
    return apiRequest<SearchPageResult>(`/search${searchParams}`)
  }
}

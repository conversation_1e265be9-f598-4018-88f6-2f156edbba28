import type { Router } from 'vue-router'
import instance from '@/api/http'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import { useAdminAuthStore } from '@/stores/adminAuth'
import type { AxiosRequestConfig } from 'axios'

let isDeveloperRefreshing = false
let isAdminRefreshing = false
let developerRefreshPromise: Promise<void> | null = null
let adminRefreshPromise: Promise<void> | null = null

export function setupHttpInterceptors(router: Router) {
  // Request interceptor to add auth headers
  instance.interceptors.request.use(
    (config) => {
      const developerAuth = useDeveloperAuthStore()
      const adminAuth = useAdminAuthStore()
      
      // 根据请求路径决定使用哪个认证系统
      if (config.url?.includes('/auth/developer/') || config.url?.includes('/developer/')) {
        if (developerAuth.token) {
          config.headers = config.headers || {}
          config.headers['Authorization'] = `Bearer ${developerAuth.token}`
        }
      } else if (config.url?.includes('/auth/admin/') || config.url?.includes('/admin/')) {
        if (adminAuth.token) {
          config.headers = config.headers || {}
          config.headers['Authorization'] = `Bearer ${adminAuth.token}`
        }
      } else {
        // 对于通用请求，优先使用当前认证用户的token
        const currentAuth = developerAuth.isAuthenticated ? developerAuth : 
                           adminAuth.isAuthenticated ? adminAuth : null
        if (currentAuth?.token) {
          config.headers = config.headers || {}
          config.headers['Authorization'] = `Bearer ${currentAuth.token}`
        }
      }
      
      return config
    },
    (error) => Promise.reject(error)
  )

  // Response interceptor to handle 401 errors
  instance.interceptors.response.use(
    (resp) => resp,
    async (error) => {
      const status = error?.response?.status
      const config = (error?.config || {}) as AxiosRequestConfig
      const developerAuth = useDeveloperAuthStore()
      const adminAuth = useAdminAuthStore()

      if (status === 401 && !config._retry) {
        config._retry = true
        
        try {
          // 判断是哪种认证类型的请求失败
          const isDeveloperRequest = config.url?.includes('/auth/developer/') || 
                                   config.url?.includes('/developer/') ||
                                   (developerAuth.isAuthenticated && !adminAuth.isAuthenticated)
          
          if (isDeveloperRequest && developerAuth.refreshToken) {
            // 开发者token刷新
            if (!isDeveloperRefreshing) {
              isDeveloperRefreshing = true
              developerRefreshPromise = developerAuth.refresh().finally(() => {
                isDeveloperRefreshing = false
              })
            }
            await developerRefreshPromise
            
            if (developerAuth.token) {
              config.headers = config.headers || {}
              config.headers['Authorization'] = `Bearer ${developerAuth.token}`
              return instance.request(config)
            }
          } else if (adminAuth.refreshToken) {
            // 管理员token刷新
            if (!isAdminRefreshing) {
              isAdminRefreshing = true
              adminRefreshPromise = adminAuth.refresh().finally(() => {
                isAdminRefreshing = false
              })
            }
            await adminRefreshPromise
            
            if (adminAuth.token) {
              config.headers = config.headers || {}
              config.headers['Authorization'] = `Bearer ${adminAuth.token}`
              return instance.request(config)
            }
          }
        } catch (refreshError) {
          // 刷新token失败，清理状态并重定向
          if (config.url?.includes('/auth/developer/') || config.url?.includes('/developer/')) {
            developerAuth.logout()
            const redirect = encodeURIComponent(router.currentRoute.value.fullPath)
            router.replace({ name: 'DeveloperLogin', query: { redirect } })
          } else if (config.url?.includes('/auth/admin/') || config.url?.includes('/admin/')) {
            adminAuth.logout()
            const redirect = encodeURIComponent(router.currentRoute.value.fullPath)
            router.replace({ name: 'AdminLogin', query: { redirect } })
          } else {
            // 对于通用请求，根据当前认证状态决定重定向
            if (developerAuth.isAuthenticated) {
              developerAuth.logout()
              router.replace({ name: 'DeveloperLogin' })
            } else if (adminAuth.isAuthenticated) {
              adminAuth.logout()
              router.replace({ name: 'AdminLogin' })
            }
          }
          return Promise.reject(refreshError)
        }
      }

      return Promise.reject(error)
    }
  )
}



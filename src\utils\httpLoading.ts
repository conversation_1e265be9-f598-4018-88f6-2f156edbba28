import { useLoading } from '@/composables/useLoading'

let inFlightCount = 0
let progressTimer: number | null = null
let currentProgress = 0

function beginProgress() {
  const { showTopProgress, updateTopProgress } = useLoading()
  showTopProgress()
  currentProgress = 0
  if (progressTimer) {
    window.clearInterval(progressTimer)
    progressTimer = null
  }
  progressTimer = window.setInterval(() => {
    // 缓慢推进到 90%
    const inc = Math.random() * 10 + 5 // 5-15
    currentProgress = Math.min(90, currentProgress + inc)
    updateTopProgress(Math.floor(currentProgress))
  }, 120)
}

function endProgress() {
  const { updateTopProgress, hideTopProgress } = useLoading()
  if (progressTimer) {
    window.clearInterval(progressTimer)
    progressTimer = null
  }
  updateTopProgress(100)
  hideTopProgress()
}

export function onRequestStart(key?: string) {
  if (inFlightCount === 0) beginProgress()
  inFlightCount += 1
  return key || `http_${Date.now()}_${Math.random().toString(36).slice(2)}`
}

export function onRequestEnd() {
  inFlightCount = Math.max(0, inFlightCount - 1)
  if (inFlightCount === 0) endProgress()
}



type EventHandler<T = any> = (payload: T) => void

export interface EventMap {
  'release:created': { id: number; projectId: number; version: string }
  'upload:progress': { progress: number }
  'event:log': { event: string; payload?: any }
}

class TypedEventBus<M extends Record<string, any>> {
  private listeners: Map<keyof M, Set<EventHandler<any>>> = new Map()

  on<K extends keyof M>(event: K, handler: EventHandler<M[K]>): () => void {
    const set = this.listeners.get(event) || new Set()
    set.add(handler as EventHandler<any>)
    this.listeners.set(event, set)
    return () => this.off(event, handler)
  }

  off<K extends keyof M>(event: K, handler: EventHandler<M[K]>): void {
    const set = this.listeners.get(event)
    if (!set) return
    set.delete(handler as EventHandler<any>)
  }

  emit<K extends keyof M>(event: K, payload: M[K]): void {
    const set = this.listeners.get(event)
    if (!set) return
    set.forEach((h) => h(payload))
    // 调试事件
    const debug = this.listeners.get('event:log' as keyof M)
    debug?.forEach((h) => h({ event, payload } as any))
  }
}

export const eventBus = new TypedEventBus<EventMap>()



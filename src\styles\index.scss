@use './tokens';
@use './reset';
@use './utilities';
@use './responsive';
@use './foundation';

/* Base */
html, body {
  background-color: var(--bg-1);
  color: var(--color-text-1);
  font-family: var(--font-sans);
  line-height: var(--line-height-base);
}

a {
  color: var(--color-primary);
  text-decoration: none;
}

/* Element Plus theme bridge (CSS vars) */
:root {
  --el-color-primary: var(--color-primary);
  --el-color-success: var(--color-success);
  --el-color-warning: var(--color-warning);
  --el-color-danger: var(--color-danger);
  --el-color-error: var(--color-danger);
  --el-color-info: var(--color-info);
}

/* Utilities are already loaded above to satisfy Sass rule order */



<template>
  <Teleport to="body">
    <Transition name="top-progress">
      <div v-if="visible" class="top-progress">
        <div 
          class="progress-bar"
          :style="{ 
            width: `${progress}%`,
            backgroundColor: color 
          }"
        />
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  /** 是否显示 */
  visible?: boolean
  /** 进度百分比 */
  progress?: number
  /** 进度条颜色 */
  color?: string
  /** 进度条高度 */
  height?: number
}

withDefaults(defineProps<Props>(), {
  visible: false,
  progress: 0,
  color: 'var(--color-primary)',
  height: 3
})
</script>

<style scoped>
.top-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: v-bind('height + "px"');
  z-index: 9998;
  background: rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.2s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6));
  animation: progress-glow 2s infinite;
}

@keyframes progress-glow {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

/* 过渡动画 */
.top-progress-enter-active,
.top-progress-leave-active {
  transition: all 0.3s ease;
}

.top-progress-enter-from {
  transform: translateY(-100%);
}

.top-progress-leave-to {
  opacity: 0;
}

/* 深色主题适配 */
[data-theme="dark"] .top-progress {
  background: rgba(255, 255, 255, 0.1);
}

/* 不同状态的颜色 */
.top-progress--success .progress-bar {
  background: var(--color-success);
}

.top-progress--warning .progress-bar {
  background: var(--color-warning);
}

.top-progress--error .progress-bar {
  background: var(--color-danger);
}
</style>

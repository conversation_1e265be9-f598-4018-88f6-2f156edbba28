// 基础分页结果
export interface PageResult<T> {
  list: T[]
  page: number
  pageSize: number
  total: number
}

// 领域枚举
export type ProjectType = 'firmware' | 'app' | 'library'
export type ReleaseType = 'stable' | 'beta' | 'alpha'
export type ArtifactKind = 'firmware' | 'android-demo' | 'sdk' | 'app' | 'library'
export type ReleaseStatus = 'pending' | 'success' | 'failed'
export type UserStatus = 'active' | 'disabled'

// 项目 - 简化版本，只包含基本信息
export interface ProjectItem {
  id: number
  name: string
  description?: string
  status: 'active' | 'inactive'
  createdAt: number
  updatedAt: number
}

// 软件类型
export interface SoftwareType {
  id: number
  projectId: number
  name: string
  latestVersion?: Version
  updatedAt: number
}

// 版本
export interface Version {
  id: number
  softwareTypeId: number
  versionName: string
  fileUrl?: string
  fileSize?: number
  updatedAt: number
}

// 版本
export interface ReleaseItem {
  id: number
  projectId: number
  projectName?: string
  version: string
  changelog: string
  createdAt: number
  status: ReleaseStatus
  size?: number
  type?: ReleaseType
  artifact?: ArtifactKind
  downloadUrl?: string
}

// 用户
export interface UserItem {
  id: number
  name: string
  username: string
  roles: string[]
  email: string
  status: UserStatus
}

// 搜索
export type SearchKind = 'project' | 'release'

export interface SearchProjectItem {
  id: number
  name: string
  description?: string
  status?: 'active' | 'inactive'
  createdAt?: number
  updatedAt?: number
}

export interface SearchReleaseItem {
  id: number
  projectId: number
  projectName?: string
  version: string
  changelog?: string
  createdAt: number
  status: ReleaseStatus
  size?: number
  type?: ReleaseType
  downloadUrl?: string
}

export interface SearchResultItem {
  kind: SearchKind
  project?: SearchProjectItem
  release?: SearchReleaseItem
  highlight?: { name?: string; description?: string; version?: string }
}

export interface Facets {
  type: Record<string, number>
  device: Record<string, number>
  versionType: Record<string, number>
}

export interface SearchPageResult<T = SearchResultItem> extends PageResult<T> {
  facets?: Facets
}

// 认证基础接口
interface BaseUser {
  id: number | string
  name: string
  username: string
  email: string
}

// 开发者用户
export interface DeveloperUser extends BaseUser {
  userType: 'developer'
  permissions?: string[]
  projects?: string[] // 可访问的项目ID列表
}

// 管理员用户  
export interface AdminUser extends BaseUser {
  userType: 'admin'
  permissions?: string[]
  level?: 'super' | 'normal' // 管理员级别
}

// 统一用户类型（向后兼容）
export type AuthUser = DeveloperUser | AdminUser

// 登录响应
export interface LoginResponse {
  token: string
  refreshToken?: string
  user?: AuthUser
  expiresIn?: number
}

// 开发者登录响应
export interface DeveloperLoginResponse {
  token: string
  refreshToken?: string
  user: DeveloperUser
  expiresIn?: number
}

// 管理员登录响应
export interface AdminLoginResponse {
  token: string
  refreshToken?: string
  user: AdminUser
  expiresIn?: number
}


// 文件资产（用于版本资源与下载）
export interface FileAsset {
  id?: string | number
  name: string
  size: number
  mime?: string
  url: string
  signature?: string
  protected?: boolean
}

// 版本详情（扩展 ReleaseItem）
export interface ReleaseDetail extends ReleaseItem {
  assets?: FileAsset[]
}

// 下载任务与状态
export type DownloadStatus =
  | 'idle'
  | 'queued'
  | 'downloading'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'

export interface DownloadTask {
  id: string
  url: string
  filename: string
  size?: number
  receivedBytes: number
  status: DownloadStatus
  speedBps?: number
  etaSeconds?: number
  error?: string
  createdAt: number
  updatedAt: number
  releaseId?: number
  projectId?: number
  supportsRange?: boolean
  chunks?: Uint8Array[]
}



export default [
  {
    method: 'POST',
    path: '/monitor/error',
    handler: async ({ body }) => {
      const payload = body || {}
      // 仅用于开发调试
      try {
        // eslint-disable-next-line no-console
        console.log('[client-error]', JSON.stringify(payload))
      } catch {}
      return { code: 0, message: 'OK', data: true }
    },
  },
  {
    method: 'POST',
    path: '/monitor/metric',
    handler: async ({ body }) => {
      const payload = body || {}
      try {
        // eslint-disable-next-line no-console
        console.log('[client-metric]', JSON.stringify(payload))
      } catch {}
      return { code: 0, message: 'OK', data: true }
    },
  },
  {
    method: 'GET',
    path: '/monitor/summary',
    handler: async () => {
      // 简单返回固定结构，真实环境由服务端统计
      return {
        code: 0,
        message: 'OK',
        data: {
          successRate: 0.98,
          avgLatencyMs: 120,
          errorByStatus: { '500': 2, '404': 5 },
          topSlowApis: [
            { url: '/projects', p95: 380 },
            { url: '/releases', p95: 420 },
          ],
        },
      }
    },
  },
]



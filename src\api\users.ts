import { request } from '@/api/http'
import type { UserItem, PageResult } from '@/types/domain'

export function fetchUsers(params: { page?: number; pageSize?: number }) {
  return request<PageResult<UserItem>>({
    url: '/users',
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['users:list'] },
  })
}

export function fetchUserDetail(id: number) {
  return request<UserItem>({
    url: `/users/${id}`,
    method: 'GET',
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 5 * 60_000, tags: [`user:${id}`] },
  })
}

export function disableUser(id: number) {
  return request<boolean>({ url: `/users/${id}/disable`, method: 'POST' })
}



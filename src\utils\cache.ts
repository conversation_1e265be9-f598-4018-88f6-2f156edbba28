export interface CacheOptions {
  ttlMs?: number
}

interface CacheEntry<T> {
  value: T
  expireAt?: number
}

export class SimpleCache<TValue> {
  private store = new Map<string, CacheEntry<TValue>>()
  private defaultTtl?: number

  constructor(options?: CacheOptions) {
    this.defaultTtl = options?.ttlMs
  }

  set(key: string, value: TValue, ttlMs?: number): void {
    const ttl = ttlMs ?? this.defaultTtl
    const entry: CacheEntry<TValue> = { value, expireAt: ttl ? Date.now() + ttl : undefined }
    this.store.set(key, entry)
  }

  get(key: string): TValue | undefined {
    const entry = this.store.get(key)
    if (!entry) return undefined
    if (entry.expireAt && Date.now() > entry.expireAt) {
      this.store.delete(key)
      return undefined
    }
    return entry.value
  }

  delete(key: string): void {
    this.store.delete(key)
  }

  clear(): void {
    this.store.clear()
  }
}



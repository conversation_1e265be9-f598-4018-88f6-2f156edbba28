import { defineStore } from 'pinia'
import { getDataService } from '@/data'
import type { ProjectItem, QueryParams } from '@/data'
import { SimpleCache } from '@/utils/cache'
import { PersistedCache } from '@/utils/persistedCache'

const dataService = getDataService()

interface QueryState {
  page: number
  pageSize: number
  search: string
  type?: string
  device?: string
  tags?: string
  sortBy?: 'name' | 'updatedAt'
  order?: 'asc' | 'desc'
}

interface ProjectsState {
  list: ProjectItem[]
  total: number
  query: QueryState
  loading: boolean
  error: string | null
  detailCache: Map<number, ProjectItem>
  ttlCache: SimpleCache<ProjectItem>
  persisted: PersistedCache<ProjectItem>
}

export const useProjectsStore = defineStore('projects', {
  state: (): ProjectsState => ({
    list: [],
    total: 0,
    query: { page: 1, pageSize: 10, search: '' },
    loading: false,
    error: null,
    detailCache: new Map<number, ProjectItem>(),
    ttlCache: new SimpleCache<ProjectItem>({ ttlMs: 5 * 60 * 1000 }),
    persisted: new PersistedCache<ProjectItem>({ ttlMs: 30 * 60 * 1000, storageKey: 'project_detail_cache_v1' }),
  }),
  actions: {
    async loadList(params?: Partial<QueryState>) {
      if (params) this.query = { ...this.query, ...params }
      const { withLoading } = await import('@/utils/async')
      await withLoading(this, async () => {
        const queryParams: QueryParams = {
          page: this.query.page,
          pageSize: this.query.pageSize,
          search: this.query.search || undefined,
          type: this.query.type || undefined,
          sortBy: this.query.sortBy || undefined,
          order: this.query.order || undefined
        }
        const res = await dataService.getProjects(queryParams)
        this.list = res.list
        this.total = res.total
      })
    },
    async getDetail(id: number): Promise<ProjectItem | undefined> {
      const cached = this.ttlCache.get(String(id)) || this.persisted.get(String(id))
      if (cached) return cached
      if (this.detailCache.has(id)) return this.detailCache.get(id)
      const res = await dataService.getProjectDetail(id)
      this.detailCache.set(id, res)
      this.ttlCache.set(String(id), res)
      this.persisted.set(String(id), res)
      return res
    },
    clearCache() {
      this.detailCache.clear()
      this.ttlCache.clear()
      this.persisted.clear()
    },
  },
})



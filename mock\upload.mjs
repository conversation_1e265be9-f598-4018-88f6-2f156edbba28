export default [
  {
    method: 'POST',
    path: '/upload',
    handler: async ({ body }) => {
      const name = (body && (body.name || body.filename)) || `file-${Date.now()}.zip`
      return {
        code: 0,
        message: 'OK',
        data: {
          id: String(Date.now()),
          name,
          size: body && body.size ? Number(body.size) : Math.floor(Math.random() * 10_000_000),
          url: `/files/${encodeURIComponent(name)}`,
        },
      }
    },
  },
]



import { request } from '@/api/http'
import type { AdminUser, AdminLoginResponse } from '@/types/domain'

/**
 * 管理员登录
 */
export function adminLogin(username: string, password: string) {
  return request<AdminLoginResponse>({ 
    url: '/auth/admin/login', 
    method: 'POST', 
    data: { username, password } 
  })
}

/**
 * 刷新管理员令牌
 */
export function adminRefresh(refreshToken: string) {
  return request<AdminLoginResponse>({ 
    url: '/auth/admin/refresh', 
    method: 'POST', 
    data: { refreshToken } 
  })
}

/**
 * 获取管理员个人信息
 */
export function adminProfile() {
  return request<AdminUser>({ 
    url: '/auth/admin/profile', 
    method: 'GET' 
  })
}

/**
 * 修改管理员密码
 */
export function updateAdminPassword(oldPassword: string, newPassword: string) {
  return request<{ success: boolean }>({ 
    url: '/auth/admin/password', 
    method: 'PUT', 
    data: { oldPassword, newPassword } 
  })
}

/**
 * 管理员登出
 */
export function adminLogout() {
  return request<{ success: boolean }>({ 
    url: '/auth/admin/logout', 
    method: 'POST' 
  })
}

export interface PersistedCacheOptions {
  ttlMs?: number
  storageKey: string
}

interface CacheEntry<T> {
  value: T
  expireAt?: number
}

export class PersistedCache<TValue> {
  private memory = new Map<string, CacheEntry<TValue>>()
  private readonly ttl?: number
  private readonly key: string

  constructor(options: PersistedCacheOptions) {
    this.ttl = options.ttlMs
    this.key = options.storageKey
    this.restore()
  }

  private restore() {
    try {
      const raw = localStorage.getItem(this.key)
      if (!raw) return
      const obj = JSON.parse(raw) as Record<string, CacheEntry<TValue>>
      let changed = false
      const now = Date.now()
      Object.entries(obj).forEach(([k, v]) => {
        if (v && (!v.expireAt || now <= v.expireAt)) {
          this.memory.set(k, v)
        } else {
          changed = true
        }
      })
      if (changed) this.persist()
    } catch {}
  }

  private persist() {
    try {
      const obj: Record<string, CacheEntry<TValue>> = {}
      this.memory.forEach((v, k) => (obj[k] = v))
      localStorage.setItem(this.key, JSON.stringify(obj))
    } catch {}
  }

  set(key: string, value: TValue, ttlMs?: number) {
    const ttl = ttlMs ?? this.ttl
    const entry: CacheEntry<TValue> = { value, expireAt: ttl ? Date.now() + ttl : undefined }
    this.memory.set(key, entry)
    this.persist()
  }

  get(key: string): TValue | undefined {
    const entry = this.memory.get(key)
    if (!entry) return undefined
    if (entry.expireAt && Date.now() > entry.expireAt) {
      this.memory.delete(key)
      this.persist()
      return undefined
    }
    return entry.value
  }

  clear() {
    this.memory.clear()
    this.persist()
  }
}



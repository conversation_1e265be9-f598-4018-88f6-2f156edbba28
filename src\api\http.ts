import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'
import { reportMetric, reportError } from '@/utils/errorMonitor'
import type { ApiResponse } from '@/types/api'
import { createAppError } from '@/types/api'
import { ApiCache } from '@/utils/apiCache'
import { getBusinessCodeMessage, getHttpStatusMessage } from '@/utils/httpError'
import { onRequestStart, onRequestEnd } from '@/utils/httpLoading'
import { buildCacheKey as buildGlobalKey, getFromCache, setToCache } from '@/utils/apiCacheStore'

export type { ApiResponse }

const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE || '/',
  timeout: 15000,
  withCredentials: false,
})

instance.interceptors.request.use((config: any) => {
  // 优先使用开发者token，其次使用通用token
  const developerToken = localStorage.getItem('developer_token')
  const adminToken = localStorage.getItem('admin_token')
  const token = developerToken || adminToken || localStorage.getItem('token')
  
  if (token) {
    config.headers = config.headers || {}
    config.headers['Authorization'] = `Bearer ${token}`
  }
  // 记录请求开始时间用于时延统计
  config._startAt = Date.now()
  // 顶部进度条联动（可通过 silent 跳过）
  if (!config.silent) {
    config._loadingKey = onRequestStart()
  }
  return config
})

instance.interceptors.response.use(
  (resp: any) => {
    const r = resp.data
    // 指标上报
    try {
      const start = resp?.config?._startAt || Date.now()
      const duration = Date.now() - start
      reportMetric({
        type: 'http',
        name: resp?.config?._metricName,
        url: resp?.config?.url,
        method: resp?.config?.method,
        status: resp?.status,
        success: true,
        durationMs: duration,
        time: Date.now(),
      })
    } catch {}

    if (typeof r === 'object' && r && 'code' in r) {
      if (r.code === 0) return r
      const msg = getBusinessCodeMessage(r.code, r.message)
      const err = createAppError({
        message: msg,
        code: r.code,
        kind: 'Business',
        url: resp?.config?.url,
        method: resp?.config?.method,
      })
      if (!resp?.config?.silent && resp?.config?._loadingKey) onRequestEnd()
      throw err
    }
    if (!resp?.config?.silent && resp?.config?._loadingKey) onRequestEnd()
    return r as any
  },
  async (error: any) => {
    const status = error?.response?.status
    const cfg = (error?.config || {}) as AxiosRequestConfig

    // 指标上报
    try {
      const start = cfg?._startAt || Date.now()
      const duration = Date.now() - start
      reportMetric({
        type: 'http',
        name: cfg?._metricName,
        url: cfg?.url,
        method: cfg?.method,
        status,
        success: false,
        durationMs: duration,
        time: Date.now(),
      })
    } catch {}

    // 简单重试：仅 GET、非401、网络错误或5xx或命中 retryOn
    try {
      const method = String(cfg?.method || 'GET').toUpperCase()
      const times = cfg?.retry?.times || 0
      const retryOn = cfg?.retry?.retryOn || []
      const delayMs = cfg?.retry?.delayMs ?? 200
      const count = cfg?._retryCount || 0
      const isNetworkError = !error?.response
      const isServerError = status >= 500 && status <= 599
      const isWhitelisted = retryOn.length > 0 ? retryOn.includes(status) : false
      const canRetry = method === 'GET' && status !== 401 && times > count && (isNetworkError || isServerError || isWhitelisted)

      if (canRetry) {
        cfg._retryCount = count + 1
        await new Promise((r) => setTimeout(r, delayMs))
        return instance.request(cfg)
      }
    } catch {}

    // 映射错误信息
    if (error?.isAxiosError) {
      const cfg = (error?.config || {}) as AxiosRequestConfig
      const status = error?.response?.status
      const msg = getHttpStatusMessage(status)
      const err = createAppError({
        message: msg,
        httpStatus: status,
        kind: status ? 'Http' : 'Network',
        url: cfg?.url,
        method: cfg?.method,
        cause: error,
      })
      // 上报错误（可静默）
      try {
        if (!cfg?.silent) {
          await reportError({
            type: 'http',
            message: msg,
            status,
            url: cfg?.url,
            method: cfg?.method,
            time: Date.now(),
          } as any)
        }
      } catch {}
      if (!cfg?.silent && cfg?._loadingKey) onRequestEnd()
      return Promise.reject(err)
    }
    const cfg2 = (error?.config || {}) as AxiosRequestConfig
    if (!cfg2?.silent && cfg2?._loadingKey) onRequestEnd()
    return Promise.reject(error)
  }
)

export async function request<T = unknown>(config: AxiosRequestConfig): Promise<T> {
  // GET 缓存读取
  const method = String(config.method || 'GET').toUpperCase()
  const cacheEnabled = !!config.cache?.enabled && method === 'GET'
  let cache: ApiCache<T> | undefined
  let cacheKey: string | undefined
  if (cacheEnabled) {
    cache = new ApiCache<T>({ ttlMs: config.cache?.ttlMs })
    cacheKey = config.cache?.key || buildGlobalKey(config.url, config.params)
    const cached = getFromCache<T>(cacheKey)
    if (cached !== undefined) return cached
  }

  // 通过响应拦截器，axios 返回的是 ApiResponse<T> 或直接对象
  const r = await instance.request<ApiResponse<T>>(config)
  const resp = r as unknown as ApiResponse<T> | T
  const data = (resp && typeof resp === 'object' && 'code' in (resp as any) ? (resp as ApiResponse<T>).data : (resp as T)) as T

  // GET 缓存写入
  if (cacheEnabled && cache && cacheKey) {
    setToCache(cacheKey, data, config.cache?.ttlMs)
  }
  return data
}

export default instance



import { defineStore } from 'pinia'

export type AppTheme = 'light' | 'dark' | 'system'
export type AppLocale = 'zh-CN' | 'en-US'

interface AppState {
  theme: AppTheme
  locale: AppLocale
  initialized: boolean
  loading: boolean
  error: string | null
}

const STORAGE_KEY = 'app_store_v1'

function loadPersistedState(): Partial<AppState> {
  try {
    const raw = localStorage.getItem(STORAGE_KEY)
    if (!raw) return {}
    return JSON.parse(raw) as Partial<AppState>
  } catch {
    return {}
  }
}

function persistState(state: Partial<AppState>) {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
  } catch {}
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    theme: 'light',
    locale: 'zh-CN',
    initialized: false,
    loading: false,
    error: null,
  }),
  getters: {
    isDark(state): boolean {
      if (state.theme === 'system') {
        return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      }
      return state.theme === 'dark'
    },
  },
  actions: {
    initialize(): void {
      const persisted = loadPersistedState()
      if (persisted.theme) this.theme = persisted.theme
      if (persisted.locale) this.locale = persisted.locale
      this.applyTheme(this.theme)
      this.applyLocale(this.locale)
      this.initialized = true
    },
    setTheme(theme: AppTheme): void {
      this.theme = theme
      this.applyTheme(theme)
      persistState({ theme: this.theme, locale: this.locale })
    },
    setLocale(locale: AppLocale): void {
      this.locale = locale
      this.applyLocale(locale)
      persistState({ theme: this.theme, locale: this.locale })
    },
    applyTheme(theme: AppTheme): void {
      const root = document.documentElement
      const isDark = theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)
      root.setAttribute('data-theme', isDark ? 'dark' : 'light')
    },
    applyLocale(_locale: AppLocale): void {
      // 预留：后续可集成 i18n
    },
    async withLoading<T>(fn: () => Promise<T>): Promise<T> {
      this.loading = true
      this.error = null
      try {
        const res = await fn()
        return res
      } catch (err) {
        const e = err as Error
        this.error = e.message
        throw err
      } finally {
        this.loading = false
      }
    },
  },
})



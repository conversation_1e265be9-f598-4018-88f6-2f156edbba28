<template>
  <span class="file-icon" :class="kind">{{ ext.toUpperCase() }}</span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props { name: string }
const props = defineProps<Props>()
const ext = computed(() => (props.name.split('.').pop() || '').slice(0, 4))
const kind = computed(() => {
  const e = (ext.value || '').toLowerCase()
  if (['png','jpg','jpeg','gif','webp','svg'].includes(e)) return 'image'
  if (['md','txt','log','json','yaml','yml'].includes(e)) return 'text'
  if (['zip','rar','7z','tar','gz'].includes(e)) return 'archive'
  if (['mp4','webm','mp3','wav'].includes(e)) return 'media'
  return 'binary'
})
</script>

<style scoped>
.file-icon { display: inline-flex; align-items: center; justify-content: center; width: 34px; height: 24px; border-radius: var(--radius-sm); font-size: 10px; font-weight: var(--font-weight-bold); }
.image { background: #e0f2fe; color: #075985; }
.text { background: #e2e8f0; color: #334155; }
.archive { background: #fef3c7; color: #92400e; }
.media { background: #ede9fe; color: #5b21b6; }
.binary { background: #fee2e2; color: #991b1b; }
</style>



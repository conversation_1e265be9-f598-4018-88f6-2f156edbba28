<template>
  <div class="public-layout">
    <!-- 页面头部 -->
    <PublicHeader />
    
    <!-- 主要内容区域 -->
    <main class="public-main">
      <div class="public-container container-xl">
        <!-- 面包屑导航 -->
        <PublicBreadcrumb v-if="showBreadcrumb" />
        
        <!-- 页面内容插槽 -->
        <div class="public-content">
          <slot />
        </div>
      </div>
    </main>
    
    <!-- 页面底部 -->
    <PublicFooter />

    
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import PublicHeader from '@/components/layout/PublicHeader.vue'
import PublicFooter from '@/components/layout/PublicFooter.vue'
import PublicBreadcrumb from '@/components/layout/PublicBreadcrumb.vue'
 

const route = useRoute()

// 是否显示面包屑导航
const showBreadcrumb = computed(() => {
  const breadcrumb = route.meta.breadcrumb
  return breadcrumb && Array.isArray(breadcrumb) && breadcrumb.length > 1
})

 
</script>

<style scoped>
.public-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.public-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.public-container {
  width: 100%;
  flex: 1;
}

.public-content {
  padding: var(--spacing-8) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .public-content { padding: var(--spacing-6) 0; }
}

@media (max-width: 480px) {
  .public-content { padding: var(--spacing-4) 0; }
}
</style>

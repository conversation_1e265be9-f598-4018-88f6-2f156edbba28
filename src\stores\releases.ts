import { defineStore } from 'pinia'
import { getDataService } from '@/data'
import type { ReleaseItem, PageResult, ReleaseDetail, QueryParams } from '@/data'
import { eventBus } from '@/utils/eventBus'
// 上传功能保持独立，暂不迁移到统一数据服务
// import { uploadFile } from '@/api/upload'
import { SimpleCache } from '@/utils/cache'

const dataService = getDataService()

interface QueryState {
  page: number
  pageSize: number
  projectId?: number
  status?: string
  artifact?: string
  sortBy?: 'createdAt' | 'version'
  order?: 'asc' | 'desc'
}

interface UploadState {
  uploading: boolean
  progress: number
}

interface ReleasesState {
  list: ReleaseItem[]
  total: number
  query: QueryState
  loading: boolean
  error: string | null
  upload: UploadState
  listCache: SimpleCache<PageResult<ReleaseItem>>
  detailCache: SimpleCache<ReleaseDetail>
}

export const useReleasesStore = defineStore('releases', {
  state: (): ReleasesState => ({
    list: [],
    total: 0,
    query: { page: 1, pageSize: 10 },
    loading: false,
    error: null,
    upload: { uploading: false, progress: 0 },
    listCache: new SimpleCache<PageResult<ReleaseItem>>({ ttlMs: 60 * 1000 }),
    detailCache: new SimpleCache<ReleaseDetail>({ ttlMs: 60 * 1000 }),
  }),
  actions: {
    async loadList(params?: Partial<QueryState>) {
      if (params) this.query = { ...this.query, ...params }
      const { withLoading } = await import('@/utils/async')
      await withLoading(this, async () => {
        const key = JSON.stringify({ ...this.query, ...(params || {}) })
        const cached = this.listCache.get(key)
        if (cached) {
          this.list = cached.list
          this.total = cached.total
          return
        }
        
        const queryParams: QueryParams = {
          page: this.query.page,
          pageSize: this.query.pageSize,
          status: this.query.status || undefined,
          sortBy: this.query.sortBy || undefined,
          order: this.query.order || undefined
        }
        
        const res = await dataService.getReleases(queryParams)
        this.list = res.list
        this.total = res.total
        this.listCache.set(key, res)
      })
    },
    async getDetail(id: number) {
      const { withLoading } = await import('@/utils/async')
      return withLoading(this, async () => {
        const key = String(id)
        const cached = this.detailCache.get(key)
        if (cached) return cached
        const res = await dataService.getReleaseDetail(id)
        this.detailCache.set(key, res)
        return res
      })
    },
    async loadByProject(projectId: number, params?: Partial<QueryState>) {
      const { withLoading } = await import('@/utils/async')
      const merged: QueryState = { ...this.query, page: 1, projectId, ...(params || {}) }
      this.query = merged
      await withLoading(this, async () => {
        const key = JSON.stringify({ projectId, ...merged, byProject: true })
        const cached = this.listCache.get(key)
        if (cached) {
          this.list = cached.list
          this.total = cached.total
          return
        }
        
        // 对于按项目查询，我们需要在通用查询中添加项目ID过滤
        // 注意：这需要后端API支持按项目ID过滤
        const queryParams: QueryParams = {
          page: merged.page,
          pageSize: merged.pageSize,
          status: merged.status || undefined,
          sortBy: merged.sortBy || undefined,
          order: merged.order || undefined
        }
        
        const res = await dataService.getReleases(queryParams)
        // 临时客户端过滤，实际应该在服务端处理
        const filteredList = res.list.filter(r => r.projectId === projectId)
        this.list = filteredList
        this.total = filteredList.length
        this.listCache.set(key, { list: filteredList, total: filteredList.length, page: res.page, pageSize: res.pageSize })
      })
    },
    async publish(data: { projectId: number; version: string; changelog?: string }) {
      const res = await dataService.createRelease(data)
      // 重新加载列表置顶显示
      await this.loadList({ page: 1 })
      eventBus.emit('release:created', {
        id: res.id,
        projectId: res.projectId,
        version: res.version,
      })
      return res
    },
    setUploadProgress(p: number) {
      this.upload.progress = Math.max(0, Math.min(100, Math.floor(p)))
    },
    setUploading(flag: boolean) {
      this.upload.uploading = flag
      if (!flag) this.upload.progress = 0
    },
    async uploadPackage(name: string, size?: number) {
      this.setUploading(true)
      try {
        // 模拟上传过程
        return new Promise((resolve) => {
          let progress = 0
          const interval = setInterval(() => {
            progress += Math.random() * 20
            this.setUploadProgress(progress)
            if (progress >= 100) {
              clearInterval(interval)
              this.setUploadProgress(100)
              resolve({
                id: Date.now(),
                url: `/uploads/${name}`,
                name,
                size: size || Math.floor(Math.random() * 10000000)
              })
            }
          }, 200)
        })
      } finally {
        this.setUploading(false)
      }
    },
  },
})



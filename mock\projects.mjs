// 简化的项目数据结构 - 只包含基本信息
const projects = Array.from({ length: 30 }).map((_, i) => {
  return {
    id: i + 1,
    name: `项目-${i + 1}`,
    description: `项目 ${i + 1} 的描述信息，这是一个示例项目，展示了基本的项目管理功能。`,
    status: i % 4 === 0 ? 'inactive' : 'active',
    createdAt: Date.now() - i * 86400000,
    updatedAt: Date.now() - Math.floor(i / 2) * 3600000,
  }
})

// 软件类型数据
const softwareTypes = []
const versions = []

// 为前5个项目生成软件类型和版本数据
for (let projectId = 1; projectId <= 5; projectId++) {
  const typesForProject = [
    { name: 'ESP32固件', hasVersions: true },
    { name: 'Android APP', hasVersions: true },
    { name: 'iOS APP', hasVersions: false },
  ]
  
  typesForProject.forEach((typeInfo, typeIndex) => {
    const softwareTypeId = projectId * 10 + typeIndex + 1
    
    const softwareType = {
      id: softwareTypeId,
      projectId: projectId,
      name: typeInfo.name,
      updatedAt: Date.now() - (projectId * typeIndex + 1) * 3600000,
    }
    
    if (typeInfo.hasVersions) {
      // 为这个软件类型生成版本数据
      const versionCount = Math.floor(Math.random() * 3) + 1 // 1-3个版本
      for (let v = 0; v < versionCount; v++) {
        const version = {
          id: softwareTypeId * 100 + v + 1,
          softwareTypeId: softwareTypeId,
          versionName: `v${projectId}.${typeIndex}.${v}`,
          fileUrl: `/downloads/${typeInfo.name.toLowerCase().replace(' ', '-')}-v${projectId}.${typeIndex}.${v}.zip`,
          fileSize: Math.floor(Math.random() * 50000000) + 1000000, // 1MB - 50MB
          updatedAt: Date.now() - (v + 1) * 86400000,
        }
        versions.push(version)
      }
      
      // 找到最新版本
      const typeVersions = versions.filter(v => v.softwareTypeId === softwareTypeId)
      if (typeVersions.length > 0) {
        const latestVersion = typeVersions.sort((a, b) => b.updatedAt - a.updatedAt)[0]
        softwareType.latestVersion = latestVersion
      }
    }
    
    softwareTypes.push(softwareType)
  })
}

let nextSoftwareTypeId = Math.max(...softwareTypes.map(s => s.id), 0) + 1
let nextVersionId = Math.max(...versions.map(v => v.id), 0) + 1

export default [
  // 开发者管理的项目列表
  {
    method: 'GET',
    path: '/developer/projects',
    handler: async ({ req, query }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 10)
      
      // 模拟开发者只能管理前10个项目
      const developerProjects = projects.slice(0, 10)
      
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = developerProjects.slice(start, end)
      
      return {
        code: 0,
        message: 'OK',
        data: {
          list,
          page,
          pageSize,
          total: developerProjects.length,
        },
      }
    },
  },
  
  {
    method: 'GET',
    path: '/projects',
    handler: async ({ query }) => {
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 10)
      const search = (query.search || '').toLowerCase()
      const type = query.type || ''
      const device = query.device || ''
      const tags = (query.tags ? String(query.tags).split(',').filter(Boolean) : [])
      const sortBy = query.sortBy || 'updatedAt'
      const order = (query.order || 'desc').toLowerCase() === 'asc' ? 'asc' : 'desc'

      let data = projects
      if (search) {
        data = data.filter((p) =>
          p.name.toLowerCase().includes(search) ||
          p.key.toLowerCase().includes(search) ||
          (p.description || '').toLowerCase().includes(search)
        )
      }
      if (type) {
        data = data.filter((p) => p.type === type)
      }
      if (device) {
        data = data.filter((p) => Array.isArray(p.devices) && p.devices.includes(device))
      }
      if (tags.length > 0) {
        data = data.filter((p) => p.tags && p.tags.some((t) => tags.includes(String(t))))
      }

      if (sortBy) {
        data = data.slice().sort((a, b) => {
          const getValue = (x) => {
            if (sortBy === 'name') return x.name
            return x.updatedAt
          }
          const va = getValue(a)
          const vb = getValue(b)
          if (va === vb) return 0
          if (order === 'asc') return va > vb ? 1 : -1
          return va < vb ? 1 : -1
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = data.slice(start, end)

      return {
        code: 0,
        message: 'OK',
        data: {
          list,
          page,
          pageSize,
          total: data.length,
        },
      }
    },
  },
  {
    method: 'GET',
    path: '/projects/:id',
    handler: async ({ params }) => {
      const id = Number(params.id)
      const item = projects.find((p) => p.id === id)
      if (!item) return { code: 404, message: '项目不存在', data: null }
      return { code: 0, message: 'OK', data: item }
    },
  },

  // ========== 软件类型管理接口 ==========
  
  // 获取项目的软件类型列表
  {
    method: 'GET',
    path: '/developer/projects/:projectId/software-types',
    handler: async ({ req, params }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const projectId = Number(params.projectId)
      const projectSoftwareTypes = softwareTypes.filter(s => s.projectId === projectId)
      
      return {
        code: 0,
        message: 'OK',
        data: projectSoftwareTypes,
      }
    },
  },

  // 创建软件类型
  {
    method: 'POST',
    path: '/developer/projects/:projectId/software-types',
    handler: async ({ req, params, body }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const projectId = Number(params.projectId)
      const { name } = body
      
      if (!name || !name.trim()) {
        return { code: 400, message: '软件类型名称不能为空', data: null }
      }
      
      const newSoftwareType = {
        id: nextSoftwareTypeId++,
        projectId: projectId,
        name: name.trim(),
        updatedAt: Date.now(),
      }
      
      softwareTypes.push(newSoftwareType)
      
      return {
        code: 0,
        message: '创建成功',
        data: newSoftwareType,
      }
    },
  },

  // 更新软件类型
  {
    method: 'PUT',
    path: '/developer/software-types/:id',
    handler: async ({ req, params, body }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const id = Number(params.id)
      const { name } = body
      
      const softwareType = softwareTypes.find(s => s.id === id)
      if (!softwareType) {
        return { code: 404, message: '软件类型不存在', data: null }
      }
      
      if (name && name.trim()) {
        softwareType.name = name.trim()
        softwareType.updatedAt = Date.now()
      }
      
      return {
        code: 0,
        message: '更新成功',
        data: softwareType,
      }
    },
  },

  // 删除软件类型
  {
    method: 'DELETE',
    path: '/developer/software-types/:id',
    handler: async ({ req, params }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const id = Number(params.id)
      const index = softwareTypes.findIndex(s => s.id === id)
      
      if (index === -1) {
        return { code: 404, message: '软件类型不存在', data: null }
      }
      
      const removed = softwareTypes.splice(index, 1)[0]
      
      // 同时删除该软件类型下的所有版本
      const removedVersions = versions.filter(v => v.softwareTypeId === id)
      versions.splice(0, versions.length, ...versions.filter(v => v.softwareTypeId !== id))
      
      return {
        code: 0,
        message: '删除成功',
        data: {
          softwareType: removed,
          removedVersions: removedVersions.length,
        },
      }
    },
  },

  // ========== 版本管理接口 ==========
  
  // 获取软件类型的版本列表
  {
    method: 'GET',
    path: '/developer/software-types/:softwareTypeId/versions',
    handler: async ({ req, params, query }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const softwareTypeId = Number(params.softwareTypeId)
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 20)
      
      let typeVersions = versions.filter(v => v.softwareTypeId === softwareTypeId)
      
      // 按更新时间倒序排列
      typeVersions.sort((a, b) => b.updatedAt - a.updatedAt)
      
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = typeVersions.slice(start, end)
      
      return {
        code: 0,
        message: 'OK',
        data: {
          list,
          page,
          pageSize,
          total: typeVersions.length,
        },
      }
    },
  },

  // 创建版本
  {
    method: 'POST',
    path: '/developer/software-types/:softwareTypeId/versions',
    handler: async ({ req, params, body }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const softwareTypeId = Number(params.softwareTypeId)
      const { versionName, fileUrl, fileSize } = body
      
      if (!versionName || !versionName.trim()) {
        return { code: 400, message: '版本号不能为空', data: null }
      }
      
      // 检查同一软件类型内版本号是否重复
      const existingVersion = versions.find(v => 
        v.softwareTypeId === softwareTypeId && 
        v.versionName === versionName.trim()
      )
      
      if (existingVersion) {
        return { code: 400, message: '版本号已存在', data: null }
      }
      
      const newVersion = {
        id: nextVersionId++,
        softwareTypeId: softwareTypeId,
        versionName: versionName.trim(),
        fileUrl: fileUrl || `/downloads/version-${nextVersionId}.zip`,
        fileSize: fileSize || Math.floor(Math.random() * 50000000) + 1000000,
        updatedAt: Date.now(),
      }
      
      versions.push(newVersion)
      
      // 更新软件类型的最新版本
      const softwareType = softwareTypes.find(s => s.id === softwareTypeId)
      if (softwareType) {
        softwareType.latestVersion = newVersion
        softwareType.updatedAt = Date.now()
      }
      
      return {
        code: 0,
        message: '创建成功',
        data: newVersion,
      }
    },
  },

  // 更新版本
  {
    method: 'PUT',
    path: '/developer/versions/:id',
    handler: async ({ req, params, body }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const id = Number(params.id)
      const { versionName, fileUrl, fileSize } = body
      
      const version = versions.find(v => v.id === id)
      if (!version) {
        return { code: 404, message: '版本不存在', data: null }
      }
      
      // 检查版本号唯一性
      if (versionName && versionName.trim() && versionName.trim() !== version.versionName) {
        const existingVersion = versions.find(v => 
          v.softwareTypeId === version.softwareTypeId && 
          v.versionName === versionName.trim() &&
          v.id !== id
        )
        
        if (existingVersion) {
          return { code: 400, message: '版本号已存在', data: null }
        }
        
        version.versionName = versionName.trim()
      }
      
      if (fileUrl) version.fileUrl = fileUrl
      if (fileSize) version.fileSize = fileSize
      version.updatedAt = Date.now()
      
      // 如果这是最新版本，更新软件类型的最新版本信息
      const softwareType = softwareTypes.find(s => s.id === version.softwareTypeId)
      if (softwareType && softwareType.latestVersion && softwareType.latestVersion.id === id) {
        softwareType.latestVersion = { ...version }
        softwareType.updatedAt = Date.now()
      }
      
      return {
        code: 0,
        message: '更新成功',
        data: version,
      }
    },
  },

  // 删除版本
  {
    method: 'DELETE',
    path: '/developer/versions/:id',
    handler: async ({ req, params }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const id = Number(params.id)
      const index = versions.findIndex(v => v.id === id)
      
      if (index === -1) {
        return { code: 404, message: '版本不存在', data: null }
      }
      
      const removed = versions.splice(index, 1)[0]
      
      // 如果删除的是最新版本，需要更新软件类型的最新版本
      const softwareType = softwareTypes.find(s => s.id === removed.softwareTypeId)
      if (softwareType && softwareType.latestVersion && softwareType.latestVersion.id === id) {
        // 找到该软件类型的其他版本中最新的一个
        const remainingVersions = versions
          .filter(v => v.softwareTypeId === removed.softwareTypeId)
          .sort((a, b) => b.updatedAt - a.updatedAt)
        
        if (remainingVersions.length > 0) {
          softwareType.latestVersion = remainingVersions[0]
        } else {
          delete softwareType.latestVersion
        }
        softwareType.updatedAt = Date.now()
      }
      
      return {
        code: 0,
        message: '删除成功',
        data: {
          id: removed.id,
          versionName: removed.versionName,
        },
      }
    },
  },
]



<template>
  <header class="public-header glass-strong accent-top">
    <div class="header-inner container-xl">
      <!-- Logo区域 -->
      <div class="header-brand">
        <RouterLink to="/" class="brand-link">
          <div class="brand-logo">
            <BaseIcon name="app-logo" size="28" />
          </div>
          <div class="brand-text">
            <h1 class="brand-title">软件发布系统</h1>
            <span class="brand-subtitle">Software Release Platform</span>
          </div>
        </RouterLink>
      </div>

      <!-- 主导航菜单 -->
      <nav class="header-nav" :class="{ 'nav-open': mobileNavOpen }">
        <ul class="nav-list">
          <li class="nav-item">
            <RouterLink to="/" class="nav-link" @click="closeMobileNav">
              <BaseIcon name="home" size="18" />
              <span>首页</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/projects" class="nav-link" @click="closeMobileNav">
              <BaseIcon name="folder" size="18" />
              <span>项目</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/search" class="nav-link" @click="closeMobileNav">
              <BaseIcon name="search" size="18" />
              <span>搜索</span>
            </RouterLink>
          </li>
        </ul>
      </nav>

      <!-- 用户区域 -->
      <div class="header-user">
        <!-- 主题切换 -->
        <div class="theme-switcher">
          <select 
            :value="app.theme" 
            @change="handleThemeChange"
            class="theme-select"
            aria-label="切换主题"
          >
            <option value="light">浅色</option>
            <option value="dark">深色</option>
            <option value="system">跟随系统</option>
          </select>
        </div>

        <!-- 用户状态 -->
        <div class="user-actions">
          <template v-if="currentAuth">
            <!-- 已登录用户 -->
            <div class="user-info">
              <Avatar :user="currentUser" size="small" />
              <span class="user-name">{{ currentUser?.name || currentUser?.username }}</span>
              <span class="user-type-badge">{{ currentUser?.userType === 'admin' ? '管理员' : '开发者' }}</span>
            </div>
            <div class="user-menu">
              <RouterLink 
                v-if="adminAuth.isAuthenticated" 
                to="/admin" 
                class="user-link"
                @click="closeMobileNav"
              >
                管理后台
              </RouterLink>
              <RouterLink 
                v-if="developerAuth.isAuthenticated" 
                to="/dev" 
                class="user-link"
                @click="closeMobileNav"
              >
                开发者中心
              </RouterLink>
              <button @click="handleLogout" class="logout-btn">
                退出登录
              </button>
            </div>
          </template>
          <template v-else>
            <!-- 未登录用户 -->
            <div class="login-buttons">
              <RouterLink to="/dev/login" class="login-btn developer" @click="closeMobileNav">
                <BaseIcon name="user" size="16" />
                <span>开发者</span>
              </RouterLink>
              <RouterLink to="/admin/login" class="login-btn admin" @click="closeMobileNav">
                <BaseIcon name="shield" size="16" />
                <span>管理员</span>
              </RouterLink>
            </div>
          </template>
        </div>

        <!-- 移动端菜单按钮 -->
        <button 
          class="mobile-nav-toggle"
          @click="toggleMobileNav"
          :aria-expanded="mobileNavOpen"
          aria-label="切换导航菜单"
        >
          <span class="hamburger"></span>
        </button>
      </div>
    </div>

    <!-- 移动端导航遮罩 -->
    <div 
      v-if="mobileNavOpen" 
      class="mobile-nav-overlay"
      @click="closeMobileNav"
    ></div>
  </header>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { useAppStore } from '@/stores/app'
import BaseIcon from '@base/atoms/BaseIcon.vue'
import Avatar from '@base/atoms/Avatar.vue'
 

const router = useRouter()
const developerAuth = useDeveloperAuthStore()
const adminAuth = useAdminAuthStore()
const app = useAppStore()

// 计算当前认证状态
const currentAuth = computed(() => {
  if (developerAuth.isAuthenticated) return developerAuth
  if (adminAuth.isAuthenticated) return adminAuth
  return null
})

const currentUser = computed(() => currentAuth.value?.user || null)

// 移动端导航状态
const mobileNavOpen = ref(false)

// 切换移动端导航
const toggleMobileNav = () => {
  mobileNavOpen.value = !mobileNavOpen.value
}

// 关闭移动端导航
const closeMobileNav = () => {
  mobileNavOpen.value = false
}

// 处理主题切换
const handleThemeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  app.setTheme(target.value as any)
}

// 处理退出登录
const handleLogout = () => {
  if (developerAuth.isAuthenticated) {
    developerAuth.logout()
  }
  if (adminAuth.isAuthenticated) {
    adminAuth.logout()
  }
  closeMobileNav()
  router.push('/')
}

 

// 监听路由变化，自动关闭移动端导航
router.beforeEach(() => {
  closeMobileNav()
})
</script>

<style scoped>
.public-header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  border-bottom: 1px solid var(--glass-border);
  transition: var(--transition-all);
}

.header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 72px;
  gap: var(--spacing-6);
}

/* Logo品牌区域 */
.header-brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  text-decoration: none;
  color: inherit;
  transition: var(--transition-transform);
}

.brand-link:hover {
  transform: scale(1.02);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  color: white;
  box-shadow: var(--shadow-sm);
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-0_5);
}

.brand-title {
  font-family: var(--font-family-display);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: var(--font-size-2xs);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 主导航 */
.header-nav {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  list-style: none;
  margin: 0;
  padding: var(--spacing-1);
  background: var(--glass-bg);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.nav-item {
  display: flex;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: var(--transition-all);
  position: relative;
  white-space: nowrap;
}

.nav-link:hover {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.nav-link.router-link-active {
  background: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.nav-link.router-link-active:hover {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 用户区域 */
.header-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-shrink: 0;
}

.theme-switcher {
  display: flex;
  align-items: center;
}

.theme-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1.5px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-colors);
}

.theme-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.theme-select:hover {
  border-color: var(--border-secondary);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-xl);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: 1;
}

.user-type-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: var(--radius-full);
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.login-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.user-link {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-all);
  border: 1px solid var(--border-primary);
}

.user-link:hover {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.login-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  border-radius: var(--radius-xl);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  transition: var(--transition-all);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.login-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.login-btn.developer {
  background: var(--gradient-primary);
  color: white;
}

.login-btn.developer:hover {
  background: var(--color-primary-600);
}

.login-btn.admin {
  background: var(--color-warning);
  color: white;
}

.login-btn.admin:hover {
  background: var(--color-warning-600);
}

.logout-btn {
  padding: var(--spacing-2) var(--spacing-3);
  background: transparent;
  border: 1.5px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
}

.logout-btn:hover {
  background: var(--bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.logout-btn:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* 移动端导航按钮 */
.mobile-nav-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  cursor: pointer;
  padding: 0;
  transition: var(--transition-all);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.mobile-nav-toggle:hover {
  background: var(--glass-bg-strong);
  transform: scale(1.05);
}

.mobile-nav-toggle:active {
  transform: scale(0.95);
}

.hamburger {
  position: relative;
  width: 20px;
  height: 2px;
  background: var(--color-text-1);
  transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  left: 0;
  width: 20px;
  height: 2px;
  background: var(--color-text-1);
  transition: all 0.3s ease;
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  bottom: -6px;
}

/* 移动端导航遮罩 */
.mobile-nav-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-inner {
    min-height: 64px;
    gap: var(--spacing-4);
  }
  
  .brand-text { display: none; }
  .brand-logo { width: 40px; height: 40px; }

  .header-nav {
    position: fixed;
    top: 72px;
    left: 0;
    right: 0;
    background: var(--glass-bg-strong);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    flex-direction: column;
    justify-content: flex-start;
    padding: var(--spacing-6);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-slow);
    z-index: var(--z-dropdown);
    box-shadow: var(--shadow-lg);
  }

  .header-nav.nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-list {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
    width: 100%;
    background: transparent;
    border: none;
    padding: 0;
  }

  .nav-link {
    justify-content: flex-start;
    padding: var(--spacing-4) var(--spacing-5);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
  }

  .mobile-nav-toggle { display: flex; }
  .mobile-nav-overlay { display: block; }
  .theme-switcher { display: none; }
  .user-info .user-name { display: none; }
  .user-menu { display: none; }
  .user-type-badge { display: none; }
  
  .user-actions { gap: var(--spacing-2); }
  .login-buttons { gap: var(--spacing-1_5); }
  .login-btn { padding: var(--spacing-2) var(--spacing-3); font-size: var(--font-size-xs); }
}

@media (max-width: 480px) {
  .header-inner { 
    min-height: 60px; 
    gap: var(--spacing-3);
  }
  .brand-logo { width: 36px; height: 36px; }
  .mobile-nav-overlay { top: 60px; }
  .header-nav { 
    top: 60px; 
    padding: var(--spacing-5);
  }
  .mobile-nav-toggle { width: 40px; height: 40px; }
  .login-btn { padding: var(--spacing-1_5) var(--spacing-2_5); }
}

/* 动画效果 */
.mobile-nav-toggle[aria-expanded="true"] .hamburger {
  background: transparent;
}

.mobile-nav-toggle[aria-expanded="true"] .hamburger::before {
  top: 0;
  transform: rotate(45deg);
}

.mobile-nav-toggle[aria-expanded="true"] .hamburger::after {
  bottom: 0;
  transform: rotate(-45deg);
}
</style>

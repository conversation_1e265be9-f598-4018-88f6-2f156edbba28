import { SimpleCache } from '@/utils/cache'

type AnyValue = unknown

const globalCache = new SimpleCache<AnyValue>({ ttlMs: 30_000 })
const tagToKeys = new Map<string, Set<string>>()

export function buildCacheKey(url?: string, params?: unknown): string {
  return JSON.stringify({ url, params })
}

export function getFromCache<T = AnyValue>(key: string): T | undefined {
  return globalCache.get(key) as T | undefined
}

export function setToCache<T = AnyValue>(key: string, value: T, ttlMs?: number): void {
  globalCache.set(key, value as AnyValue, ttlMs)
}

export function registerCacheTags(key: string, tags?: string[]): void {
  if (!tags || tags.length === 0) return
  tags.forEach((tag) => {
    if (!tag) return
    const set = tagToKeys.get(tag) || new Set<string>()
    set.add(key)
    tagToKeys.set(tag, set)
  })
}

export function invalidateCacheTags(tags?: string[]): void {
  if (!tags || tags.length === 0) return
  tags.forEach((tag) => {
    const set = tagToKeys.get(tag)
    if (!set) return
    set.forEach((key) => {
      // SimpleCache 没有 delete 暴露接口，这里采用 clear 的折中策略：
      // 为避免清空所有缓存，采用过期策略：设置一个空值的零TTL覆盖，触发失效。
      globalCache.set(key, undefined as unknown as AnyValue, 1)
    })
    tagToKeys.delete(tag)
  })
}

export function clearAllApiCache(): void {
  tagToKeys.clear()
  globalCache.clear()
}



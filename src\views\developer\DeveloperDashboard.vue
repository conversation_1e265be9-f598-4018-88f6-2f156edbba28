<template>
  <div class="developer-dashboard">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="dashboard-title">开发者中心</h1>
        <p class="dashboard-subtitle">管理您的所有软件项目</p>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary" @click="logout">退出登录</button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="dashboard-content">
      <!-- 项目内容管理 -->
      <ProjectContentManager ref="projectsRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'
import ProjectContentManager from '@/components/developer/ProjectContentManager.vue'

const router = useRouter()
const developerAuth = useDeveloperAuthStore()

// 组件引用
const projectsRef = ref()

// 退出登录
function logout() {
  developerAuth.logout()
  router.replace('/')
}
</script>

<style scoped>
.developer-dashboard {
  padding: var(--spacing-6);
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.header-content h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-1) 0;
}

.dashboard-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-3);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}


/* 内容区域 */
.dashboard-content {
  min-height: 600px;
}

@media (max-width: 768px) {
  .developer-dashboard {
    padding: var(--spacing-4);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>



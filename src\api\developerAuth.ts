import { request } from '@/api/http'
import type { DeveloperUser, DeveloperLoginResponse } from '@/types/domain'

/**
 * 开发者登录
 */
export function developerLogin(username: string, password: string) {
  return request<DeveloperLoginResponse>({ 
    url: '/auth/developer/login', 
    method: 'POST', 
    data: { username, password } 
  })
}

/**
 * 刷新开发者令牌
 */
export function developerRefresh(refreshToken: string) {
  return request<DeveloperLoginResponse>({ 
    url: '/auth/developer/refresh', 
    method: 'POST', 
    data: { refreshToken } 
  })
}

/**
 * 获取开发者个人信息
 */
export function developerProfile() {
  return request<DeveloperUser>({ 
    url: '/auth/developer/profile', 
    method: 'GET' 
  })
}

/**
 * 修改开发者密码
 */
export function updateDeveloperPassword(oldPassword: string, newPassword: string) {
  return request<{ success: boolean }>({ 
    url: '/auth/developer/password', 
    method: 'PUT', 
    data: { oldPassword, newPassword } 
  })
}

/**
 * 开发者登出
 */
export function developerLogout() {
  return request<{ success: boolean }>({ 
    url: '/auth/developer/logout', 
    method: 'POST' 
  })
}

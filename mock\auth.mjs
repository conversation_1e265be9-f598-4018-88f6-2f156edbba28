export default [
  // 开发者登录接口
  {
    method: 'POST',
    path: '/auth/developer/login',
    handler: async ({ body }) => {
      const username = body && body.username
      const password = body && body.password
      
      // 验证开发者账号：admin/123456
      if (username !== 'admin' || password !== '123456') {
        return { code: 400, message: '开发者账号或密码错误', data: null }
      }
      
      return {
        code: 0,
        message: 'OK',
        data: {
          token: `dev-token-${Date.now()}`,
          refreshToken: `dev-refresh-${Date.now()}`,
          expiresIn: 7200, // 2小时
          user: {
            id: 1,
            username: 'admin',
            name: '开发者账号',
            email: '<EMAIL>',
            userType: 'developer',
            permissions: ['project:read', 'project:write', 'release:create'],
            projects: ['*'] // 可访问所有项目
          },
        },
      }
    },
  },
  
  // 管理员登录接口
  {
    method: 'POST',
    path: '/auth/admin/login',
    handler: async ({ body }) => {
      const username = body && body.username
      const password = body && body.password
      
      // 验证管理员账号：admin/123456
      if (username !== 'admin' || password !== '123456') {
        return { code: 400, message: '管理员账号或密码错误', data: null }
      }
      
      return {
        code: 0,
        message: 'OK',
        data: {
          token: `admin-token-${Date.now()}`,
          refreshToken: `admin-refresh-${Date.now()}`,
          expiresIn: 7200, // 2小时
          user: {
            id: 2,
            username: 'admin',
            name: '系统管理员',
            email: '<EMAIL>',
            userType: 'admin',
            level: 'super',
            permissions: ['*'] // 所有权限
          },
        },
      }
    },
  },

  // 开发者token刷新接口
  {
    method: 'POST',
    path: '/auth/developer/refresh',
    handler: async ({ body }) => {
      const refreshToken = body && body.refreshToken
      if (!refreshToken || !refreshToken.startsWith('dev-refresh-')) {
        return { code: 401, message: '刷新token无效', data: null }
      }
      
      return {
        code: 0,
        message: 'OK',
        data: {
          token: `dev-token-${Date.now()}`,
          refreshToken: `dev-refresh-${Date.now()}`,
          expiresIn: 7200,
          user: {
            id: 1,
            username: 'admin',
            name: '开发者账号',
            email: '<EMAIL>',
            userType: 'developer',
            permissions: ['project:read', 'project:write', 'release:create'],
            projects: ['*']
          },
        },
      }
    },
  },

  // 管理员token刷新接口
  {
    method: 'POST',
    path: '/auth/admin/refresh',
    handler: async ({ body }) => {
      const refreshToken = body && body.refreshToken
      if (!refreshToken || !refreshToken.startsWith('admin-refresh-')) {
        return { code: 401, message: '刷新token无效', data: null }
      }
      
      return {
        code: 0,
        message: 'OK',
        data: {
          token: `admin-token-${Date.now()}`,
          refreshToken: `admin-refresh-${Date.now()}`,
          expiresIn: 7200,
          user: {
            id: 2,
            username: 'admin',
            name: '系统管理员',
            email: '<EMAIL>',
            userType: 'admin',
            level: 'super',
            permissions: ['*']
          },
        },
      }
    },
  },

  // 开发者个人信息接口
  {
    method: 'GET',
    path: '/auth/developer/profile',
    handler: async ({ req }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      return {
        code: 0,
        message: 'OK',
        data: {
          id: 1,
          username: 'admin',
          name: '开发者账号',
          email: '<EMAIL>',
          userType: 'developer',
          permissions: ['project:read', 'project:write', 'release:create'],
          projects: ['*']
        },
      }
    },
  },

  // 管理员个人信息接口
  {
    method: 'GET',
    path: '/auth/admin/profile',
    handler: async ({ req }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('admin-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      return {
        code: 0,
        message: 'OK',
        data: {
          id: 2,
          username: 'admin',
          name: '系统管理员',
          email: '<EMAIL>',
          userType: 'admin',
          level: 'super',
          permissions: ['*']
        },
      }
    },
  },

  // 开发者密码修改接口
  {
    method: 'PUT',
    path: '/auth/developer/password',
    handler: async ({ body, req }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('dev-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const { oldPassword, newPassword } = body || {}
      if (oldPassword !== '123456') {
        return { code: 400, message: '当前密码错误', data: null }
      }
      
      if (!newPassword || newPassword.length < 6) {
        return { code: 400, message: '新密码长度不能少于6位', data: null }
      }
      
      return { code: 0, message: '密码修改成功', data: { success: true } }
    },
  },

  // 管理员密码修改接口
  {
    method: 'PUT',
    path: '/auth/admin/password',
    handler: async ({ body, req }) => {
      const auth = req.headers.authorization
      if (!auth || !auth.includes('admin-token-')) {
        return { code: 401, message: '未授权', data: null }
      }
      
      const { oldPassword, newPassword } = body || {}
      if (oldPassword !== '123456') {
        return { code: 400, message: '当前密码错误', data: null }
      }
      
      if (!newPassword || newPassword.length < 6) {
        return { code: 400, message: '新密码长度不能少于6位', data: null }
      }
      
      return { code: 0, message: '密码修改成功', data: { success: true } }
    },
  },

  // 开发者登出接口
  {
    method: 'POST',
    path: '/auth/developer/logout',
    handler: async () => ({ code: 0, message: '登出成功', data: { success: true } }),
  },

  // 管理员登出接口
  {
    method: 'POST',
    path: '/auth/admin/logout',
    handler: async () => ({ code: 0, message: '登出成功', data: { success: true } }),
  },

  // 向后兼容的通用登录接口（重定向提示）
  {
    method: 'POST',
    path: '/auth/login',
    handler: async () => {
      return { 
        code: 400, 
        message: '请使用专用登录接口：/auth/developer/login 或 /auth/admin/login', 
        data: null 
      }
    },
  },

  // 向后兼容的接口
  {
    method: 'POST',
    path: '/auth/logout',
    handler: async () => ({ code: 0, message: 'OK', data: true }),
  },
  {
    method: 'GET',
    path: '/auth/profile',
    handler: async () => ({
      code: 400,
      message: '请使用专用接口：/auth/developer/profile 或 /auth/admin/profile',
      data: null,
    }),
  },
  {
    method: 'GET',
    path: '/auth/validate',
    handler: async () => ({ code: 0, message: 'OK', data: { valid: true } }),
  },
]
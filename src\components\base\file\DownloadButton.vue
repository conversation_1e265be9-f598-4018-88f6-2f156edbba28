<template>
  <button
    class="btn btn-primary"
    :disabled="!url || disabled"
    @click="handleClick"
  >
    <slot>下载</slot>
  </button>
</template>

<script setup lang="ts">
import { useDownloadsStore } from '@/stores/downloads'

interface Props {
  url?: string
  filename?: string
  size?: number
  disabled?: boolean
  releaseId?: number
  projectId?: number
}

const props = defineProps<Props>()
const downloads = useDownloadsStore()

function handleClick() {
  if (!props.url) return
  const filename = props.filename || props.url.split('/').pop() || 'download.bin'
  downloads.enqueue({ url: props.url, filename, size: props.size, releaseId: props.releaseId, projectId: props.projectId })
}
</script>

<style scoped>
.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
}
.btn-primary { background: var(--color-primary); color: #fff; }
.btn-primary:hover { background: var(--color-primary-dark); }
</style>



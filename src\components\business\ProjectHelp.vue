<template>
  <div class="help-card">
    <h3 class="card-title">{{ title }}</h3>
    <p class="help-text">{{ description }}</p>
    <button 
      class="btn btn-outline"
      @click="$emit('contact-support')"
    >
      {{ buttonText }}
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  description?: string
  buttonText?: string
}

withDefaults(defineProps<Props>(), {
  title: '需要帮助？',
  description: '如果在使用过程中遇到问题，请联系技术支持团队。',
  buttonText: '联系支持'
})

defineEmits<{
  'contact-support': []
}>()
</script>

<style scoped>
.help-card {
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-4) 0;
}

.help-text {
  color: var(--color-text-2);
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
}

.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--color-text-2);
}

.btn-outline:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}
</style>

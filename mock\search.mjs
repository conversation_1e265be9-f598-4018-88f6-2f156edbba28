const typeOptions = ['firmware', 'app', 'library']
const deviceOptions = ['esp32', 'stm32', 'arduino', 'android', 'ios']

function generateProjects(count = 30) {
  return Array.from({ length: count }).map((_, i) => {
    const type = typeOptions[i % typeOptions.length]
    const devices = [deviceOptions[i % deviceOptions.length], deviceOptions[(i + 2) % deviceOptions.length]]
    const versionCount = (i % 8) + 1
    const downloadCount = (i + 1) * 137
    const latestRelease = {
      version: `v1.${Math.floor(i / 3)}.${i % 3}`,
      createdAt: Date.now() - i * 3600_000,
      type: i % 3 === 0 ? 'stable' : i % 3 === 1 ? 'beta' : 'alpha',
    }
    return {
      id: i + 1,
      name: `项目-${i + 1}`,
      key: `proj-${i + 1}`,
      description: `项目 ${i + 1} 的描述信息，用于展示搜索摘要与列表信息。`,
      type,
      devices,
      owner: i % 3 === 0 ? 'alice' : i % 3 === 1 ? 'bob' : 'carol',
      repo: `https://example.com/repo/${i + 1}`,
      createdAt: Date.now() - i * 86400000,
      updatedAt: Date.now() - Math.floor(i / 2) * 3600000,
      tags: [i % 2 === 0 ? 'web' : 'service', i % 5 === 0 ? 'hot' : 'stable'],
      stats: { versionCount, downloadCount },
      latestRelease,
    }
  })
}

function generateReleases(count = 80) {
  const typeOptions2 = ['stable', 'beta', 'alpha']
  const list = []
  for (let i = 1; i <= count; i++) {
    const projectId = (i % 20) + 1
    const type = typeOptions2[i % typeOptions2.length]
    list.push({
      id: i,
      projectId,
      projectName: `项目-${projectId}`,
      version: `v1.${Math.floor(i / 10)}.${i % 10}`,
      changelog: `更新内容 ${i}`,
      createdAt: Date.now() - i * 3600_000,
      status: i % 3 === 0 ? 'failed' : i % 2 === 0 ? 'pending' : 'success',
      size: 10_000_000 + i * 12345,
      type,
      downloadUrl: `/download/project-${projectId}/release-${i}.zip`,
    })
  }
  return list
}

const projects = generateProjects()
const releases = generateReleases()

export default [
  {
    method: 'GET',
    path: '/search',
    handler: async ({ query }) => {
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 10)
      const q = (query.q || '').toLowerCase()
      const type = String(query.type || '')
      const versionType = String(query.versionType || '')
      const device = String(query.device || '')

      // 搜索 projects
      let proj = projects
      if (q) {
        proj = proj.filter((p) =>
          p.name.toLowerCase().includes(q) ||
          p.key.toLowerCase().includes(q) ||
          (p.description || '').toLowerCase().includes(q)
        )
      }
      if (type) proj = proj.filter((p) => p.type === type)
      if (device) proj = proj.filter((p) => Array.isArray(p.devices) && p.devices.includes(device))

      // 搜索 releases
      let rel = releases
      if (q) {
        rel = rel.filter((r) =>
          r.version.toLowerCase().includes(q) ||
          (r.changelog || '').toLowerCase().includes(q) ||
          String(r.projectName || '').toLowerCase().includes(q)
        )
      }
      if (versionType) rel = rel.filter((r) => r.type === versionType)

      // 构造 facets
      const facets = {
        type: proj.reduce((acc, p) => { acc[p.type] = (acc[p.type] || 0) + 1; return acc }, {}),
        device: proj.flatMap((p) => p.devices || []).reduce((acc, d) => { acc[d] = (acc[d] || 0) + 1; return acc }, {}),
        versionType: rel.reduce((acc, r) => { acc[r.type] = (acc[r.type] || 0) + 1; return acc }, {}),
      }

      const results = [
        ...proj.map((p) => ({
          kind: 'project',
          project: p,
          highlight: q ? { name: p.name.includes(q) ? p.name : undefined, description: p.description?.includes(q) ? p.description : undefined } : undefined,
        })),
        ...rel.map((r) => ({
          kind: 'release',
          release: r,
          highlight: q ? { version: r.version.includes(q) ? r.version : undefined, description: r.changelog?.includes(q) ? r.changelog : undefined } : undefined,
        })),
      ]

      // 简单排序：按项目 updatedAt 与版本 createdAt
      const sorted = results.sort((a, b) => {
        const va = a.kind === 'project' ? a.project.updatedAt : a.release.createdAt
        const vb = b.kind === 'project' ? b.project.updatedAt : b.release.createdAt
        return vb - va
      })

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = sorted.slice(start, end)

      return {
        code: 0,
        message: 'OK',
        data: {
          list,
          page,
          pageSize,
          total: sorted.length,
          facets,
        },
      }
    },
  },
]



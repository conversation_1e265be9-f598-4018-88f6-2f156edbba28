# 软件/固件发布管理系统 - 设计文档总览

## 📋 项目概述

这是一个基于 **Spring Boot + Vue 3** 的前后端分离软件/固件发布管理系统，专为内网环境设计，支持1000用户规模的并发访问。

### 🎯 核心特性

- **三种用户角色**：普通员工（免登录访问）、开发者、超级管理员
- **项目分类管理**：支持固件和APP两大类别
- **版本控制系统**：支持开发版、稳定版、发布版本、测试版本
- **文件管理**：支持任意格式文件上传，最大200MB
- **权限控制**：基于项目的细粒度权限管理
- **操作审计**：完整的操作日志记录

## 📚 文档结构

### 1. [需求设计文档](./需求设计文档.md)
- 详细的功能需求分析
- 用户角色和权限定义
- 业务流程设计
- 功能模块划分

### 2. [系统架构设计](./系统架构设计.md)
- 整体技术架构
- 前后端技术栈选型
- 系统分层设计
- 部署架构方案

### 3. [API接口设计](./API接口设计.md)
- 完整的RESTful API规范
- 接口权限控制
- 请求/响应格式定义
- 错误处理机制

### 4. [数据库设计](./数据库设计.md)
- MySQL数据库表结构
- 索引优化策略
- 数据完整性约束
- 备份和监控方案

### 5. [前端设计](./前端设计.md)
- Vue 3项目架构
- 组件设计规范
- 状态管理方案
- 响应式布局设计

## 🏗️ 技术架构

### 前端技术栈
```
Vue 3 + TypeScript + Vite
├── UI框架: Element Plus
├── 状态管理: Pinia
├── 路由: Vue Router 4
├── HTTP客户端: Axios
└── 构建工具: Vite
```

### 后端技术栈
```
Spring Boot 3.x
├── 安全框架: Spring Security + JWT
├── 数据库: MySQL 8.0+
├── ORM: MyBatis Plus
├── 文档: Swagger/OpenAPI 3
└── 日志: Logback + SLF4J
```

### 数据库设计
```
MySQL 8.0+
├── 用户表 (users)
├── 项目表 (projects)  
├── 权限表 (user_project_permissions)
├── 版本发布表 (software_releases)
├── 下载日志表 (download_logs)
└── 操作日志表 (operation_logs)
```

## 🔐 权限设计

### 用户角色层次
```
Admin (超级管理员)
├── 创建/删除项目
├── 管理所有开发者账号
├── 分配项目权限
└── 系统全局管理

Developer (开发者)
├── 管理分配的项目
├── 上传/管理版本文件
├── 编辑项目信息
└── 查看操作日志

Public User (普通员工)
├── 浏览所有项目
├── 下载文件
└── 查看版本信息
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Java 17+
- MySQL 8.0+
- Maven 3.8+

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

### 后端启动
```bash
cd backend
mvn spring-boot:run
```

### 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE sw_publish_system;

-- 执行 doc/数据库设计.md 中的建表SQL
-- 插入默认admin账号
```

## 📊 系统容量规划

### 性能指标
- **用户数量**: 1000并发用户
- **文件大小**: 最大200MB
- **存储容量**: 预估1TB本地存储
- **数据库**: 支持千万级记录

### 扩展性设计
- **无状态设计**: 支持水平扩展
- **文件存储**: 可扩展到云存储
- **数据库**: 支持读写分离
- **缓存**: 可集成Redis缓存

## 🔍 功能特点

### 📁 项目管理
- **分类管理**: 固件 vs APP项目分类
- **状态控制**: 活跃/暂停/废弃状态管理
- **权限分配**: 开发者项目权限细粒度控制
- **统计分析**: 下载量、版本数统计

### 📦 版本控制
- **版本格式**: 标准化v1.0.0.0格式
- **版本类型**: 开发版/稳定版/发布版本/测试版本
- **设备适配**: 耳机/音箱/Android/iOS等设备类型
- **历史保留**: 完整版本历史记录

### 🔒 安全特性
- **JWT认证**: 无状态Token认证
- **权限验证**: 基于角色的访问控制
- **操作审计**: 详细的操作日志记录
- **文件验证**: SHA256哈希值校验

### 📱 用户体验
- **响应式设计**: 支持移动端访问
- **文件上传**: 拖拽上传+进度显示
- **搜索筛选**: 多维度搜索和筛选
- **下载统计**: 实时下载数据统计

## 🛠️ 开发规范

### 代码风格
- **前端**: ESLint + Prettier
- **后端**: Alibaba Java代码规范
- **数据库**: 统一命名规范

### Git工作流
```
main (生产分支)
├── develop (开发分支)
├── feature/* (功能分支)
├── hotfix/* (修复分支)
└── release/* (发布分支)
```

## 🎉 总结

这套设计文档为软件/固件发布管理系统提供了完整的技术方案，涵盖了从需求分析到具体实现的各个方面。系统采用现代化的技术栈，具有良好的扩展性和维护性，能够满足1000用户规模的内网环境需求。

通过分层架构设计、细粒度权限控制和完善的日志审计，系统在保证功能完整性的同时，也确保了安全性和可监控性。前后端分离的设计使得系统具有良好的技术架构，便于团队协作开发和后期维护。

---

type ErrorPayload = {
  type: string
  message: string
  stack?: string
  url?: string
  line?: number
  column?: number
  status?: number
  method?: string
  time: number
  extra?: Record<string, unknown>
}

function shouldReport(): boolean {
  const flag = import.meta.env.VITE_REPORT_ERROR
  if (typeof flag === 'string') return flag === 'true'
  return import.meta.env.MODE === 'production'
}

export function setupGlobalErrorHandlers() {
  // Vue 错误：在应用创建后设置
  // 将在 main.ts 中通过 app.config.errorHandler 挂载

  // window 全局错误
  window.addEventListener('error', (event) => {
    const err = event.error
    const payload: ErrorPayload = {
      type: 'window',
      message: err?.message || String(event.message || 'Unknown Error'),
      stack: err?.stack,
      url: event.filename,
      line: event.lineno || undefined,
      column: event.colno || undefined,
      time: Date.now(),
    }
    reportError(payload)
  })

  // Promise 未处理异常
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason
    const payload: ErrorPayload = {
      type: 'promise',
      message: reason?.message || String(reason),
      stack: reason?.stack,
      time: Date.now(),
    }
    reportError(payload)
  })
}

export async function reportError(payload: ErrorPayload) {
  if (!shouldReport()) {
    // 开发环境仅打印，避免噪音上报
    try {
      // eslint-disable-next-line no-console
      console.warn('[dev-error]', payload)
    } catch {}
    return
  }
  try {
    await fetch('/monitor/error', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    })
  } catch {
    // 忽略上报失败
  }
}

export { shouldReport }

export type MetricPayload = {
  type: 'http'
  name?: string
  url?: string
  method?: string
  status?: number
  success: boolean
  durationMs: number
  time: number
}

export async function reportMetric(payload: MetricPayload) {
  if (!shouldReport()) {
    try {
      // eslint-disable-next-line no-console
      console.warn('[dev-metric]', payload)
    } catch {}
    return
  }
  try {
    await fetch('/monitor/metric', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    })
  } catch {
    // 忽略上报失败
  }
}



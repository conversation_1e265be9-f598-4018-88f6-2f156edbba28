<template>
  <div class="page page-file-preview">
    <h1 class="title">文件预览</h1>
    <div class="preview">
      <template v-if="isImage">
        <img :src="url" class="img" alt="preview" />
      </template>
      <template v-else-if="isText">
        <iframe :src="url" class="frame"></iframe>
      </template>
      <template v-else>
        <div class="fallback">不支持的预览类型，请下载查看。</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const url = computed(() => String(route.query.url || ''))
const ext = computed(() => (url.value.split('?')[0].split('#')[0].split('.').pop() || '').toLowerCase())
const isImage = computed(() => ['png','jpg','jpeg','gif','webp','svg'].includes(ext.value))
const isText = computed(() => ['md','txt','json','yaml','yml','log'].includes(ext.value))
</script>

<style scoped>
.title { margin: 0 0 var(--spacing-3) 0; }
.preview { background: var(--bg-1); border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: var(--spacing-3); min-height: 320px; display: flex; align-items: center; justify-content: center; }
.img { max-width: 100%; max-height: 70vh; border-radius: var(--radius-md); }
.frame { width: 100%; height: 70vh; border: none; border-radius: var(--radius-md); }
.fallback { color: var(--color-text-3); }
</style>



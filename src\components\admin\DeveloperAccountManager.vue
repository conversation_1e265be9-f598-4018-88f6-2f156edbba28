<template>
  <div class="developer-account-manager">
    <div class="section-header">
      <h2 class="section-title">开发者账号管理</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="showCreateModal = true">
          新增开发者
        </button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filters-bar">
      <div class="search-box">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜索开发者..." 
          class="search-input"
          @input="handleSearch"
        >
      </div>
    </div>

    <!-- 开发者列表 -->
    <div class="developers-table card">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>用户名</th>
            <th>管理项目数</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading && developers.length === 0">
            <td colspan="5" class="loading-cell">加载中...</td>
          </tr>
          <tr v-else-if="developers.length === 0">
            <td colspan="5" class="empty-cell">暂无开发者数据</td>
          </tr>
          <tr v-for="developer in paginatedDevelopers" :key="developer.id" v-else>
            <td>{{ developer.id }}</td>
            <td>{{ developer.username }}</td>
            <td>
              <span class="project-count">{{ developer.projectCount || 0 }} 个</span>
            </td>
            <td>{{ formatTime(developer.createdAt) }}</td>
            <td>
              <div class="action-buttons">
                <button 
                  class="btn btn-sm btn-outline" 
                  @click="editDeveloper(developer)"
                >
                  编辑
                </button>
                <button 
                  class="btn btn-sm btn-danger" 
                  @click="deleteDeveloper(developer)"
                  :disabled="developer.projectCount > 0"
                >
                  删除
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          class="btn btn-sm btn-outline" 
          :disabled="currentPage <= 1" 
          @click="changePage(currentPage - 1)"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
        </span>
        <button 
          class="btn btn-sm btn-outline" 
          :disabled="currentPage >= totalPages" 
          @click="changePage(currentPage + 1)"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑开发者弹窗 -->
    <div v-if="showCreateModal" class="modal-overlay" @click="closeCreateModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingDeveloper ? '编辑开发者' : '新增开发者' }}</h3>
          <button class="modal-close" @click="closeCreateModal">×</button>
        </div>
        <form class="developer-form" @submit.prevent="saveDeveloper">
          <div class="form-group">
            <label class="form-label">用户名 *</label>
            <input
              v-model="developerForm.username"
              type="text"
              class="form-input"
              placeholder="请输入用户名"
              required
              :disabled="!!editingDeveloper"
            />
          </div>
          <div class="form-group" v-if="!editingDeveloper">
            <label class="form-label">密码 *</label>
            <input
              v-model="developerForm.password"
              type="password"
              class="form-input"
              placeholder="请输入初始密码"
              required
            />
          </div>
          <div v-if="formError" class="error-message">
            {{ formError }}
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeCreateModal">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? '保存中...' : '确认保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="showDeleteModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="modal-close" @click="showDeleteModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除开发者 <strong>{{ deletingDeveloper?.username }}</strong> 吗？</p>
          <p class="warning-text">此操作不可恢复，请谨慎操作。</p>
        </div>
        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" @click="showDeleteModal = false">取消</button>
          <button type="button" class="btn btn-danger" @click="confirmDelete" :disabled="deleting">
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { getDataService } from '@/data'
import type { DeveloperInfo } from '@/data'

const dataService = getDataService()

interface DeveloperForm {
  username: string
  password?: string
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const developers = ref<DeveloperInfo[]>([])
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = 10
const totalCount = ref(0)

// 弹窗状态
const showCreateModal = ref(false)
const showDeleteModal = ref(false)
const editingDeveloper = ref<DeveloperInfo | null>(null)
const deletingDeveloper = ref<DeveloperInfo | null>(null)

// 表单数据
const developerForm = reactive<DeveloperForm>({
  username: '',
  password: ''
})

const formError = ref('')

// 计算属性
const paginatedDevelopers = computed(() => developers.value)

const totalPages = computed(() => {
  return Math.ceil(totalCount.value / pageSize)
})

// 时间格式化
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  loadDevelopers()
}

// 分页
function changePage(page: number) {
  currentPage.value = page
  loadDevelopers()
}

// 加载开发者列表
async function loadDevelopers() {
  loading.value = true
  try {
    const result = await dataService.getDevelopers({
      page: currentPage.value,
      pageSize,
      search: searchQuery.value,
      sortBy: 'createdAt',
      order: 'desc'
    })
    developers.value = result.list
    totalCount.value = result.total
  } catch (error) {
    console.error('加载开发者列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 编辑开发者
function editDeveloper(developer: DeveloperInfo) {
  editingDeveloper.value = developer
  Object.assign(developerForm, {
    username: developer.username
  })
  showCreateModal.value = true
}

// 删除开发者
function deleteDeveloper(developer: DeveloperInfo) {
  if (developer.projectCount > 0) {
    alert('该开发者还管理着项目，无法删除')
    return
  }
  deletingDeveloper.value = developer
  showDeleteModal.value = true
}

// 确认删除
async function confirmDelete() {
  if (!deletingDeveloper.value) return
  
  deleting.value = true
  try {
    await dataService.deleteDeveloper(deletingDeveloper.value.id as number)
    
    showDeleteModal.value = false
    deletingDeveloper.value = null
    
    // 重新加载开发者列表
    await loadDevelopers()
  } catch (error) {
    console.error('删除失败:', error)
  } finally {
    deleting.value = false
  }
}

// 关闭创建弹窗
function closeCreateModal() {
  showCreateModal.value = false
  editingDeveloper.value = null
  formError.value = ''
  Object.assign(developerForm, {
    username: '',
    password: ''
  })
}

// 保存开发者
async function saveDeveloper() {
  formError.value = ''
  
  // 基础验证
  if (!developerForm.username) {
    formError.value = '请填写用户名'
    return
  }
  
  if (!editingDeveloper.value && !developerForm.password) {
    formError.value = '请设置初始密码'
    return
  }
  
  saving.value = true
  try {
    if (editingDeveloper.value) {
      // 更新现有开发者
      await dataService.updateDeveloper(editingDeveloper.value.id as number, {
        name: developerForm.username
      })
    } else {
      // 创建新开发者
      await dataService.createDeveloper({
        username: developerForm.username,
        password: developerForm.password!
      })
    }
    
    closeCreateModal()
    // 重新加载开发者列表
    await loadDevelopers()
  } catch (error) {
    console.error('保存失败:', error)
    formError.value = '保存失败，请重试'
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(() => {
  loadDevelopers()
})
</script>

<style scoped>
.developer-account-manager {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 搜索和过滤栏 */
.filters-bar {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.filter-select {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* 表格样式 */
.developers-table {
  overflow-x: auto;
}

.developers-table table {
  width: 100%;
  border-collapse: collapse;
}

.developers-table th,
.developers-table td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.developers-table th {
  background: var(--bg-2);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-2);
  font-size: var(--font-size-sm);
}

.developers-table tbody tr:hover {
  background: var(--bg-2);
}

.loading-cell,
.empty-cell {
  text-align: center;
  color: var(--color-text-3);
  font-style: italic;
  padding: var(--spacing-6) !important;
}

.project-count {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.status-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.active {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-badge.inactive {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-2);
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

.page-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 480px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-5);
}

.warning-text {
  color: var(--color-warning-dark);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-2);
}

.developer-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input,
.form-select {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

.btn-warning {
  background: var(--color-warning);
  color: white;
}

.btn-warning:hover {
  background: var(--color-warning-dark);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .filters-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
}
</style>

# 软件/固件发布管理系统 - 架构设计文档

## 1. 整体架构

### 1.1 技术栈选型

#### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia（推荐）或 Vuex
- **路由**: Vue Router 4
- **UI组件库**: Element Plus 或 Ant Design Vue
- **HTTP客户端**: Axios
- **文件上传**: axios + 进度条组件

#### 后端技术栈
- **框架**: Spring Boot 3.x
- **认证**: Spring Security + JWT
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus 或 JPA
- **文件存储**: 本地文件系统
- **API文档**: Swagger/OpenAPI 3
- **日志**: Logback + SLF4J

### 1.2 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   普通员工端     │    │    开发者端      │    │   Admin端       │
│  (无需登录)      │    │   (需要登录)     │    │  (超级管理员)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Nginx反向代理  │
                    │  (静态资源服务)  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Vue.js前端    │
                    │  (SPA单页应用)   │
                    └─────────────────┘
                                 │
                          HTTP/HTTPS
                                 │
                    ┌─────────────────┐
                    │  Spring Boot     │
                    │   后端API服务    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  本地文件系统    │
                    │  (文件存储)      │
                    └─────────────────┘
```

## 2. 前端架构设计

### 2.1 项目结构
```
src/
├── api/              # API接口定义
│   ├── auth.ts       # 认证相关API
│   ├── projects.ts   # 项目管理API
│   ├── releases.ts   # 版本发布API
│   └── users.ts      # 用户管理API
├── components/       # 公共组件
│   ├── common/       # 通用组件
│   ├── layout/       # 布局组件
│   └── upload/       # 文件上传组件
├── views/           # 页面组件
│   ├── public/      # 公开页面（普通员工）
│   ├── developer/   # 开发者页面
│   └── admin/       # 管理员页面
├── router/          # 路由配置
├── store/           # 状态管理
├── utils/           # 工具函数
├── types/           # TypeScript类型定义
└── assets/          # 静态资源
```

### 2.2 页面路由设计

#### 公开路由（普通员工访问）
- `/` - 首页（项目列表）
- `/project/:id` - 项目详情页
- `/project/:id/releases` - 项目版本列表
- `/release/:id` - 版本详情页
- `/download/:id` - 文件下载

#### 开发者路由（需要登录）
- `/login` - 登录页
- `/developer` - 开发者工作台
- `/developer/projects` - 我的项目列表
- `/developer/project/:id` - 项目管理页
- `/developer/project/:id/releases` - 版本管理
- `/developer/upload` - 文件上传页

#### Admin路由（超级管理员）
- `/admin` - Admin控制台
- `/admin/projects` - 项目管理
- `/admin/users` - 用户管理
- `/admin/permissions` - 权限分配
- `/admin/logs` - 操作日志

### 2.3 状态管理设计

#### Store模块划分
```typescript
// stores/auth.ts - 认证状态
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  permissions: string[];
}

// stores/projects.ts - 项目状态
interface ProjectState {
  currentProject: Project | null;
  projectList: Project[];
  userProjects: Project[]; // 开发者分配的项目
}

// stores/releases.ts - 版本发布状态
interface ReleaseState {
  releases: Release[];
  currentRelease: Release | null;
  uploadProgress: number;
}
```

## 3. 后端架构设计

### 3.1 项目结构
```
src/main/java/com/company/swpublish/
├── config/           # 配置类
│   ├── SecurityConfig.java
│   ├── WebConfig.java
│   └── FileConfig.java
├── controller/       # 控制器层
│   ├── PublicController.java    # 公开接口
│   ├── AuthController.java      # 认证接口
│   ├── ProjectController.java   # 项目管理
│   ├── ReleaseController.java   # 版本发布
│   └── AdminController.java     # 管理员接口
├── service/          # 业务逻辑层
│   ├── AuthService.java
│   ├── ProjectService.java
│   ├── ReleaseService.java
│   ├── UserService.java
│   └── FileService.java
├── repository/       # 数据访问层
│   ├── UserRepository.java
│   ├── ProjectRepository.java
│   └── ReleaseRepository.java
├── entity/           # 实体类
│   ├── User.java
│   ├── Project.java
│   ├── Release.java
│   └── OperationLog.java
├── dto/              # 数据传输对象
├── security/         # 安全相关
│   ├── JwtTokenProvider.java
│   ├── JwtAuthenticationFilter.java
│   └── CustomUserDetailsService.java
└── utils/            # 工具类
    ├── FileUtils.java
    └── ResponseUtils.java
```

### 3.2 分层架构设计

#### Controller层职责
- 接收HTTP请求
- 参数验证和转换
- 调用Service层处理业务
- 返回统一格式响应

#### Service层职责
- 业务逻辑处理
- 事务管理
- 数据校验
- 调用Repository层

#### Repository层职责
- 数据库操作
- 数据持久化
- 查询优化

### 3.3 安全架构设计

#### 认证流程
1. Admin账号硬编码在配置文件中
2. 开发者通过用户名密码登录
3. 生成JWT Token包含用户信息和权限
4. 前端在请求头中携带Token
5. 后端验证Token并获取用户权限

#### 权限控制
- 基于角色的访问控制（RBAC）
- 方法级权限注解
- 项目级权限检查

## 4. 数据流设计

### 4.1 用户访问流程

#### 普通员工访问流程
```
用户访问 → 前端路由 → 调用公开API → 返回项目/版本数据 → 渲染页面
```

#### 开发者操作流程
```
登录认证 → 获取JWT Token → 访问权限验证 → 调用业务API → 操作数据 → 记录日志
```

#### Admin管理流程
```
Admin登录 → 最高权限验证 → 管理操作 → 影响系统配置 → 记录操作日志
```

### 4.2 文件上传流程
```
选择文件 → 前端校验 → 生成上传Token → 分片上传（可选） → 服务器存储 → 
返回文件信息 → 保存版本记录 → 更新数据库
```

### 4.3 文件下载流程
```
用户点击下载 → 验证文件权限 → 生成下载链接 → 
记录下载日志 → 文件流传输 → 更新下载统计
```

## 5. 性能与扩展性设计

### 5.1 性能优化策略
- **前端优化**：
  - 组件懒加载
  - 图片懒加载
  - API请求缓存
  - 分页加载

- **后端优化**：
  - 数据库连接池
  - 查询结果缓存
  - 文件分片上传
  - 异步处理

### 5.2 扩展性考虑
- **水平扩展**：无状态设计，支持负载均衡
- **存储扩展**：可扩展到云存储（OSS/S3）
- **数据库扩展**：读写分离，分库分表
- **监控告警**：集成监控系统

## 6. 部署架构

### 6.1 内网部署方案
```
┌─────────────────┐
│   Nginx代理      │ (端口80/443)
│  静态文件服务    │
└─────────────────┘
         │
┌─────────────────┐
│  Spring Boot    │ (端口8080)
│   应用服务器     │
└─────────────────┘
         │
┌─────────────────┐
│   MySQL数据库   │ (端口3306)
└─────────────────┘
         │
┌─────────────────┐
│  文件存储目录    │ (/data/uploads)
└─────────────────┘
```

### 6.2 环境配置
- **开发环境**：单机部署，H2内存数据库
- **测试环境**：模拟生产，MySQL数据库
- **生产环境**：内网部署，数据备份策略

这个架构设计为1000用户的内网环境进行了优化，既保证了功能完整性，又考虑了系统的可维护性和扩展性。

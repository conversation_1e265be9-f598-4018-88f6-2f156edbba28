<template>
  <div class="release-card" @click="goToProject">
    <div class="card-header">
      <div class="project-info">
        <h3 class="project-name">{{ item.projectName || `项目-${item.projectId}` }}</h3>
        <div class="project-meta">
          <span class="artifact-type" v-if="item.artifact">{{ formatArtifact(item.artifact) }}</span>
          <span class="release-type" v-if="item.type">{{ formatReleaseType(item.type) }}</span>
        </div>
      </div>
      <span class="version-badge">{{ item.version }}</span>
    </div>
    
    <div class="card-content">
      <p class="changelog" v-if="item.changelog">{{ truncateText(item.changelog, 80) }}</p>
      <div class="card-footer">
        <span class="update-time">{{ formatDateTime(item.createdAt) }}</span>
        <DownloadButton
          v-if="item.downloadUrl"
          :url="item.downloadUrl"
          :filename="`${item.projectName || `项目-${item.projectId}`}-${item.version}.zip`"
          :size="item.size"
          :releaseId="item.id"
          :projectId="item.projectId"
          class="download-btn"
          @click.stop
        >
          下载
        </DownloadButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { ReleaseItem } from '@/types/domain'
import { formatArtifact, formatDateTime, formatReleaseType } from '@/utils/format'
import DownloadButton from '@/components/base/file/DownloadButton.vue'

interface Props {
  item: ReleaseItem
}

const props = defineProps<Props>()
const router = useRouter()

function goToProject() {
  router.push(`/project/${props.item.projectId}`)
}

function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}
</script>

<style scoped>
.release-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  transition: var(--transition-all);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.release-card:hover {
  border-color: var(--color-primary-300);
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.release-card:active {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
  gap: var(--spacing-4);
}

.project-info {
  flex: 1;
  min-width: 0;
}

.project-name {
  font-family: var(--font-family-display);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-2) 0;
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-meta {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.artifact-type,
.release-type {
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  background: var(--bg-secondary);
  color: var(--color-text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.03em;
  border: 1px solid var(--border-primary);
}

.version-badge {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-1_5) var(--spacing-3);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  white-space: nowrap;
  box-shadow: var(--shadow-sm);
  letter-spacing: -0.01em;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.changelog {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-4) 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-primary);
  margin-top: auto;
}

.update-time {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.download-btn {
  flex-shrink: 0;
}

/* 下载按钮样式调整 */
.download-btn :deep(.btn) {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-xl);
  background: var(--color-primary);
  color: white;
  transition: var(--transition-all);
  box-shadow: var(--shadow-sm);
}

.download-btn :deep(.btn:hover) {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
</style>



const list = Array.from({ length: 24 }).map((_, i) => ({
  id: i + 1,
  name: `用户-${i + 1}`,
  username: `user${i + 1}`,
  roles: i % 5 === 0 ? ['admin'] : ['user'],
  email: `user${i + 1}@mock.dev`,
  status: i % 7 === 0 ? 'disabled' : 'active',
}))

export default [
  {
    method: 'GET',
    path: '/users',
    handler: async ({ query }) => {
      const page = Number(query.page || 1)
      const pageSize = Number(query.pageSize || 10)
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const data = list.slice(start, end)
      return { code: 0, message: 'OK', data: { list: data, page, pageSize, total: list.length } }
    },
  },
  {
    method: 'GET',
    path: '/users/:id',
    handler: async ({ params }) => {
      const id = Number(params.id)
      const item = list.find((x) => x.id === id)
      if (!item) return { code: 404, message: '未找到', data: null }
      return { code: 0, message: 'OK', data: item }
    },
  },
  {
    method: 'POST',
    path: '/users/:id/disable',
    handler: async ({ params }) => {
      const id = Number(params.id)
      const item = list.find((x) => x.id === id)
      if (!item) return { code: 404, message: '未找到', data: null }
      item.status = 'disabled'
      return { code: 0, message: 'OK', data: true }
    },
  },
]



<template>
  <div class="admin-dashboard">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="user-info">
        <h1 class="dashboard-title">管理员控制中心</h1>
        <p class="dashboard-subtitle">欢迎回来，{{ user?.name || '管理员' }}</p>
        <div class="admin-level" v-if="user?.level">
          <span :class="['level-badge', user.level]">{{ levelText }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary" @click="logout">退出登录</button>
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="dashboard-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        :class="['tab-button', { active: activeTab === tab.key }]"
        @click="activeTab = tab.key"
      >
        <i :class="tab.icon"></i>
        {{ tab.label }}
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="dashboard-content">
      <!-- 系统概览 -->
      <AdminOverview 
        v-if="activeTab === 'overview'" 
        ref="overviewRef"
      />
      
      <!-- 开发者账号管理 -->
      <DeveloperAccountManager 
        v-if="activeTab === 'developers'"
        ref="developersRef"
      />
      
      <!-- 项目管理 -->
      <ProjectManager 
        v-if="activeTab === 'projects'"
        ref="projectsRef"
      />
      
      <!-- 权限分配管理 -->
      <PermissionManager 
        v-if="activeTab === 'permissions'"
        ref="permissionsRef"
      />
    </div>

    <!-- 密码修改弹窗 -->
    <div v-if="showPasswordModal" class="modal-overlay" @click="closePasswordModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>修改管理员密码</h3>
          <button class="modal-close" @click="closePasswordModal">×</button>
        </div>
        <form class="password-form" @submit.prevent="changePassword">
          <div class="form-group">
            <label class="form-label">当前密码</label>
            <input
              v-model="passwordForm.oldPassword"
              type="password"
              class="form-input"
              placeholder="请输入当前密码"
              required
            />
          </div>
          <div class="form-group">
            <label class="form-label">新密码</label>
            <input
              v-model="passwordForm.newPassword"
              type="password"
              class="form-input"
              placeholder="请输入新密码"
              required
            />
          </div>
          <div class="form-group">
            <label class="form-label">确认新密码</label>
            <input
              v-model="passwordForm.confirmPassword"
              type="password"
              class="form-input"
              placeholder="请再次输入新密码"
              required
            />
          </div>
          <div v-if="passwordError" class="error-message">
            {{ passwordError }}
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closePasswordModal">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="passwordLoading">
              {{ passwordLoading ? '修改中...' : '确认修改' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'
// 密码更新功能暂时保留旧API
// import { updateAdminPassword } from '@/api/adminAuth'
import AdminOverview from '@/components/admin/AdminOverview.vue'
import DeveloperAccountManager from '@/components/admin/DeveloperAccountManager.vue'
import ProjectManager from '@/components/admin/ProjectManager.vue'
import PermissionManager from '@/components/admin/PermissionManager.vue'

const router = useRouter()
const adminAuth = useAdminAuthStore()

// 标签页配置
const tabs = [
  { key: 'overview', label: '系统概览', icon: 'icon-dashboard' },
  { key: 'developers', label: '开发者管理', icon: 'icon-users' },
  { key: 'projects', label: '项目管理', icon: 'icon-projects' },
  { key: 'permissions', label: '权限分配', icon: 'icon-key' }
]

const activeTab = ref('overview')

// 组件引用
const overviewRef = ref()
const developersRef = ref()
const projectsRef = ref()
const permissionsRef = ref()

// 计算属性
const user = computed(() => adminAuth.user)
const levelText = computed(() => {
  if (user.value?.level === 'super') return '超级管理员'
  if (user.value?.level === 'normal') return '普通管理员'
  return '管理员'
})

// 密码修改相关
const showPasswordModal = ref(false)
const passwordLoading = ref(false)
const passwordError = ref('')
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 退出登录
function logout() {
  adminAuth.logout()
  router.replace('/')
}


// 关闭密码修改弹窗
function closePasswordModal() {
  showPasswordModal.value = false
  passwordError.value = ''
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}

// 修改密码
async function changePassword() {
  passwordError.value = ''
  
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    passwordError.value = '两次输入的新密码不一致'
    return
  }
  
  if (passwordForm.newPassword.length < 6) {
    passwordError.value = '新密码长度不能少于6位'
    return
  }
  
  passwordLoading.value = true
  
  try {
    // 模拟密码更新API
    await new Promise(resolve => setTimeout(resolve, 1000))
    closePasswordModal()
    alert('密码修改成功')
  } catch (error: any) {
    passwordError.value = error?.message || '密码修改失败'
  } finally {
    passwordLoading.value = false
  }
}

// 初始化
onMounted(() => {
  // 页面加载时不需要特别的初始化
  // 各个组件会自行初始化
})
</script>

<style scoped>
.admin-dashboard {
  padding: var(--spacing-6);
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.dashboard-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-1) 0;
}

.dashboard-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-3);
  margin: 0 0 var(--spacing-2) 0;
}

.admin-level {
  margin-top: var(--spacing-2);
}

.level-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.level-badge.super {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.level-badge.normal {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 导航标签 */
.dashboard-tabs {
  display: flex;
  border-bottom: 2px solid var(--border-color);
  margin-bottom: var(--spacing-6);
  gap: var(--spacing-2);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  background: none;
  border: none;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-3);
  cursor: pointer;
  transition: all var(--transition-base);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  position: relative;
}

.tab-button:hover {
  color: var(--color-text-1);
  background: var(--bg-2);
}

.tab-button.active {
  color: var(--color-primary);
  background: var(--bg-1);
  border-bottom: 2px solid var(--color-primary);
  margin-bottom: -2px;
}

.tab-button i {
  font-size: var(--font-size-lg);
}

/* 内容区域 */
.dashboard-content {
  min-height: 600px;
}

/* Modal Styles - Same as Developer Dashboard */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 420px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input {
  height: 40px;
  padding: 0 var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
  .admin-dashboard {
    padding: var(--spacing-4);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .dashboard-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .dashboard-tabs::-webkit-scrollbar {
    display: none;
  }
  
  .tab-button {
    flex-shrink: 0;
    padding: var(--spacing-3) var(--spacing-4);
  }
}

@media (max-width: 480px) {
  .tab-button {
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
  
  .tab-button i {
    font-size: var(--font-size-md);
  }
}
</style>



<script setup lang="ts">
import * as ElIcons from '@element-plus/icons-vue'

type IconName = keyof typeof ElIcons | string

const props = withDefaults(defineProps<{
  name: IconName
  size?: number | string
  color?: string
  rotate?: number
  spin?: boolean
}>(), {
  size: 16,
  color: undefined,
  rotate: 0,
  spin: false,
})

const iconStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
  transform: props.rotate ? `rotate(${props.rotate}deg)` : undefined,
}))

const Comp = computed(() => {
  const key = props.name as keyof typeof ElIcons
  const found = (ElIcons as any)[key]
  return found || (ElIcons as any).QuestionFilled
})
</script>

<template>
  <span class="base-icon" :class="{ spin: props.spin }" :style="iconStyle">
    <component :is="Comp" />
  </span>
  
</template>

<style scoped>
.base-icon {
  display: inline-flex;
  line-height: 1;
}
.spin {
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>





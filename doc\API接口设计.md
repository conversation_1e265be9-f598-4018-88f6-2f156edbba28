# API接口设计文档

## 1. 接口规范

### 1.1 基础信息
- **Base URL**: `/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **认证方式**: JWT <PERSON> (Header: `Authorization: Bearer <token>`)

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}, 
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 1.3 状态码定义
- `200` - 操作成功
- `400` - 请求参数错误
- `401` - 未认证或认证失败
- `403` - 无权限访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 2. 公开接口（无需认证）

### 2.1 项目相关

#### 获取项目列表
```http
GET /api/public/projects
```

**查询参数**:
- `category` (可选): 项目分类 (firmware/app)
- `page` (可选): 页码，默认1
- `size` (可选): 每页条数，默认20

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "projects": [
      {
        "id": 1,
        "name": "智能耳机固件",
        "description": "最新款蓝牙耳机固件",
        "category": "firmware",
        "latestVersion": "v1.2.0.0",
        "updateTime": "2024-01-15T10:30:00Z",
        "downloadCount": 1580
      }
    ]
  }
}
```

#### 获取项目详情
```http
GET /api/public/projects/{projectId}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "智能耳机固件",
    "description": "支持ANC降噪的蓝牙5.0耳机固件",
    "category": "firmware",
    "status": "active",
    "createdAt": "2023-06-01T09:00:00Z",
    "releasesCount": 12,
    "totalDownloads": 15800
  }
}
```

### 2.2 版本发布相关

#### 获取项目版本列表
```http
GET /api/public/projects/{projectId}/releases
```

**查询参数**:
- `versionType` (可选): 版本类型 (开发版/稳定版/发布版本/测试版本)
- `deviceType` (可选): 设备类型 (耳机/音箱/Android/iOS)
- `page` (可选): 页码
- `size` (可选): 每页条数

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 12,
    "releases": [
      {
        "id": 1,
        "version": "v1.2.0.0",
        "versionType": "发布版本",
        "title": "重大功能更新",
        "description": "新增智能降噪算法，修复连接稳定性问题",
        "deviceType": "耳机",
        "fileSize": "2.5MB",
        "downloadCount": 850,
        "publishedAt": "2024-01-15T10:30:00Z",
        "publisher": "张工程师"
      }
    ]
  }
}
```

#### 获取版本详情
```http
GET /api/public/releases/{releaseId}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "projectId": 1,
    "projectName": "智能耳机固件",
    "version": "v1.2.0.0",
    "versionType": "发布版本",
    "title": "重大功能更新",
    "description": "## 新增功能\n- 智能降噪算法2.0\n- 语音助手优化\n\n## 问题修复\n- 修复蓝牙连接稳定性\n- 优化电池续航",
    "deviceType": "耳机",
    "fileSize": "2.5MB",
    "fileHash": "sha256:abc123...",
    "downloadCount": 850,
    "publishedAt": "2024-01-15T10:30:00Z",
    "publisher": "张工程师",
    "compatibility": "适用于X1/X2/X3系列耳机"
  }
}
```

### 2.3 文件下载

#### 下载文件
```http
GET /api/public/download/{releaseId}
```

**响应**: 文件流，同时记录下载日志

#### 获取下载链接
```http
GET /api/public/download/{releaseId}/url
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "downloadUrl": "/api/public/download/1",
    "fileName": "smart_earphone_v1.2.0.0.bin",
    "fileSize": "2.5MB",
    "expiresAt": "2024-01-16T10:30:00Z"
  }
}
```

### 2.4 搜索功能

#### 综合搜索
```http
GET /api/public/search
```

**查询参数**:
- `keyword` (必需): 搜索关键词
- `type` (可选): 搜索类型 (project/release/all), 默认all
- `category` (可选): 项目分类 (firmware/app)
- `deviceType` (可选): 设备类型
- `versionType` (可选): 版本类型
- `page` (可选): 页码
- `size` (可选): 每页条数
- `sortBy` (可选): 排序字段 (relevance/name/updateTime/downloadCount)
- `sortOrder` (可选): 排序方向 (asc/desc)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "keyword": "蓝牙耳机",
    "results": {
      "projects": [
        {
          "id": 1,
          "name": "智能蓝牙耳机固件",
          "description": "支持ANC降噪的蓝牙5.0耳机固件",
          "category": "firmware",
          "matchScore": 0.95,
          "highlightFields": ["name", "description"]
        }
      ],
      "releases": [
        {
          "id": 1,
          "projectId": 1,
          "projectName": "智能蓝牙耳机固件",
          "version": "v1.2.0.0",
          "title": "蓝牙连接优化版本",
          "description": "优化蓝牙连接稳定性...",
          "deviceType": "耳机",
          "matchScore": 0.88,
          "highlightFields": ["title", "description"]
        }
      ]
    },
    "facets": {
      "categories": [
        {"name": "firmware", "count": 15},
        {"name": "app", "count": 10}
      ],
      "deviceTypes": [
        {"name": "耳机", "count": 12},
        {"name": "音箱", "count": 8}
      ]
    },
    "suggestions": ["蓝牙音箱", "无线耳机", "降噪耳机"]
  }
}
```

#### 项目搜索
```http
GET /api/public/projects/search
```

**查询参数**:
- `keyword` (必需): 搜索关键词
- `category` (可选): 项目分类
- `exact` (可选): 是否精确匹配，默认false

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 8,
    "projects": [
      {
        "id": 1,
        "name": "智能蓝牙耳机固件",
        "description": "支持ANC降噪的蓝牙5.0耳机固件",
        "category": "firmware",
        "latestVersion": "v1.2.0.0",
        "totalDownloads": 1580,
        "matchScore": 0.95,
        "snippet": "智能<em>蓝牙耳机</em>固件项目，支持ANC降噪..."
      }
    ]
  }
}
```

#### 版本搜索
```http
GET /api/public/releases/search
```

**查询参数**:
- `keyword` (必需): 搜索关键词
- `projectId` (可选): 项目ID
- `versionType` (可选): 版本类型
- `deviceType` (可选): 设备类型
- `dateRange` (可选): 发布日期范围 (7d/30d/90d)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 12,
    "releases": [
      {
        "id": 1,
        "projectId": 1,
        "projectName": "智能蓝牙耳机固件",
        "version": "v1.2.0.0",
        "versionType": "发布版本",
        "title": "蓝牙连接优化版本",
        "description": "修复蓝牙连接稳定性问题，优化音质表现",
        "deviceType": "耳机",
        "publishedAt": "2024-01-15T10:30:00Z",
        "downloadCount": 850,
        "matchScore": 0.88,
        "snippet": "修复<em>蓝牙连接</em>稳定性问题..."
      }
    ]
  }
}
```

#### 搜索建议
```http
GET /api/public/search/suggestions
```

**查询参数**:
- `query`: 输入的查询字符串

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "suggestions": [
      {
        "text": "蓝牙耳机",
        "type": "keyword",
        "count": 15
      },
      {
        "text": "智能蓝牙耳机固件",
        "type": "project",
        "projectId": 1
      },
      {
        "text": "v1.2.0.0",
        "type": "version",
        "projectId": 1,
        "releaseId": 1
      }
    ]
  }
}
```

## 3. 认证接口

### 3.1 用户登录
```http
POST /api/auth/login
```

**请求体**:
```json
{
  "username": "developer01",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "developer01",
      "email": "<EMAIL>",
      "role": "developer",
      "projects": [1, 3, 5]
    },
    "expiresAt": "2024-01-16T10:30:00Z"
  }
}
```

### 3.2 Token验证
```http
GET /api/auth/verify
```

**Headers**: `Authorization: Bearer <token>`

**响应示例**:
```json
{
  "code": 200,
  "message": "Token有效",
  "data": {
    "valid": true,
    "user": {
      "id": 1,
      "username": "developer01",
      "role": "developer"
    }
  }
}
```

### 3.3 退出登录
```http
POST /api/auth/logout
```

## 4. 开发者接口（需要认证）

### 4.1 项目管理

#### 获取我的项目列表
```http
GET /api/developer/projects
```

**Headers**: `Authorization: Bearer <token>`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "projects": [
      {
        "id": 1,
        "name": "智能耳机固件",
        "description": "蓝牙耳机固件项目",
        "category": "firmware",
        "permission": "full", // full/readonly
        "releasesCount": 12,
        "lastUpdated": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### 更新项目信息
```http
PUT /api/developer/projects/{projectId}
```

**请求体**:
```json
{
  "description": "更新后的项目描述",
  "deviceTypes": ["耳机", "音箱"]
}
```

### 4.2 版本发布管理

#### 创建新版本发布
```http
POST /api/developer/projects/{projectId}/releases
```

**请求体**:
```json
{
  "version": "v1.3.0.0",
  "versionType": "发布版本",
  "title": "功能增强版本",
  "description": "详细的更新说明...",
  "deviceType": "耳机",
  "filePath": "/uploads/2024/01/15/firmware_v1.3.0.0.bin"
}
```

#### 更新版本信息
```http
PUT /api/developer/releases/{releaseId}
```

#### 删除版本
```http
DELETE /api/developer/releases/{releaseId}
```

### 4.3 文件上传

#### 上传文件
```http
POST /api/developer/upload
```

**Content-Type**: `multipart/form-data`

**表单字段**:
- `file`: 文件
- `projectId`: 项目ID

**响应示例**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "filePath": "/uploads/2024/01/15/firmware_v1.3.0.0.bin",
    "fileName": "firmware_v1.3.0.0.bin",
    "fileSize": "3.2MB",
    "fileHash": "sha256:def456...",
    "uploadTime": "2024-01-15T15:45:00Z"
  }
}
```

#### 获取上传进度
```http
GET /api/developer/upload/progress/{uploadId}
```

### 4.4 操作日志

#### 获取操作日志
```http
GET /api/developer/logs
```

**查询参数**:
- `projectId` (可选): 项目ID
- `action` (可选): 操作类型
- `startDate` (可选): 开始日期
- `endDate` (可选): 结束日期

### 4.5 批量操作

#### 批量删除版本
```http
POST /api/developer/releases/batch-delete
```

**请求体**:
```json
{
  "releaseIds": [1, 2, 3],
  "reason": "版本废弃",
  "force": false
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "批量操作完成",
  "data": {
    "total": 3,
    "succeeded": 2,
    "failed": 1,
    "results": [
      {
        "releaseId": 1,
        "status": "success",
        "message": "删除成功"
      },
      {
        "releaseId": 2,
        "status": "success", 
        "message": "删除成功"
      },
      {
        "releaseId": 3,
        "status": "failed",
        "message": "版本正在被下载，无法删除",
        "code": "VERSION_IN_USE"
      }
    ]
  }
}
```

#### 批量更新版本状态
```http
POST /api/developer/releases/batch-update-status
```

**请求体**:
```json
{
  "releaseIds": [1, 2, 3],
  "status": "deprecated",
  "reason": "功能升级"
}
```

#### 批量导出版本信息
```http
POST /api/developer/releases/batch-export
```

**请求体**:
```json
{
  "projectId": 1,
  "filters": {
    "versionType": "发布版本",
    "dateRange": "30d"
  },
  "format": "excel"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "导出任务已创建",
  "data": {
    "taskId": "export_123456",
    "estimatedTime": "2分钟",
    "downloadUrl": "/api/developer/export/download/export_123456",
    "status": "processing"
  }
}
```

## 5. Admin接口（超级管理员）

### 5.1 项目管理

#### 创建项目
```http
POST /api/admin/projects
```

**请求体**:
```json
{
  "name": "新项目名称",
  "description": "项目描述",
  "category": "firmware"
}
```

#### 删除项目
```http
DELETE /api/admin/projects/{projectId}
```

### 5.2 用户管理

#### 创建开发者账号
```http
POST /api/admin/users
```

**请求体**:
```json
{
  "username": "newdeveloper",
  "password": "initialPassword",
  "email": "<EMAIL>",
  "role": "developer"
}
```

#### 获取用户列表
```http
GET /api/admin/users
```

#### 更新用户信息
```http
PUT /api/admin/users/{userId}
```

### 5.3 权限管理

#### 分配项目权限
```http
POST /api/admin/permissions
```

**请求体**:
```json
{
  "userId": 2,
  "projectId": 1,
  "permissionType": "full"
}
```

#### 撤销项目权限
```http
DELETE /api/admin/permissions/{permissionId}
```

#### 获取用户权限列表
```http
GET /api/admin/users/{userId}/permissions
```

#### 批量分配权限
```http
POST /api/admin/permissions/batch-assign
```

**请求体**:
```json
{
  "assignments": [
    {
      "userId": 2,
      "projectId": 1,
      "permissionType": "full"
    },
    {
      "userId": 3,
      "projectId": 1,
      "permissionType": "readonly"
    },
    {
      "userId": 2,
      "projectId": 2,
      "permissionType": "full"
    }
  ],
  "notifyUsers": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "批量权限分配完成",
  "data": {
    "total": 3,
    "succeeded": 2,
    "failed": 1,
    "results": [
      {
        "userId": 2,
        "projectId": 1,
        "status": "success",
        "message": "权限分配成功"
      },
      {
        "userId": 3,
        "projectId": 1,
        "status": "success",
        "message": "权限分配成功"
      },
      {
        "userId": 2,
        "projectId": 2,
        "status": "failed",
        "message": "用户已有该项目权限",
        "code": "PERMISSION_EXISTS"
      }
    ]
  }
}
```

#### 批量撤销权限
```http
POST /api/admin/permissions/batch-revoke
```

**请求体**:
```json
{
  "permissionIds": [1, 2, 3],
  "reason": "项目重组",
  "notifyUsers": true
}
```

### 5.4 系统监控

#### 获取系统统计
```http
GET /api/admin/statistics
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalProjects": 15,
    "totalReleases": 89,
    "totalDownloads": 25600,
    "activeUsers": 12,
    "storageUsed": "1.2GB",
    "todayDownloads": 156
  }
}
```

#### 获取操作日志
```http
GET /api/admin/logs
```

## 6. 错误处理与异常机制

### 6.1 错误码体系

#### 6.1.1 系统级错误码 (1000-1999)
- `1000` - 系统内部错误
- `1001` - 数据库连接失败
- `1002` - 文件系统错误
- `1003` - 网络超时
- `1004` - 服务不可用

#### 6.1.2 认证授权错误码 (2000-2999)
- `2000` - 未认证
- `2001` - Token无效或过期
- `2002` - 权限不足
- `2003` - 账号被禁用
- `2004` - 登录失败次数过多

#### 6.1.3 业务逻辑错误码 (3000-3999)
- `3000` - 参数验证失败
- `3001` - 资源不存在
- `3002` - 资源已存在
- `3003` - 操作冲突
- `3004` - 业务规则限制

#### 6.1.4 文件操作错误码 (4000-4999)
- `4000` - 文件上传失败
- `4001` - 文件大小超限
- `4002` - 文件类型不支持
- `4003` - 文件已存在
- `4004` - 存储空间不足
- `4005` - 文件损坏或校验失败

### 6.2 异常处理机制

#### 6.2.1 网络异常处理
```json
{
  "code": 1003,
  "message": "网络请求超时",
  "data": {
    "retryable": true,
    "retryAfter": 5,
    "maxRetries": 3,
    "currentRetry": 1
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 6.2.2 文件冲突处理
```json
{
  "code": 3003,
  "message": "版本号已存在",
  "data": {
    "conflictType": "version_exists",
    "existingVersion": {
      "id": 123,
      "version": "v1.2.0.0",
      "publishedAt": "2024-01-01T00:00:00Z"
    },
    "suggestions": [
      "使用新的版本号",
      "更新现有版本"
    ]
  }
}
```

#### 6.2.3 权限异常处理
```json
{
  "code": 2002,
  "message": "无权限访问该项目",
  "data": {
    "requiredPermission": "project:write",
    "userPermissions": ["project:read"],
    "requestAccess": {
      "url": "/api/request-permission",
      "adminContact": "<EMAIL>"
    }
  }
}
```

#### 6.2.4 文件上传异常处理
```json
{
  "code": 4001,
  "message": "文件大小超出限制",
  "data": {
    "fileSize": "250MB",
    "maxSize": "200MB",
    "fileName": "firmware_v1.0.0.bin",
    "uploadId": "upload_123456",
    "resumable": false
  }
}
```

### 6.3 异常恢复策略

#### 6.3.1 文件上传异常恢复
```http
# 检查上传状态
GET /api/developer/upload/status/{uploadId}

# 响应示例
{
  "code": 200,
  "data": {
    "uploadId": "upload_123456",
    "status": "failed",
    "progress": 45,
    "failureReason": "network_timeout",
    "resumable": true,
    "resumeToken": "resume_token_abc123"
  }
}

# 恢复上传
POST /api/developer/upload/resume
{
  "uploadId": "upload_123456",
  "resumeToken": "resume_token_abc123",
  "startByte": 94371840
}
```

#### 6.3.2 事务回滚机制
```http
# 事务状态查询
GET /api/developer/transaction/{transactionId}

# 手动回滚
POST /api/developer/transaction/{transactionId}/rollback
```

### 6.4 输入验证规则

#### 6.4.1 版本号验证
```typescript
interface VersionValidation {
  pattern: "^v\\d+\\.\\d+\\.\\d+\\.\\d+$";
  example: "v1.0.0.0";
  errorMessage: "版本号格式必须为 v主版本.次版本.修订版.构建版";
}

// API验证失败响应
{
  "code": 3000,
  "message": "版本号格式不正确",
  "data": {
    "field": "version",
    "value": "1.0.0",
    "expectedFormat": "v1.0.0.0",
    "pattern": "^v\\d+\\.\\d+\\.\\d+\\.\\d+$"
  }
}
```

#### 6.4.2 文件类型和大小验证
```http
POST /api/developer/upload

# 验证失败响应
{
  "code": 4002,
  "message": "文件类型不支持",
  "data": {
    "fileName": "malicious.exe",
    "fileType": "application/x-msdownload",
    "allowedTypes": ["*"],
    "blockedTypes": ["application/x-msdownload", "application/javascript"],
    "securityCheck": "failed"
  }
}
```

#### 6.4.3 项目名称验证
```typescript
interface ProjectNameValidation {
  minLength: 2;
  maxLength: 50;
  pattern: "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_]+$";
  forbidden: ["admin", "system", "test"];
  errorMessage: "项目名称只能包含中文、英文、数字、空格、横线和下划线";
}
```

### 6.5 常见错误响应示例

#### 参数验证错误
```json
{
  "code": 3000,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "version",
        "value": "1.0.0",
        "message": "版本号格式不正确，应为v1.0.0.0格式",
        "code": "INVALID_VERSION_FORMAT"
      },
      {
        "field": "deviceType",
        "value": "",
        "message": "设备类型不能为空",
        "code": "REQUIRED_FIELD"
      }
    ],
    "validationRules": "/api/validation-rules"
  }
}
```

#### 并发冲突错误
```json
{
  "code": 3003,
  "message": "资源正在被其他用户编辑",
  "data": {
    "resourceType": "project",
    "resourceId": 123,
    "lockedBy": "developer01",
    "lockedAt": "2024-01-01T10:30:00Z",
    "lockExpiry": "2024-01-01T10:45:00Z",
    "forceUnlock": {
      "available": false,
      "reason": "需要管理员权限"
    }
  }
}
```

## 7. 接口版本控制

当前API版本: `v1`

版本控制策略:
- URL路径中包含版本号: `/api/v1/...`
- 向前兼容，新版本保留旧接口
- 废弃接口提前通知并设置过期时间

## 8. 接口限流

- **普通员工**: 无限制（内网环境）
- **开发者**: 上传接口 10次/分钟
- **Admin**: 管理接口 100次/分钟

## 9. 接口安全

### 9.1 认证安全
- JWT Token有效期: 24小时
- 支持Token刷新机制
- 敏感操作需要二次验证

### 9.2 数据安全
- 所有输入参数进行校验和过滤
- SQL注入防护
- XSS攻击防护
- 文件上传类型和大小限制

这套API设计涵盖了系统的所有功能需求，支持三种用户角色的不同操作，具有良好的扩展性和安全性。

import { defineStore } from 'pinia'
import { adminLogin, adminRefresh, adminProfile } from '@/api/adminAuth'
import type { Admin<PERSON>ser, AdminLoginResponse } from '@/types/domain'

interface AdminAuthState {
  token: string | null
  refreshToken: string | null
  expiresAt: number | null
  user: AdminUser | null
  _refreshTimer?: number | null
}

const STORAGE_KEY = 'admin_auth_v1'

function persist(state: Partial<AdminAuthState>) {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
  } catch {}
}

function restore(): Partial<AdminAuthState> {
  try {
    const raw = localStorage.getItem(STORAGE_KEY)
    if (!raw) return {}
    return JSON.parse(raw) as Partial<AdminAuthState>
  } catch {
    return {}
  }
}

export const useAdminAuthStore = defineStore('adminAuth', {
  state: (): AdminAuthState => ({
    token: null,
    refreshToken: null,
    expiresAt: null,
    user: null,
    _refreshTimer: null,
  }),
  
  getters: {
    isAuthenticated(state): boolean {
      if (!state.token || !state.user) return false
      if (state.expiresAt && Date.now() > state.expiresAt) return false
      return state.user.userType === 'admin'
    },
    
    userPermissions(state): string[] {
      return state.user?.permissions || []
    },
    
    isSuper(state): boolean {
      return state.user?.level === 'super'
    }
  },
  
  actions: {
    initialize(): void {
      const restored = restore()
      if (restored.token) this.token = restored.token
      if (restored.refreshToken) this.refreshToken = restored.refreshToken
      if (restored.expiresAt) this.expiresAt = restored.expiresAt
      if (restored.user) this.user = restored.user as AdminUser
      
      // 同步到 axios 拦截器
      if (this.token && this.user?.userType === 'admin') {
        localStorage.setItem('admin_token', this.token)
      }
      
      this.setupAutoRefresh()
    },
    
    async login(username: string, password: string): Promise<void> {
      const res = await adminLogin(username, password)
      const ttl = res.expiresIn ? res.expiresIn * 1000 : 2 * 60 * 60 * 1000
      
      this.token = res.token
      this.refreshToken = res.refreshToken || null
      this.expiresAt = Date.now() + ttl
      this.user = res.user
      
      // 存储到本地
      localStorage.setItem('admin_token', this.token)
      persist({ 
        token: this.token, 
        refreshToken: this.refreshToken, 
        expiresAt: this.expiresAt,
        user: this.user 
      })
      
      this.setupAutoRefresh()
    },
    
    async fetchProfile(): Promise<void> {
      const user = await adminProfile()
      this.user = user
      persist({ 
        user: this.user, 
        token: this.token, 
        refreshToken: this.refreshToken, 
        expiresAt: this.expiresAt 
      })
    },
    
    async refresh(): Promise<void> {
      if (!this.refreshToken) return
      
      const res = await adminRefresh(this.refreshToken)
      const ttl = res.expiresIn ? res.expiresIn * 1000 : 2 * 60 * 60 * 1000
      
      this.token = res.token
      this.refreshToken = res.refreshToken || this.refreshToken
      this.expiresAt = Date.now() + ttl
      
      localStorage.setItem('admin_token', this.token)
      persist({ 
        token: this.token, 
        refreshToken: this.refreshToken, 
        expiresAt: this.expiresAt, 
        user: this.user 
      })
      
      this.setupAutoRefresh()
    },
    
    logout(): void {
      this.token = null
      this.refreshToken = null
      this.expiresAt = null
      this.user = null
      
      localStorage.removeItem('admin_token')
      persist({})
      
      if (this._refreshTimer) {
        window.clearTimeout(this._refreshTimer)
        this._refreshTimer = null
      }
    },
    
    setupAutoRefresh(): void {
      if (!this.expiresAt) return
      
      if (this._refreshTimer) {
        window.clearTimeout(this._refreshTimer)
        this._refreshTimer = null
      }
      
      const msLeft = this.expiresAt - Date.now()
      // 在过期前5分钟刷新，最少提前30秒
      const refreshIn = Math.max(msLeft - 5 * 60 * 1000, 30 * 1000)
      
      this._refreshTimer = window.setTimeout(() => {
        this.refresh().catch(() => {})
      }, refreshIn)
    },
    
    hasPermission(permission: string): boolean {
      return this.userPermissions.includes(permission)
    },
    
    canManageUsers(): boolean {
      return this.hasPermission('manage_users') || this.isSuper
    },
    
    canManageProjects(): boolean {
      return this.hasPermission('manage_projects') || this.isSuper
    },
    
    canManageSystem(): boolean {
      return this.hasPermission('manage_system') || this.isSuper
    }
  },
})

import { request } from '@/api/http'
import type { SearchPageResult, SearchResultItem } from '@/types/domain'

export interface SearchParams {
  q: string
  type?: string
  versionType?: 'stable' | 'beta' | 'alpha' | ''
  device?: string
  page?: number
  pageSize?: number
}

export function searchAll(params: SearchParams) {
  return request<SearchPageResult<SearchResultItem>>({
    url: '/search',
    method: 'GET',
    params,
    retry: { times: 1 },
    cache: { enabled: true, ttlMs: 30_000, tags: ['search:list'] },
  })
}



<template>
  <!-- 全页面加载 -->
  <PageLoading
    :visible="globalLoading"
    :title="loadingTitle"
    :description="loadingDescription"
    :show-progress="showProgress"
    :progress="globalProgress"
  />
  
  <!-- 顶部进度条 -->
  <TopProgress
    :visible="topProgressVisible"
    :progress="topProgress"
    :color="topProgressColor"
  />
</template>

<script setup lang="ts">
import { useLoading } from '@/composables/useLoading'
import PageLoading from '@base/feedback/PageLoading.vue'
import TopProgress from '@base/feedback/TopProgress.vue'

// 使用全局加载状态
const {
  globalLoading,
  globalProgress,
  loadingTitle,
  loadingDescription,
  showProgress,
  topProgressVisible,
  topProgress,
  topProgressColor
} = useLoading()
</script>

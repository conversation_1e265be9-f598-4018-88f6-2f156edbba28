/* Utility classes */

/* Spacing (m-*, p-*) */
.m-0 { margin: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.ml-2 { margin-left: var(--space-2) !important; }
.mr-2 { margin-right: var(--space-2) !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }

/* Flex */
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.items-center { align-items: center !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-3 { gap: var(--space-3) !important; }

/* Text */
.text-sm { font-size: var(--font-size-sm) !important; }
.text-md { font-size: var(--font-size-md) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-muted { color: var(--color-text-3) !important; }
.text-primary { color: var(--color-primary) !important; }

/* Radius */
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-full { border-radius: var(--radius-round) !important; }

/* Shadow */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* Grid System */
.container { width: 100%; margin: 0 auto; padding-left: var(--space-4); padding-right: var(--space-4); }
.row { display: flex; flex-wrap: wrap; margin-left: calc(var(--space-2) * -1); margin-right: calc(var(--space-2) * -1); }
[class^="col-"] { padding-left: var(--space-2); padding-right: var(--space-2); box-sizing: border-box; }
.col-1 { width: 8.3333%; }
.col-2 { width: 16.6667%; }
.col-3 { width: 25%; }
.col-4 { width: 33.3333%; }
.col-5 { width: 41.6667%; }
.col-6 { width: 50%; }
.col-7 { width: 58.3333%; }
.col-8 { width: 66.6667%; }
.col-9 { width: 75%; }
.col-10 { width: 83.3333%; }
.col-11 { width: 91.6667%; }
.col-12 { width: 100%; }



import { SimpleCache } from '@/utils/cache'

export interface ApiCacheOptions {
  ttlMs?: number
  enabled?: boolean
}

export class ApiCache<TValue> {
  private readonly cache: SimpleCache<TValue>
  private readonly enabled: boolean

  constructor(options?: ApiCacheOptions) {
    this.cache = new SimpleCache<TValue>({ ttlMs: options?.ttlMs ?? 30_000 })
    this.enabled = options?.enabled ?? true
  }

  buildKey(url?: string, params?: unknown): string {
    return JSON.stringify({ url, params })
  }

  get(key: string): TValue | undefined {
    if (!this.enabled) return undefined
    return this.cache.get(key)
  }

  set(key: string, value: TValue): void {
    if (!this.enabled) return
    this.cache.set(key, value)
  }

  delete(key: string): void {
    // SimpleCache 未公开 delete，为兼容保留空实现
    // 将该键设置为立即过期的空值，达到删除效果
    this.cache.set(key, undefined as unknown as TValue, 1)
  }

  clear(): void {
    this.cache.clear()
  }
}



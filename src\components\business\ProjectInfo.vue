<template>
  <div class="info-card">
    <h3 class="card-title">项目信息</h3>
    <div class="info-list">
      <div 
        v-for="item in infoItems" 
        :key="item.label" 
        class="info-item"
      >
        <span class="info-label">{{ item.label }}</span>
        <span class="info-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface InfoItem {
  label: string
  value: string
}

interface Props {
  infoItems: InfoItem[]
}

defineProps<Props>()
</script>

<style scoped>
.info-card {
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-4) 0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
}

.info-value {
  color: var(--color-text-1);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}
</style>

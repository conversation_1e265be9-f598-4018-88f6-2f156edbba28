/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActivityHistory: typeof import('./components/business/ActivityHistory.vue')['default']
    AdminOverview: typeof import('./components/admin/AdminOverview.vue')['default']
    Avatar: typeof import('./components/base/atoms/Avatar.vue')['default']
    AvatarUploadForm: typeof import('./components/business/AvatarUploadForm.vue')['default']
    Badge: typeof import('./components/base/atoms/Badge.vue')['default']
    BaseButton: typeof import('./components/base/atoms/BaseButton.vue')['default']
    BaseCheckbox: typeof import('./components/base/form/BaseCheckbox.vue')['default']
    BaseDatePicker: typeof import('./components/base/form/BaseDatePicker.vue')['default']
    BaseDrawer: typeof import('./components/base/feedback/BaseDrawer.vue')['default']
    BaseIcon: typeof import('./components/base/atoms/BaseIcon.vue')['default']
    BaseInput: typeof import('./components/base/form/BaseInput.vue')['default']
    BaseList: typeof import('./components/base/data/BaseList.vue')['default']
    BaseModal: typeof import('./components/base/feedback/BaseModal.vue')['default']
    BaseProgress: typeof import('./components/base/feedback/BaseProgress.vue')['default']
    BaseRadio: typeof import('./components/base/form/BaseRadio.vue')['default']
    BaseSelect: typeof import('./components/base/form/BaseSelect.vue')['default']
    BaseSpin: typeof import('./components/base/feedback/BaseSpin.vue')['default']
    BaseTooltip: typeof import('./components/base/feedback/BaseTooltip.vue')['default']
    BaseUpload: typeof import('./components/base/form/BaseUpload.vue')['default']
    Breadcrumb: typeof import('./components/base/navigation/Breadcrumb.vue')['default']
    Card: typeof import('./components/base/data/Card.vue')['default']
    ChangePasswordForm: typeof import('./components/business/ChangePasswordForm.vue')['default']
    Container: typeof import('./components/layout/Container.vue')['default']
    CtaSection: typeof import('./components/business/home/<USER>')['default']
    CTASection: typeof import('./components/sections/CTASection.vue')['default']
    DeveloperAccountManager: typeof import('./components/admin/DeveloperAccountManager.vue')['default']
    DeveloperOverview: typeof import('./components/developer/DeveloperOverview.vue')['default']
    DeviceManagement: typeof import('./components/business/DeviceManagement.vue')['default']
    DownloadButton: typeof import('./components/base/file/DownloadButton.vue')['default']
    DownloadsPanel: typeof import('./components/base/feedback/DownloadsPanel.vue')['default']
    EditProfileForm: typeof import('./components/business/EditProfileForm.vue')['default']
    Empty: typeof import('./components/base/data/Empty.vue')['default']
    ErrorBoundary: typeof import('./components/common/ErrorBoundary.vue')['default']
    FeatureCard: typeof import('./components/business/FeatureCard.vue')['default']
    FeaturesSection: typeof import('./components/business/home/<USER>')['default']
    FileIcon: typeof import('./components/base/file/FileIcon.vue')['default']
    FilePreview: typeof import('./components/base/file/FilePreview.vue')['default']
    FileSize: typeof import('./components/base/file/FileSize.vue')['default']
    Flex: typeof import('./components/layout/Flex.vue')['default']
    GlobalLoading: typeof import('./components/app/GlobalLoading.vue')['default']
    Grid: typeof import('./components/layout/Grid.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    HeroSection: typeof import('./components/business/home/<USER>')['default']
    InfoCard: typeof import('./components/business/project/InfoCard.vue')['default']
    LazyImage: typeof import('./components/common/LazyImage.vue')['default']
    Menu: typeof import('./components/base/navigation/Menu.vue')['default']
    OnlineStatus: typeof import('./components/base/user/OnlineStatus.vue')['default']
    PageHeader: typeof import('./components/business/projects/PageHeader.vue')['default']
    PageLoading: typeof import('./components/base/feedback/PageLoading.vue')['default']
    Pagination: typeof import('./components/base/navigation/Pagination.vue')['default']
    PermissionButton: typeof import('./components/auth/PermissionButton.vue')['default']
    PermissionGuard: typeof import('./components/auth/PermissionGuard.vue')['default']
    PermissionLink: typeof import('./components/auth/PermissionLink.vue')['default']
    PermissionManager: typeof import('./components/admin/PermissionManager.vue')['default']
    PermissionWrapper: typeof import('./components/base/PermissionWrapper.vue')['default']
    ProjectCard: typeof import('./components/business/projects/ProjectCard.vue')['default']
    ProjectContentManager: typeof import('./components/developer/ProjectContentManager.vue')['default']
    ProjectHeader: typeof import('./components/business/ProjectHeader.vue')['default']
    ProjectHelp: typeof import('./components/business/ProjectHelp.vue')['default']
    ProjectInfo: typeof import('./components/business/ProjectInfo.vue')['default']
    ProjectManager: typeof import('./components/admin/ProjectManager.vue')['default']
    ProjectsGrid: typeof import('./components/business/projects/ProjectsGrid.vue')['default']
    ProjectsSection: typeof import('./components/sections/ProjectsSection.vue')['default']
    ProjectStats: typeof import('./components/business/ProjectStats.vue')['default']
    PublicBreadcrumb: typeof import('./components/layout/PublicBreadcrumb.vue')['default']
    PublicFooter: typeof import('./components/layout/PublicFooter.vue')['default']
    PublicHeader: typeof import('./components/layout/PublicHeader.vue')['default']
    PublicLayout: typeof import('./components/layout/PublicLayout.vue')['default']
    RecentReleasesSection: typeof import('./components/business/home/<USER>')['default']
    ReleaseCard: typeof import('./components/business/ReleaseCard.vue')['default']
    ReleaseManager: typeof import('./components/developer/ReleaseManager.vue')['default']
    ResponsiveContainer: typeof import('./components/layout/ResponsiveContainer.vue')['default']
    ResponsiveGrid: typeof import('./components/layout/ResponsiveGrid.vue')['default']
    ResponsiveSection: typeof import('./components/layout/ResponsiveSection.vue')['default']
    Result: typeof import('./components/base/feedback/Result.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SEOHead: typeof import('./components/common/SEOHead.vue')['default']
    SessionStatus: typeof import('./components/app/SessionStatus.vue')['default']
    Skeleton: typeof import('./components/base/feedback/Skeleton.vue')['default']
    StackLayout: typeof import('./components/layout/StackLayout.vue')['default']
    StatCard: typeof import('./components/cards/StatCard.vue')['default']
    Steps: typeof import('./components/base/navigation/Steps.vue')['default']
    Table: typeof import('./components/base/data/Table.vue')['default']
    Tabs: typeof import('./components/base/navigation/Tabs.vue')['default']
    Tag: typeof import('./components/base/atoms/Tag.vue')['default']
    TopProgress: typeof import('./components/base/feedback/TopProgress.vue')['default']
    TopProgressBar: typeof import('./components/common/TopProgressBar.vue')['default']
    UserInfo: typeof import('./components/base/user/UserInfo.vue')['default']
    UserProfile: typeof import('./components/business/UserProfile.vue')['default']
    UserRole: typeof import('./components/base/user/UserRole.vue')['default']
    VersionItem: typeof import('./components/business/VersionItem.vue')['default']
    VersionList: typeof import('./components/business/VersionList.vue')['default']
    VersionManager: typeof import('./components/developer/VersionManager.vue')['default']
    VersionsList: typeof import('./components/business/project/VersionsList.vue')['default']
  }
}

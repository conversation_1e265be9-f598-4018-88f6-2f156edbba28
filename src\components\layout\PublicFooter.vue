<template>
  <footer class="public-footer accent-top">
    <div class="footer-container container-xl">
      <!-- 主要内容区域 -->
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-section footer-brand">
          <div class="brand-info">
            <div class="brand-logo">
              <BaseIcon :name="'app-logo' as any" size="32" />
              <h3 class="brand-name">软件发布系统</h3>
            </div>
            <p class="brand-description">
              安全、高效的软件版本管理和发布平台，为团队协作提供专业的解决方案。
            </p>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="footer-section">
          <h4 class="section-title">快速链接</h4>
          <ul class="footer-links">
            <li><RouterLink to="/" class="footer-link">首页</RouterLink></li>
            <li><RouterLink to="/projects" class="footer-link">项目列表</RouterLink></li>
            <li><RouterLink to="/search" class="footer-link">搜索</RouterLink></li>
          </ul>
        </div>

        <!-- 开发者专区 -->
        <div class="footer-section">
          <h4 class="section-title">开发者</h4>
          <ul class="footer-links">
            <li><RouterLink to="/login" class="footer-link">开发者登录</RouterLink></li>
            <li><RouterLink to="/developer" class="footer-link">开发者中心</RouterLink></li>
          </ul>
        </div>

        
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import BaseIcon from '@base/atoms/BaseIcon.vue'
</script>

<style scoped>
.public-footer {
  background: var(--bg-2);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.footer-container {
  padding: 0;
}

/* 主要内容区域 */
.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: var(--spacing-12);
  padding: var(--spacing-12) 0;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* 品牌信息 */
.footer-brand { max-width: 360px; }

.brand-info { display: flex; flex-direction: column; gap: var(--spacing-3); }

.brand-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.brand-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.brand-description {
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* 章节标题 */
.section-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

/* 链接列表 */
.footer-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.footer-link {
  color: var(--color-text-2);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-base);
}

.footer-link:hover { color: var(--color-primary); }

/* 底部信息栏 */
/* 删除底部信息栏相关样式 */

/* 响应式设计 */
@media (max-width: 1024px) {
  .footer-content { grid-template-columns: 1fr 1fr; gap: var(--spacing-8); }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    padding: var(--spacing-8) 0;
  }
  .footer-brand { max-width: none; }
  .footer-bottom-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  .footer-meta { width: 100%; justify-content: space-between; }
}

@media (max-width: 480px) {
  .footer-content { padding: var(--spacing-6) 0; gap: var(--spacing-4); }
  .brand-logo { flex-direction: column; align-items: flex-start; gap: var(--spacing-2); }
  .social-links { gap: var(--spacing-1); }
  .social-link { width: 32px; height: 32px; }
  .footer-meta { flex-direction: column; align-items: flex-start; gap: var(--spacing-2); }
}

/* 深色主题适配 */
[data-theme="dark"] .public-footer {
  background: var(--bg-1);
  border-top-color: var(--border-color);
}

[data-theme="dark"] .social-link { background: var(--bg-2); }
[data-theme="dark"] .social-link:hover { background: var(--color-primary); }
</style>

<template>
  <div class="downloads-panel" v-if="visible">
    <div class="panel-header">
      <div class="title">下载管理</div>
      <button class="btn btn-sm" @click="$emit('close')">关闭</button>
    </div>
    <div class="panel-body">
      <div v-if="tasks.length === 0" class="empty">暂无下载任务</div>
      <ul class="task-list">
        <li v-for="t in tasks" :key="t.id" class="task-item">
          <div class="task-main">
            <div class="name">{{ t.filename }}</div>
            <div class="meta">
              <span>{{ formatBytes(t.receivedBytes) }} / {{ formatBytes(t.size) }}</span>
              <span v-if="t.speedBps">速度：{{ formatBytes(t.speedBps) }}/s</span>
              <span v-if="t.etaSeconds">剩余：~{{ t.etaSeconds }}s</span>
              <span>状态：{{ mapStatus(t.status) }}</span>
            </div>
            <div class="progress">
              <div class="bar" :style="{ width: percent(t) + '%' }"></div>
            </div>
            <div v-if="t.error" class="error">{{ t.error }}</div>
          </div>
          <div class="task-actions">
            <button class="btn btn-sm" v-if="t.status==='downloading'" @click="pause(t.id)">暂停</button>
            <button class="btn btn-sm" v-else-if="t.status==='paused'" @click="resume(t.id)">继续</button>
            <button class="btn btn-sm" v-else-if="t.status==='failed'" @click="retry(t.id)">重试</button>
            <button class="btn btn-sm" v-if="t.status!=='completed'" @click="cancel(t.id)">取消</button>
          </div>
        </li>
      </ul>
    </div>
    <div class="panel-footer">
      <button class="btn btn-sm" @click="clearCompleted">清除已完成</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useDownloadsStore } from '@/stores/downloads'

interface Props { visible?: boolean }
const props = defineProps<Props>()
const downloads = useDownloadsStore()
const tasks = computed(() => downloads.tasks)

function percent(t: any) {
  if (!t.size) return 0
  return Math.max(0, Math.min(100, Math.floor((t.receivedBytes / t.size) * 100)))
}
function pause(id: string) { downloads.pause(id) }
function resume(id: string) { downloads.start(id) }
function retry(id: string) { downloads.retry(id) }
function cancel(id: string) { downloads.cancel(id) }
function clearCompleted() { downloads.clearCompleted() }
function formatBytes(n?: number) {
  if (!n) return '0 B'
  if (n < 1024) return `${n} B`
  if (n < 1024*1024) return `${(n/1024).toFixed(1)} KB`
  return `${(n/1024/1024).toFixed(1)} MB`
}
function mapStatus(s: string) {
  if (s==='queued') return '队列中'
  if (s==='downloading') return '下载中'
  if (s==='paused') return '已暂停'
  if (s==='completed') return '已完成'
  if (s==='failed') return '失败'
  if (s==='cancelled') return '已取消'
  return '—'
}
</script>

<style scoped>
.downloads-panel { position: fixed; right: 16px; bottom: 16px; width: 360px; background: var(--bg-1); border: 1px solid var(--border-color); border-radius: var(--radius-lg); box-shadow: var(--shadow-lg); overflow: hidden; z-index: 1000; }
.panel-header { display: flex; align-items: center; justify-content: space-between; padding: var(--spacing-3) var(--spacing-4); border-bottom: 1px solid var(--border-color); }
.panel-body { max-height: 420px; overflow: auto; padding: var(--spacing-3) var(--spacing-4); }
.panel-footer { padding: var(--spacing-3) var(--spacing-4); border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end; }
.title { font-weight: var(--font-weight-semibold); }
.empty { color: var(--color-text-3); text-align: center; padding: var(--spacing-6) 0; }
.task-list { list-style: none; padding: 0; margin: 0; display: flex; flex-direction: column; gap: var(--spacing-3); }
.task-item { display: grid; grid-template-columns: 1fr auto; gap: var(--spacing-2); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-3); }
.name { font-weight: var(--font-weight-medium); }
.meta { color: var(--color-text-3); font-size: var(--font-size-xs); display: flex; gap: var(--spacing-2); flex-wrap: wrap; }
.progress { height: 6px; background: var(--bg-3); border-radius: var(--radius-full); overflow: hidden; margin-top: 6px; }
.bar { height: 100%; background: var(--color-primary); }
.error { color: var(--color-danger, #dc2626); font-size: var(--font-size-xs); margin-top: 4px; }
.btn { border: 1px solid var(--border-color); background: var(--bg-1); border-radius: var(--radius-sm); padding: 2px 8px; cursor: pointer; }
.btn-sm { font-size: var(--font-size-xs); }
@media (max-width: 480px) { .downloads-panel { right: 8px; left: 8px; width: auto; } }
</style>



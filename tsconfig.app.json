{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "types": ["vite/client"],
    "paths": {
      "@/*": ["src/*"],
      "@base/*": ["src/components/base/*"]
    }
  },
  "include": [
    "env.d.ts",
    "src/env.d.ts",
    "src/vite-env.d.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/*.d.ts",
    "src/**/*.d.ts"
  ]
}

<template>
  <div class="version-item">
    <div class="version-info">
      <h3 class="version-number">{{ version.number }}</h3>
      <p class="version-description">{{ version.description }}</p>
      <div class="version-meta">
        <span class="version-date">{{ version.date }}</span>
        <span class="version-size">{{ version.size }}</span>
        <span class="version-type">{{ version.type }}</span>
      </div>
    </div>
    <div class="version-actions">
      <DownloadButton 
        v-if="version.downloadUrl"
        :url="version.downloadUrl"
        :filename="version.filename"
        :size="version.sizeBytes"
        class="btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import DownloadButton from '@/components/base/file/DownloadButton.vue'

interface Version {
  id: string
  number: string
  description: string
  date: string
  size: string
  type: string
  downloadUrl?: string
  filename?: string
  sizeBytes?: number
}

interface Props {
  version: Version
}

defineProps<Props>()

defineEmits<{
  download: [version: Version]
}>()

 
</script>

<style scoped>
.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--bg-1);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.version-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.version-number {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0 0 var(--spacing-1) 0;
}

.version-description {
  color: var(--color-text-2);
  margin: 0 0 var(--spacing-2) 0;
}

.version-meta {
  display: flex;
  gap: var(--spacing-3);
}

.version-date,
.version-size,
.version-type {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.version-actions {
  display: flex;
  gap: var(--spacing-2);
}

 

.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-secondary {
  background: var(--bg-2);
  color: var(--color-text-2);
}

.btn-secondary:hover {
  background: var(--bg-3);
  color: var(--color-text-1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .version-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .version-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>

<template>
  <div class="version-manager">
    <div class="section-header">
      <h2 class="section-title">版本历史管理</h2>
    </div>

    <!-- 项目选择 -->
    <div class="project-selector">
      <select v-model="selectedProjectId" class="project-select" @change="loadVersions">
        <option value="">选择项目查看版本</option>
        <option 
          v-for="project in myProjects" 
          :key="project.id"
          :value="project.id"
        >
          {{ project.name }} ({{ getProjectTypeLabel(project.type) }})
        </option>
      </select>
    </div>

    <!-- 版本列表 -->
    <div v-if="selectedProjectId" class="versions-section">
      <div class="versions-header">
        <h3>{{ currentProjectName }} 的版本历史</h3>
        <div class="versions-stats">
          <span class="stat-item">总计 {{ versions.length }} 个版本</span>
        </div>
      </div>

      <div v-if="loading" class="loading-state">
        <p>正在加载版本...</p>
      </div>
      <div v-else-if="versions.length === 0" class="empty-state">
        <p>该项目暂无版本历史</p>
      </div>
      <div v-else class="versions-list">
        <div 
          v-for="version in versions" 
          :key="version.id"
          class="version-item card"
        >
          <div class="version-header">
            <div class="version-info">
              <h4 class="version-number">{{ version.version }}</h4>
              <span :class="['version-type', version.type]">
                {{ getVersionTypeLabel(version.type) }}
              </span>
            </div>
            <div class="version-meta">
              <span class="version-size">{{ formatFileSize(version.fileSize) }}</span>
              <span class="version-date">{{ formatTime(version.createdAt) }}</span>
            </div>
          </div>

          <div class="version-description" v-if="version.description">
            <p>{{ version.description }}</p>
          </div>

          <div class="version-details">
            <div class="details-grid">
              <div class="detail-item">
                <span class="detail-label">文件名</span>
                <span class="detail-value">{{ version.fileName }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">适用设备</span>
                <span class="detail-value">{{ version.devices.join(', ') }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">最后修改</span>
                <span class="detail-value">{{ formatTime(version.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <div class="version-actions">
            <button 
              class="btn btn-sm btn-outline" 
              @click="editVersion(version)"
            >
              编辑信息
            </button>
            <button 
              class="btn btn-sm btn-outline" 
              @click="downloadVersion(version)"
            >
              下载文件
            </button>
            <button 
              class="btn btn-sm btn-outline btn-danger" 
              @click="deleteVersion(version)"
            >
              删除版本
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑版本信息弹窗 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑版本信息</h3>
          <button class="modal-close" @click="closeEditModal">×</button>
        </div>
        
        <form class="version-form" @submit.prevent="saveVersionInfo">
          <!-- 版本基本信息 -->
          <div class="form-section">
            <h4 class="form-section-title">版本信息</h4>
            
            <div class="form-group">
              <label class="form-label">版本号 *</label>
              <input
                v-model="versionForm.version"
                type="text"
                class="form-input"
                placeholder="例如: v1.0.0"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">版本类型 *</label>
              <select v-model="versionForm.type" class="form-select" required>
                <option value="">请选择版本类型</option>
                <option value="development">开发版</option>
                <option value="beta">测试版</option>
                <option value="stable">稳定版</option>
                <option value="release">发布版</option>
              </select>
            </div>


            <div class="form-group">
              <label class="form-label">版本描述</label>
              <textarea
                v-model="versionForm.description"
                class="form-textarea"
                placeholder="请输入版本描述和更新内容..."
                rows="4"
              ></textarea>
            </div>
          </div>

          <!-- 设备兼容性 -->
          <div class="form-section">
            <h4 class="form-section-title">设备兼容性</h4>
            
            <div class="form-group">
              <label class="form-label">适用设备</label>
              <div class="devices-selection">
                <div class="devices-input">
                  <input
                    v-model="deviceInput"
                    type="text"
                    class="form-input"
                    placeholder="输入设备型号，按回车添加"
                    @keydown.enter.prevent="addDevice"
                  />
                  <button type="button" class="btn btn-sm btn-outline" @click="addDevice">
                    添加
                  </button>
                </div>
                <div class="devices-list" v-if="versionForm.devices.length > 0">
                  <div 
                    v-for="(device, index) in versionForm.devices" 
                    :key="index"
                    class="device-tag"
                  >
                    {{ device }}
                    <button 
                      type="button" 
                      class="remove-device"
                      @click="removeDevice(index)"
                    >
                      ×
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="formError" class="error-message">
            {{ formError }}
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeEditModal">
              取消
            </button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? '保存中...' : '保存更改' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="showDeleteModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="modal-close" @click="showDeleteModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除版本 <strong>{{ deletingVersion?.version }}</strong> 吗？</p>
          <p class="warning-text">此操作将永久删除版本文件，且不可恢复！</p>
        </div>
        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" @click="showDeleteModal = false">取消</button>
          <button type="button" class="btn btn-danger" @click="confirmDelete" :disabled="deleting">
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

interface Project {
  id: number
  name: string
  type: 'firmware' | 'app' | 'library'
}

interface Version {
  id: number
  projectId: number
  version: string
  type: 'development' | 'beta' | 'stable' | 'release'
  fileName: string
  fileSize: number
  description?: string
  devices: string[]
  createdAt: number
  updatedAt: number
}

interface VersionForm {
  version: string
  type: '' | 'development' | 'beta' | 'stable' | 'release'
  description?: string
  devices: string[]
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const myProjects = ref<Project[]>([])
const versions = ref<Version[]>([])
const selectedProjectId = ref<number | string>('')
const deviceInput = ref('')

// 弹窗状态
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const editingVersion = ref<Version | null>(null)
const deletingVersion = ref<Version | null>(null)

// 表单数据
const versionForm = reactive<VersionForm>({
  version: '',
  type: '',
  description: '',
  devices: []
})

const formError = ref('')

// 计算属性
const currentProjectName = computed(() => {
  const project = myProjects.value.find(p => p.id === selectedProjectId.value)
  return project?.name || ''
})

// 项目类型标签
function getProjectTypeLabel(type: string) {
  const typeMap: Record<string, string> = {
    'firmware': '固件',
    'app': '应用程序',
    'library': '库文件'
  }
  return typeMap[type] || type
}

// 版本类型标签
function getVersionTypeLabel(type: string) {
  const typeMap: Record<string, string> = {
    'development': '开发版',
    'beta': '测试版',
    'stable': '稳定版',
    'release': '发布版'
  }
  return typeMap[type] || type
}


// 格式化文件大小
function formatFileSize(bytes: number) {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return Math.round(size * 10) / 10 + units[unitIndex]
}

// 时间格式化
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载我的项目
async function loadMyProjects() {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    myProjects.value = [
      { id: 1, name: '智能家居控制系统', type: 'firmware' },
      { id: 2, name: '传感器数据采集', type: 'firmware' },
      { id: 3, name: '设备通信协议库', type: 'library' },
      { id: 4, name: '配置管理工具', type: 'app' }
    ]
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 加载版本列表
async function loadVersions() {
  if (!selectedProjectId.value) {
    versions.value = []
    return
  }
  
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟不同项目的版本数据
    if (selectedProjectId.value === 1) {
      versions.value = [
        {
          id: 1,
          projectId: 1,
          version: 'v2.1.0',
          type: 'stable',
          fileName: 'smart_home_v2.1.0.bin',
          fileSize: 1024 * 512, // 512KB
          description: '新增蓝牙连接功能，修复已知bug',
          devices: ['ESP32', 'ESP8266'],
          createdAt: Date.now() - 86400000 * 2,
          updatedAt: Date.now() - 86400000 * 1
        },
        {
          id: 2,
          projectId: 1,
          version: 'v2.0.1',
          type: 'stable',
          fileName: 'smart_home_v2.0.1.bin',
          fileSize: 1024 * 480,
          description: '修复连接稳定性问题',
          devices: ['ESP32'],
          createdAt: Date.now() - 86400000 * 15,
          updatedAt: Date.now() - 86400000 * 15
        },
        {
          id: 3,
          projectId: 1,
          version: 'v2.0.0',
          type: 'release',
          fileName: 'smart_home_v2.0.0.bin',
          fileSize: 1024 * 450,
          description: '重大版本更新，重构架构',
          devices: ['ESP32', 'ESP8266'],
          createdAt: Date.now() - 86400000 * 30,
          updatedAt: Date.now() - 86400000 * 30
        },
        {
          id: 4,
          projectId: 1,
          version: 'v1.9.8',
          type: 'stable',
          fileName: 'smart_home_v1.9.8.bin',
          fileSize: 1024 * 400,
          description: '最后的v1.x版本，已归档',
          devices: ['ESP8266'],
          createdAt: Date.now() - 86400000 * 60,
          updatedAt: Date.now() - 86400000 * 45
        }
      ]
    } else {
      versions.value = []
    }
  } catch (error) {
    console.error('加载版本列表失败:', error)
  } finally {
    loading.value = false
  }
}


// 编辑版本
function editVersion(version: Version) {
  editingVersion.value = version
  Object.assign(versionForm, {
    version: version.version,
    type: version.type,
    description: version.description || '',
    devices: [...version.devices]
  })
  deviceInput.value = ''
  showEditModal.value = true
}

// 下载版本
function downloadVersion(version: Version) {
  // 模拟下载
  const link = document.createElement('a')
  link.href = `/api/versions/${version.id}/download`
  link.download = version.fileName
  link.click()
}

// 删除版本
function deleteVersion(version: Version) {
  deletingVersion.value = version
  showDeleteModal.value = true
}

// 确认删除
async function confirmDelete() {
  if (!deletingVersion.value) return
  
  deleting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const index = versions.value.findIndex(v => v.id === deletingVersion.value!.id)
    if (index > -1) {
      versions.value.splice(index, 1)
    }
    
    showDeleteModal.value = false
    deletingVersion.value = null
  } catch (error) {
    console.error('删除失败:', error)
  } finally {
    deleting.value = false
  }
}

// 添加设备
function addDevice() {
  const device = deviceInput.value.trim()
  if (device && !versionForm.devices.includes(device)) {
    versionForm.devices.push(device)
    deviceInput.value = ''
  }
}

// 移除设备
function removeDevice(index: number) {
  versionForm.devices.splice(index, 1)
}

// 关闭编辑弹窗
function closeEditModal() {
  showEditModal.value = false
  editingVersion.value = null
  formError.value = ''
  deviceInput.value = ''
  
  Object.assign(versionForm, {
    version: '',
    type: '',
    description: '',
    devices: []
  })
}

// 保存版本信息
async function saveVersionInfo() {
  formError.value = ''
  
  // 基础验证
  if (!versionForm.version || !versionForm.type) {
    formError.value = '请填写必填项'
    return
  }
  
  if (!editingVersion.value) {
    formError.value = '无法找到要编辑的版本'
    return
  }
  
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新版本信息
    Object.assign(editingVersion.value, {
      version: versionForm.version,
      type: versionForm.type,
      description: versionForm.description,
      devices: [...versionForm.devices],
      updatedAt: Date.now()
    })
    
    closeEditModal()
  } catch (error) {
    console.error('保存失败:', error)
    formError.value = '保存失败，请重试'
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(() => {
  loadMyProjects()
})
</script>

<style scoped>
.version-manager {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 项目选择 */
.project-selector {
  margin-bottom: var(--spacing-6);
}

.project-select {
  width: 100%;
  max-width: 400px;
  height: 48px;
  padding: 0 var(--spacing-4);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

.project-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* 版本列表 */
.versions-section {
  margin-bottom: var(--spacing-8);
}

.versions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.versions-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
}

.versions-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-item {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.loading-state,
.empty-state {
  text-align: center;
  color: var(--color-text-3);
  padding: var(--spacing-8);
  font-size: var(--font-size-lg);
}

.versions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.version-item {
  padding: var(--spacing-5);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.version-item:hover {
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-sm);
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.version-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.version-number {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-1);
  margin: 0;
}

.version-type {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.version-type.development {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.version-type.beta {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.version-type.stable {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.version-type.release {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}


.version-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

.version-size {
  font-weight: var(--font-weight-medium);
}

.version-description {
  margin-bottom: var(--spacing-4);
}

.version-description p {
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.version-details {
  margin-bottom: var(--spacing-4);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.detail-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-3);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.detail-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-1);
  font-weight: var(--font-weight-medium);
}


.version-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-content {
  background: var(--bg-1);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 700px;
  margin: var(--spacing-4);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--bg-1);
  z-index: 1;
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-3);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-5);
}

.warning-text {
  color: var(--color-danger-dark);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-2);
}

.version-form {
  padding: var(--spacing-5);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-section-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-1);
  margin: 0;
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
}

.form-input {
  height: 40px;
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* 设备选择 */
.devices-selection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.devices-input {
  display: flex;
  gap: var(--spacing-2);
}

.devices-input .form-input {
  flex: 1;
}

.devices-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.device-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.remove-device {
  background: none;
  border: none;
  color: var(--color-primary-dark);
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
  position: sticky;
  bottom: 0;
  background: var(--bg-1);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .project-select {
    max-width: none;
  }
  
  .versions-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .versions-stats {
    width: 100%;
    justify-content: space-between;
  }
  
  .version-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .version-meta {
    align-items: flex-start;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .version-actions {
    width: 100%;
  }
  
  .modal-content {
    margin: var(--spacing-2);
    max-height: 95vh;
  }
  
  .version-form {
    padding: var(--spacing-4);
  }
  
  .devices-input {
    flex-direction: column;
  }
}
</style>

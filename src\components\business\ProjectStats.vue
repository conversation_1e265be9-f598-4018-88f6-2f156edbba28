<template>
  <div class="project-stats">
    <div 
      v-for="stat in stats" 
      :key="stat.label" 
      class="stat-item"
    >
      <span class="stat-value">{{ stat.value }}</span>
      <span class="stat-label">{{ stat.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Stat {
  value: string | number
  label: string
}

interface Props {
  stats: Stat[]
}

defineProps<Props>()
</script>

<style scoped>
.project-stats {
  display: flex;
  gap: var(--spacing-6);
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-stats {
    justify-content: space-around;
  }
}
</style>

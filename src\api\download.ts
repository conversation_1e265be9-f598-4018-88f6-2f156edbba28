import { request } from '@/api/http'

export interface AuthorizeDownloadParams {
  url: string
  releaseId?: number
  projectId?: number
}

export function authorizeDownload(data: AuthorizeDownloadParams) {
  return request<{ authorized: boolean }>({ url: '/download/authorize', method: 'POST', data, silent: true })
}

export interface TrackDownloadPayload {
  url: string
  releaseId?: number
  projectId?: number
  status: 'start' | 'success' | 'fail' | 'cancel'
  error?: string
}

// 关闭下载统计上报



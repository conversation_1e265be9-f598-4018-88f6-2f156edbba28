# 前端设计文档

## 1. 技术栈与工具

### 1.1 核心技术
- **框架**: Vue 3.x (Composition API)
- **语言**: TypeScript 5.x
- **构建工具**: Vite 5.x
- **包管理**: npm/pnpm

### 1.2 主要依赖
```json
{
  "dependencies": {
    "vue": "^3.5.18",
    "vue-router": "^4.0.0",
    "pinia": "^2.0.0",
    "axios": "^1.0.0",
    "element-plus": "^2.0.0",
    "@element-plus/icons-vue": "^2.0.0",
    "dayjs": "^1.11.0",
    "markdown-it": "^13.0.0",
    "file-saver": "^2.0.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^6.0.1",
    "@types/file-saver": "^2.0.0",
    "typescript": "~5.8.3",
    "vue-tsc": "^3.0.5",
    "sass": "^1.0.0",
    "unplugin-auto-import": "^0.17.0",
    "unplugin-vue-components": "^0.26.0"
  }
}
```

## 2. 项目结构设计

### 2.1 目录结构
```
src/
├── api/                    # API接口管理
│   ├── index.ts           # axios配置和拦截器
│   ├── auth.ts           # 认证相关API
│   ├── projects.ts       # 项目管理API
│   ├── releases.ts       # 版本发布API
│   ├── users.ts          # 用户管理API
│   └── upload.ts         # 文件上传API
├── assets/                # 静态资源
│   ├── images/           # 图片资源
│   ├── icons/            # 图标文件
│   └── styles/           # 全局样式
│       ├── variables.scss # SCSS变量
│       ├── mixins.scss   # SCSS混入
│       └── global.scss   # 全局样式
├── components/            # 公共组件
│   ├── common/           # 通用组件
│   │   ├── Loading.vue   # 加载组件
│   │   ├── Empty.vue     # 空状态组件
│   │   └── Pagination.vue # 分页组件
│   ├── layout/           # 布局组件
│   │   ├── Header.vue    # 页面头部
│   │   ├── Sidebar.vue   # 侧边栏
│   │   └── Footer.vue    # 页面底部
│   └── upload/           # 上传组件
│       ├── FileUpload.vue # 文件上传
│       └── ProgressBar.vue # 进度条
├── views/                # 页面组件
│   ├── public/           # 公开页面
│   │   ├── Home.vue      # 首页
│   │   ├── ProjectList.vue # 项目列表
│   │   ├── ProjectDetail.vue # 项目详情
│   │   └── ReleaseDetail.vue # 版本详情
│   ├── auth/             # 认证页面
│   │   └── Login.vue     # 登录页
│   ├── developer/        # 开发者页面
│   │   ├── Dashboard.vue # 工作台
│   │   ├── MyProjects.vue # 我的项目
│   │   ├── ProjectManage.vue # 项目管理
│   │   ├── ReleaseManage.vue # 版本管理
│   │   └── UploadFile.vue # 文件上传
│   └── admin/            # 管理员页面
│       ├── Dashboard.vue # 控制台
│       ├── ProjectAdmin.vue # 项目管理
│       ├── UserManage.vue # 用户管理
│       └── PermissionManage.vue # 权限管理
├── router/               # 路由配置
│   ├── index.ts         # 路由入口
│   ├── guards.ts        # 路由守卫
│   └── modules/         # 路由模块
│       ├── public.ts    # 公开路由
│       ├── developer.ts # 开发者路由
│       └── admin.ts     # 管理员路由
├── stores/               # 状态管理
│   ├── auth.ts          # 认证状态
│   ├── projects.ts      # 项目状态
│   ├── releases.ts      # 版本状态
│   └── app.ts           # 应用状态
├── types/                # TypeScript类型
│   ├── api.ts           # API响应类型
│   ├── user.ts          # 用户相关类型
│   ├── project.ts       # 项目相关类型
│   └── common.ts        # 通用类型
├── utils/                # 工具函数
│   ├── http.ts          # HTTP工具
│   ├── storage.ts       # 存储工具
│   ├── validator.ts     # 验证工具
│   ├── format.ts        # 格式化工具
│   ├── constants.ts     # 常量定义
│   └── validation-rules.ts # 验证规则配置
├── directives/           # 自定义指令
├── composables/          # 组合式函数
│   ├── useAuth.ts       # 认证相关
│   ├── useUpload.ts     # 上传相关
│   └── usePagination.ts # 分页相关
├── App.vue              # 根组件
├── main.ts              # 应用入口
└── env.d.ts             # 环境变量类型
```

## 3. 路由设计

### 3.1 路由配置

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { setupRouterGuards } from './guards'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    // 公开路由
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/public/Home.vue'),
      meta: { title: '首页', requiresAuth: false }
    },
    {
      path: '/projects',
      name: 'ProjectList',
      component: () => import('@/views/public/ProjectList.vue'),
      meta: { title: '项目列表', requiresAuth: false }
    },
    
    // 认证路由
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { title: '登录', requiresAuth: false }
    },
    
    // 开发者路由
    {
      path: '/developer',
      component: () => import('@/layouts/DeveloperLayout.vue'),
      meta: { requiresAuth: true, roles: ['developer', 'admin'] },
      children: [
        {
          path: '',
          name: 'DeveloperDashboard',
          component: () => import('@/views/developer/Dashboard.vue'),
          meta: { title: '工作台' }
        },
        {
          path: 'projects',
          name: 'MyProjects',
          component: () => import('@/views/developer/MyProjects.vue'),
          meta: { title: '我的项目' }
        }
      ]
    },
    
    // 管理员路由
    {
      path: '/admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, roles: ['admin'] },
      children: [
        {
          path: '',
          name: 'AdminDashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: { title: '管理控制台' }
        }
      ]
    }
  ]
})

setupRouterGuards(router)
export default router
```

### 3.2 路由守卫

```typescript
// router/guards.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 设置页面标题
    document.title = to.meta.title ? `${to.meta.title} - 软件发布系统` : '软件发布系统'
    
    // 检查是否需要认证
    if (to.meta.requiresAuth) {
      if (!authStore.isAuthenticated) {
        next({ name: 'Login', query: { redirect: to.fullPath } })
        return
      }
      
      // 检查角色权限
      if (to.meta.roles && !to.meta.roles.includes(authStore.user?.role)) {
        next({ name: 'Forbidden' })
        return
      }
    }
    
    next()
  })
}
```

## 4. 状态管理设计

### 4.1 认证状态 (stores/auth.ts)

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginRequest } from '@/types/user'
import { authApi } from '@/api/auth'
import { storage } from '@/utils/storage'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(storage.get('token'))
  
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isDeveloper = computed(() => user.value?.role === 'developer')
  
  // 登录
  const login = async (credentials: LoginRequest) => {
    const response = await authApi.login(credentials)
    token.value = response.data.token
    user.value = response.data.user
    storage.set('token', token.value)
  }
  
  // 退出登录
  const logout = async () => {
    try {
      await authApi.logout()
    } finally {
      token.value = null
      user.value = null
      storage.remove('token')
    }
  }
  
  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      try {
        const response = await authApi.verify()
        user.value = response.data.user
      } catch {
        logout()
      }
    }
  }
  
  return {
    user: readonly(user),
    token: readonly(token),
    isAuthenticated,
    isAdmin,
    isDeveloper,
    login,
    logout,
    initUser
  }
})
```

### 4.2 项目状态 (stores/projects.ts)

```typescript
import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Project, ProjectListParams } from '@/types/project'
import { projectApi } from '@/api/projects'

export const useProjectStore = defineStore('projects', () => {
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const loading = ref(false)
  const total = ref(0)
  
  // 获取项目列表
  const fetchProjects = async (params: ProjectListParams) => {
    loading.value = true
    try {
      const response = await projectApi.getList(params)
      projects.value = response.data.projects
      total.value = response.data.total
    } finally {
      loading.value = false
    }
  }
  
  // 获取项目详情
  const fetchProjectDetail = async (id: number) => {
    const response = await projectApi.getDetail(id)
    currentProject.value = response.data
    return response.data
  }
  
  return {
    projects: readonly(projects),
    currentProject: readonly(currentProject),
    loading: readonly(loading),
    total: readonly(total),
    fetchProjects,
    fetchProjectDetail
  }
})
```

## 5. 组件设计

### 5.1 文件上传组件

```vue
<!-- components/upload/FileUpload.vue -->
<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :before-upload="beforeUpload"
      :on-progress="onProgress"
      :on-success="onSuccess"
      :on-error="onError"
      :show-file-list="false"
      :auto-upload="autoUpload"
      drag
    >
      <div class="upload-content">
        <el-icon class="upload-icon"><UploadFilled /></el-icon>
        <div class="upload-text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <div class="upload-tip">
          支持任意格式文件，大小不超过200MB
        </div>
      </div>
    </el-upload>
    
    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress
        :percentage="progress"
        :status="progressStatus"
        :stroke-width="6"
      />
      <div class="progress-text">
        {{ progressText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadFile, UploadProgressEvent } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

interface Props {
  projectId?: number
  autoUpload?: boolean
}

interface Emits {
  (e: 'success', file: UploadFile, response: any): void
  (e: 'error', error: Error): void
}

const props = withDefaults(defineProps<Props>(), {
  autoUpload: true
})

const emit = defineEmits<Emits>()

const authStore = useAuthStore()
const uploadRef = ref()
const uploading = ref(false)
const progress = ref(0)

const uploadUrl = computed(() => '/api/developer/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

const uploadData = computed(() => ({
  projectId: props.projectId
}))

const progressStatus = computed(() => {
  if (progress.value === 100) return 'success'
  if (progress.value === 0) return 'exception'
  return undefined
})

const progressText = computed(() => {
  if (progress.value === 100) return '上传完成'
  if (uploading.value) return `上传中... ${progress.value}%`
  return ''
})

const beforeUpload = (file: File) => {
  const isValidSize = file.size <= 200 * 1024 * 1024 // 200MB
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过200MB')
    return false
  }
  
  uploading.value = true
  progress.value = 0
  return true
}

const onProgress = (event: UploadProgressEvent) => {
  progress.value = Math.round(event.percent)
}

const onSuccess = (response: any, file: UploadFile) => {
  uploading.value = false
  progress.value = 100
  ElMessage.success('文件上传成功')
  emit('success', file, response)
}

const onError = (error: Error) => {
  uploading.value = false
  progress.value = 0
  ElMessage.error('文件上传失败')
  emit('error', error)
}

// 手动上传
const submit = () => {
  uploadRef.value?.submit()
}

defineExpose({
  submit
})
</script>

<style scoped lang="scss">
.file-upload {
  .upload-content {
    text-align: center;
    padding: 40px 20px;
    
    .upload-icon {
      font-size: 48px;
      color: #409eff;
      margin-bottom: 16px;
    }
    
    .upload-text {
      font-size: 16px;
      color: #606266;
      margin-bottom: 8px;
      
      em {
        color: #409eff;
        font-style: normal;
      }
    }
    
    .upload-tip {
      font-size: 14px;
      color: #909399;
    }
  }
  
  .upload-progress {
    margin-top: 20px;
    
    .progress-text {
      text-align: center;
      margin-top: 8px;
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>
```

### 5.2 项目卡片组件

```vue
<!-- components/common/ProjectCard.vue -->
<template>
  <el-card class="project-card" :body-style="{ padding: '20px' }" shadow="hover">
    <div class="project-header">
      <div class="project-info">
        <h3 class="project-name">{{ project.name }}</h3>
        <el-tag :type="categoryType" size="small">{{ categoryText }}</el-tag>
      </div>
      <div class="project-stats">
        <el-statistic
          :value="project.totalDownloads"
          title="总下载量"
          :value-style="{ fontSize: '18px', color: '#409eff' }"
        />
      </div>
    </div>
    
    <p class="project-description">{{ project.description }}</p>
    
    <div class="project-version">
      <span class="version-label">最新版本:</span>
      <el-tag type="success" size="small">{{ project.latestVersion }}</el-tag>
    </div>
    
    <div class="project-footer">
      <div class="project-time">
        更新时间: {{ formatTime(project.latestReleaseAt) }}
      </div>
      <div class="project-actions">
        <el-button type="primary" size="small" @click="viewProject">
          查看详情
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import type { Project } from '@/types/project'
import { formatTime } from '@/utils/format'

interface Props {
  project: Project
}

const props = defineProps<Props>()
const router = useRouter()

const categoryType = computed(() => {
  return props.project.category === 'firmware' ? 'warning' : 'info'
})

const categoryText = computed(() => {
  return props.project.category === 'firmware' ? '固件' : 'APP'
})

const viewProject = () => {
  router.push(`/projects/${props.project.id}`)
}
</script>

<style scoped lang="scss">
.project-card {
  margin-bottom: 20px;
  
  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    
    .project-info {
      flex: 1;
      
      .project-name {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .project-stats {
      margin-left: 20px;
    }
  }
  
  .project-description {
    margin: 0 0 12px 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .project-version {
    margin-bottom: 16px;
    
    .version-label {
      font-size: 14px;
      color: #909399;
      margin-right: 8px;
    }
  }
  
  .project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .project-time {
      font-size: 12px;
      color: #c0c4cc;
    }
  }
}
</style>
```

## 6. 页面设计

### 6.1 首页设计 (views/public/Home.vue)

```vue
<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">软件/固件发布系统</h1>
      <p class="page-subtitle">为您提供最新的软件和固件下载服务</p>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="项目总数" :value="stats.totalProjects" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="版本总数" :value="stats.totalReleases" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总下载量" :value="stats.totalDownloads" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="今日下载" :value="stats.todayDownloads" />
        </el-col>
      </el-row>
    </div>
    
    <!-- 分类导航 -->
    <div class="category-section">
      <el-tabs v-model="activeCategory" @tab-change="onCategoryChange">
        <el-tab-pane label="全部" name="all" />
        <el-tab-pane label="固件" name="firmware" />
        <el-tab-pane label="APP" name="app" />
      </el-tabs>
    </div>
    
    <!-- 项目列表 -->
    <div class="projects-section">
      <el-row :gutter="20" v-loading="loading">
        <el-col :span="8" v-for="project in projects" :key="project.id">
          <ProjectCard :project="project" />
        </el-col>
      </el-row>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[12, 24, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onPageSizeChange"
          @current-change="onPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ProjectCard from '@/components/common/ProjectCard.vue'
import { useProjectStore } from '@/stores/projects'
import { publicApi } from '@/api/public'

const projectStore = useProjectStore()

const activeCategory = ref('all')
const currentPage = ref(1)
const pageSize = ref(12)
const loading = ref(false)
const stats = ref({
  totalProjects: 0,
  totalReleases: 0,
  totalDownloads: 0,
  todayDownloads: 0
})

const { projects, total } = storeToRefs(projectStore)

const fetchProjects = async () => {
  await projectStore.fetchProjects({
    category: activeCategory.value === 'all' ? undefined : activeCategory.value,
    page: currentPage.value,
    size: pageSize.value
  })
}

const fetchStats = async () => {
  const response = await publicApi.getStats()
  stats.value = response.data
}

const onCategoryChange = () => {
  currentPage.value = 1
  fetchProjects()
}

const onPageChange = () => {
  fetchProjects()
}

const onPageSizeChange = () => {
  currentPage.value = 1
  fetchProjects()
}

onMounted(() => {
  fetchProjects()
  fetchStats()
})
</script>

<style scoped lang="scss">
.home-page {
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    .page-title {
      font-size: 32px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
    }
    
    .page-subtitle {
      font-size: 16px;
      color: #606266;
      margin: 0;
    }
  }
  
  .stats-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;
  }
  
  .category-section {
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
}
</style>
```

## 7. 响应式设计

### 7.1 断点定义

```scss
// assets/styles/variables.scss
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
```

### 7.2 移动端适配

```vue
<!-- 移动端项目卡片适配 -->
<style scoped lang="scss">
.project-card {
  @include respond-to(xs) {
    .project-header {
      flex-direction: column;
      align-items: flex-start;
      
      .project-stats {
        margin: 12px 0 0 0;
        width: 100%;
      }
    }
    
    .project-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
}
</style>
```

## 8. 国际化支持

### 8.1 i18n配置 (可选功能)

```typescript
// main.ts
import { createI18n } from 'vue-i18n'

const messages = {
  'zh-CN': {
    common: {
      confirm: '确认',
      cancel: '取消',
      submit: '提交'
    },
    project: {
      name: '项目名称',
      description: '项目描述'
    }
  },
  'en-US': {
    common: {
      confirm: 'Confirm',
      cancel: 'Cancel',
      submit: 'Submit'
    },
    project: {
      name: 'Project Name',
      description: 'Project Description'
    }
  }
}

const i18n = createI18n({
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages
})

app.use(i18n)
```

## 9. 输入验证规则

### 9.1 验证规则配置

```typescript
// utils/validation-rules.ts
export const ValidationRules = {
  // 版本号验证
  version: {
    pattern: /^v\d+\.\d+\.\d+\.\d+$/,
    message: '版本号格式必须为 v主版本.次版本.修订版.构建版 (如: v1.0.0.0)',
    example: 'v1.0.0.0'
  },

  // 项目名称验证
  projectName: {
    minLength: 2,
    maxLength: 50,
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/,
    forbidden: ['admin', 'system', 'test', 'root'],
    message: '项目名称长度为2-50字符，只能包含中文、英文、数字、空格、横线和下划线'
  },

  // 项目描述验证
  projectDescription: {
    maxLength: 500,
    message: '项目描述不能超过500字符'
  },

  // 版本标题验证
  versionTitle: {
    minLength: 1,
    maxLength: 200,
    message: '版本标题长度为1-200字符'
  },

  // 设备类型验证
  deviceType: {
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/,
    maxLength: 50,
    message: '设备类型只能包含中文、英文、数字、空格、横线和下划线，不超过50字符'
  },

  // 文件验证
  file: {
    maxSize: 200 * 1024 * 1024, // 200MB
    allowedTypes: ['*'], // 允许所有类型
    blockedTypes: [
      'application/x-msdownload',    // .exe
      'application/javascript',      // .js (可执行)
      'application/x-sh',           // .sh
      'application/x-bat'           // .bat
    ],
    message: '文件大小不能超过200MB'
  },

  // 用户名验证
  username: {
    minLength: 3,
    maxLength: 20,
    pattern: /^[a-zA-Z0-9_]+$/,
    message: '用户名长度为3-20字符，只能包含英文、数字和下划线'
  },

  // 密码验证
  password: {
    minLength: 8,
    maxLength: 50,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]+$/,
    message: '密码长度为8-50字符，必须包含大小写字母和数字'
  },

  // 邮箱验证
  email: {
    pattern: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
    message: '请输入有效的邮箱地址'
  }
};

// 验证工具函数
export class Validator {
  static validateVersion(version: string): ValidationResult {
    const rule = ValidationRules.version;
    if (!rule.pattern.test(version)) {
      return {
        valid: false,
        message: rule.message,
        field: 'version'
      };
    }
    return { valid: true };
  }

  static validateProjectName(name: string): ValidationResult {
    const rule = ValidationRules.projectName;
    
    if (name.length < rule.minLength || name.length > rule.maxLength) {
      return {
        valid: false,
        message: `项目名称长度必须在${rule.minLength}-${rule.maxLength}字符之间`,
        field: 'projectName'
      };
    }

    if (!rule.pattern.test(name)) {
      return {
        valid: false,
        message: rule.message,
        field: 'projectName'
      };
    }

    if (rule.forbidden.some(word => name.toLowerCase().includes(word))) {
      return {
        valid: false,
        message: '项目名称不能包含保留关键字',
        field: 'projectName'
      };
    }

    return { valid: true };
  }

  static validateFile(file: File): ValidationResult {
    const rule = ValidationRules.file;
    
    // 文件大小验证
    if (file.size > rule.maxSize) {
      return {
        valid: false,
        message: `文件大小不能超过${Math.round(rule.maxSize / 1024 / 1024)}MB`,
        field: 'file'
      };
    }

    // 文件类型验证
    if (rule.blockedTypes.includes(file.type)) {
      return {
        valid: false,
        message: '不支持该文件类型，存在安全风险',
        field: 'file'
      };
    }

    return { valid: true };
  }

  static validateForm(data: Record<string, any>, rules: string[]): ValidationResult[] {
    const results: ValidationResult[] = [];
    
    for (const field of rules) {
      const value = data[field];
      let result: ValidationResult;

      switch (field) {
        case 'version':
          result = this.validateVersion(value);
          break;
        case 'projectName':
          result = this.validateProjectName(value);
          break;
        case 'file':
          result = this.validateFile(value);
          break;
        default:
          result = { valid: true };
      }

      if (!result.valid) {
        results.push(result);
      }
    }

    return results;
  }
}

interface ValidationResult {
  valid: boolean;
  message?: string;
  field?: string;
}
```

### 9.2 表单验证组件

```vue
<!-- components/common/ValidatedInput.vue -->
<template>
  <div class="validated-input">
    <el-input
      v-model="inputValue"
      :placeholder="placeholder"
      :disabled="disabled"
      @blur="validateInput"
      @input="onInput"
      :class="{ 'is-error': hasError }"
    />
    <div v-if="hasError" class="error-message">
      {{ errorMessage }}
    </div>
    <div v-if="showPattern" class="pattern-hint">
      {{ patternHint }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Validator, ValidationRules } from '@/utils/validation-rules'

interface Props {
  modelValue: string
  validationType: keyof typeof ValidationRules
  placeholder?: string
  disabled?: boolean
  showPattern?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'validation-change', valid: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const inputValue = ref(props.modelValue)
const errorMessage = ref('')
const hasError = computed(() => !!errorMessage.value)

const patternHint = computed(() => {
  const rule = ValidationRules[props.validationType]
  return rule.example ? `格式示例: ${rule.example}` : rule.message
})

const validateInput = () => {
  let result
  switch (props.validationType) {
    case 'version':
      result = Validator.validateVersion(inputValue.value)
      break
    case 'projectName':
      result = Validator.validateProjectName(inputValue.value)
      break
    default:
      result = { valid: true }
  }

  errorMessage.value = result.valid ? '' : result.message || ''
  emit('validation-change', result.valid)
}

const onInput = (value: string) => {
  inputValue.value = value
  emit('update:modelValue', value)
  
  // 实时验证（防抖）
  clearTimeout(validateTimeout.value)
  validateTimeout.value = setTimeout(validateInput, 500)
}

const validateTimeout = ref<NodeJS.Timeout>()

watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 组件挂载时验证
onMounted(() => {
  if (inputValue.value) {
    validateInput()
  }
})
</script>

<style scoped lang="scss">
.validated-input {
  .is-error {
    border-color: #f56c6c;
  }
  
  .error-message {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 4px;
  }
  
  .pattern-hint {
    color: #909399;
    font-size: 12px;
    margin-top: 4px;
  }
}
</style>
```

### 9.3 文件上传验证

```typescript
// composables/useFileValidation.ts
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Validator, ValidationRules } from '@/utils/validation-rules'

export function useFileValidation() {
  const isValidating = ref(false)

  const validateFile = async (file: File): Promise<boolean> => {
    isValidating.value = true

    try {
      // 基础验证
      const basicResult = Validator.validateFile(file)
      if (!basicResult.valid) {
        ElMessage.error(basicResult.message)
        return false
      }

      // 文件名验证
      const fileName = file.name
      if (fileName.length > 255) {
        ElMessage.error('文件名长度不能超过255字符')
        return false
      }

      // 恶意文件检查（基于文件名）
      const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.vbs', '.js']
      const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      
      if (dangerousExtensions.includes(fileExtension)) {
        ElMessage.error('不允许上传可执行文件')
        return false
      }

      // 文件内容初步检查（检查文件头）
      const isValidContent = await validateFileContent(file)
      if (!isValidContent) {
        ElMessage.error('文件内容验证失败')
        return false
      }

      return true
    } catch (error) {
      ElMessage.error('文件验证过程中发生错误')
      return false
    } finally {
      isValidating.value = false
    }
  }

  const validateFileContent = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        const arrayBuffer = e.target?.result as ArrayBuffer
        const uint8Array = new Uint8Array(arrayBuffer.slice(0, 4))
        
        // 检查常见的可执行文件魔数
        const magicNumbers = [
          [0x4D, 0x5A], // PE文件 (.exe, .dll)
          [0x7F, 0x45, 0x4C, 0x46], // ELF文件 (Linux可执行文件)
        ]

        for (const magic of magicNumbers) {
          let matches = true
          for (let i = 0; i < magic.length; i++) {
            if (uint8Array[i] !== magic[i]) {
              matches = false
              break
            }
          }
          if (matches) {
            resolve(false) // 发现可执行文件特征
            return
          }
        }

        resolve(true) // 文件内容验证通过
      }

      reader.onerror = () => resolve(false)
      reader.readAsArrayBuffer(file.slice(0, 4))
    })
  }

  return {
    isValidating,
    validateFile
  }
}
```

## 10. 性能优化

### 10.1 代码分割

```typescript
// 路由懒加载
const routes = [
  {
    path: '/admin',
    component: () => import(
      /* webpackChunkName: "admin" */ 
      '@/views/admin/Dashboard.vue'
    )
  }
]
```

### 10.2 组件缓存

```vue
<!-- keep-alive缓存 -->
<template>
  <router-view v-slot="{ Component, route }">
    <keep-alive :include="['ProjectList', 'MyProjects']">
      <component :is="Component" :key="route.fullPath" />
    </keep-alive>
  </router-view>
</template>
```

这个前端设计文档提供了完整的Vue3+TypeScript项目架构，包括组件设计、状态管理、路由配置等，为1000用户规模的内网系统提供了良好的用户体验和可维护性。

import { createApp } from 'vue'
import './style.css'
import '@/styles/index.scss'
import App from './App.vue'
import { setupGlobalErrorHandlers, reportError } from '@/utils/errorMonitor'
import { setupStore, useAppStore } from '@/stores'
import router from '@/router'
// HTTP拦截器暂时保留（用于认证等功能）
import { setupHttpInterceptors } from '@/api/setupInterceptors'
import { setupRouterProgress } from '@/plugins/routerProgress'

const app = createApp(App)
setupStore(app)
app.use(router)
setupHttpInterceptors(router)
setupRouterProgress(router)

// Vue 全局错误
app.config.errorHandler = (err: unknown, _instance, info: string) => {
  const error = err instanceof Error ? err : new Error(String(err))
  reportError({
    type: 'vue',
    message: error.message,
    stack: error.stack,
    time: Date.now(),
    extra: { info },
  })
}

setupGlobalErrorHandlers()

// 初始化全局 App Store（主题、语言、等）
const appStore = useAppStore()
appStore.initialize()

app.mount('#app')

import { defineConfig, loadEnv, type Plugin, type ViteDevServer } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { fileURLToPath, URL } from 'node:url'
import fs from 'node:fs'
import path from 'node:path'

function pathToRegex(pattern: string): { regex: RegExp; keys: string[] } {
  const keys: string[] = []
  const regexStr = pattern
    .replace(/([.+*?=^!${}()|[\]\\])/g, '\\$1')
    .replace(/:(\w+)/g, (_: string, key: string) => {
      keys.push(key)
      return '([^/]+)'
    })
  return { regex: new RegExp(`^${regexStr}$`), keys }
}

function createDevMockPlugin(enable: boolean): Plugin {
  return {
    name: 'dev-mock',
    apply: 'serve',
    async configureServer(server: ViteDevServer) {
      if (!enable) return

      const mockDir = path.resolve(process.cwd(), 'mock')
      if (!fs.existsSync(mockDir)) return

      type Route = {
        method: string
        url: string
        regex: RegExp
        keys: string[]
        handler: (ctx: {
          req: import('node:http').IncomingMessage
          res: import('node:http').ServerResponse
          params: Record<string, string>
          body: unknown
          query: Record<string, string>
        }) => unknown | Promise<unknown>
      }

      async function importRoutes(): Promise<Route[]> {
        const entries = fs
          .readdirSync(mockDir)
          .filter((f) => f.endsWith('.js') || f.endsWith('.mjs'))
        const routes: Route[] = []
        for (const file of entries) {
          const fileUrl = new URL(`file:///${path.join(mockDir, file).replace(/\\/g, '/')}`)
          const mod = await import(fileUrl.href)
          const list: any[] = (mod.default || mod.routes || []) as any[]
          routes.push(
            ...list.map((r) => {
              const method = (r.method || 'GET').toUpperCase()
              const url = r.path || r.url
              const { regex, keys } = pathToRegex(url)
              return { method, url, regex, keys, handler: r.handler || r.response }
            })
          )
        }
        return routes
      }

      async function readBody(req: import('node:http').IncomingMessage): Promise<unknown> {
        return await new Promise((resolve) => {
          const chunks: Buffer[] = []
          req
            .on('data', (c: Buffer) => chunks.push(c))
            .on('end', () => {
              const raw = Buffer.concat(chunks).toString('utf8')
              try {
                resolve(raw ? JSON.parse(raw) : undefined)
              } catch (_: unknown) {
                resolve(raw)
              }
            })
        })
      }

      let routes = await importRoutes()

      server.watcher.add(mockDir)
      server.watcher.on('change', async (changed: string) => {
        if (!changed.startsWith(mockDir)) return
        routes = await importRoutes()
        server.ws.send({ type: 'full-reload' })
      })

      server.middlewares.use(async (
        req: import('node:http').IncomingMessage,
        res: import('node:http').ServerResponse,
        next: (err?: any) => void
      ) => {
        const url = (req.url || '').split('?')[0]
        const method = (req.method || 'GET').toUpperCase()

        for (const r of routes) {
          if (r.method !== method) continue
          const match = r.regex.exec(url)
          if (!match) continue
          const params: Record<string, string> = {}
          r.keys.forEach((k: string, i: number) => (params[k] = String(match[i + 1])))
          const body = await readBody(req)
          const query = Object.fromEntries(new URL(req.url || '', 'http://localhost').searchParams.entries())
          try {
            const result = await r.handler({ req, res, params, body, query })
            res.statusCode = 200
            res.setHeader('Content-Type', 'application/json; charset=utf-8')
            res.end(JSON.stringify(result))
          } catch (e: any) {
            res.statusCode = 500
            res.setHeader('Content-Type', 'application/json; charset=utf-8')
            res.end(JSON.stringify({ code: 500, message: (e && e.message) || 'Mock Error' }))
          }
          return
        }
        next()
      })
    },
  }
}

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isDev = mode === 'development'
  const useMock = env.VITE_USE_MOCK ? env.VITE_USE_MOCK === 'true' : isDev
  return {
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@base': fileURLToPath(new URL('./src/components/base', import.meta.url)),
      },
    },
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'src/auto-imports.d.ts',
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        dts: 'src/components.d.ts',
        resolvers: [ElementPlusResolver()],
      }),
      // Dev-only mock middleware
      // Dev mock middleware (no-op if disabled)
      // @ts-ignore
      createDevMockPlugin(useMock),
    ],
    server: {
      port: 5173,
      strictPort: true,
      open: false,
    },
  }
})

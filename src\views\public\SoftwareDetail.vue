<template>
  <div class="page page-software-detail">
    <div class="header">
      <h1 class="title">{{ title }}</h1>
      <div class="meta">
        <span>所属项目：#{{ projectId }}</span>
        <span>类别：{{ mapArtifact(artifact) }}</span>
      </div>
    </div>

    <VersionList 
      :versions="versions"
      :total="releases.total"
      :page="releases.query.page"
      :pageSize="releases.query.pageSize"
      @change-page="handleChangePage"
    />
  </div>
  
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useReleasesStore } from '@/stores/releases'
import VersionList from '@/components/business/VersionList.vue'

const route = useRoute()
const releases = useReleasesStore()

const projectId = computed(() => Number(route.params.projectId))
const artifact = computed(() => String(route.params.artifact || 'firmware'))
const title = computed(() => `${mapArtifact(artifact.value)} 历史版本`)

const versions = computed(() =>
  releases.list.map((r) => ({
    id: String(r.id),
    number: r.version,
    description: r.changelog,
    date: new Date(r.createdAt).toLocaleDateString(),
    size: r.size ? `${(r.size / (1024 * 1024)).toFixed(1)} MB` : '—',
    type: r.type === 'stable' ? '稳定版' : r.type === 'beta' ? '测试版' : '开发版',
    downloadUrl: r.downloadUrl,
    filename: r.downloadUrl ? r.downloadUrl.split('/').pop() : undefined,
    sizeBytes: r.size,
  }))
)

onMounted(async () => {
  await releases.loadByProject(projectId.value, { artifact: artifact.value, sortBy: 'createdAt', order: 'desc' })
})

async function handleChangePage(page: number) {
  await releases.loadByProject(projectId.value, { page, artifact: artifact.value })
}


function mapArtifact(a?: string) {
  if (a === 'firmware') return '固件'
  if (a === 'android-demo') return 'Android Demo'
  if (a === 'sdk') return 'SDK'
  if (a === 'app') return '应用'
  if (a === 'library') return '库'
  return '软件'
}
</script>

<style scoped>
.page { padding: var(--spacing-6) 0; }
.header { margin-bottom: var(--spacing-6); }
.title { margin: 0 0 var(--spacing-2) 0; }
.meta { display: flex; gap: var(--spacing-3); color: var(--color-text-3); font-size: var(--font-size-sm); }
</style>



<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card glass">
        <!-- Header -->
        <div class="login-header">
          <div class="logo-section">
            <h1 class="login-title text-gradient">开发者登录</h1>
            <p class="login-subtitle">管理您的项目和版本发布</p>
          </div>
        </div>

        <!-- Form -->
        <form class="login-form" @submit.prevent="handleLogin">
          <div class="form-group">
            <label class="form-label">开发者账户</label>
            <div class="input-wrapper">
              <input
                v-model="formData.username"
                type="text"
                class="form-input"
                placeholder="请输入开发者用户名"
                required
                :disabled="loading"
              />
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">密码</label>
            <div class="input-wrapper">
              <input
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                placeholder="请输入密码"
                required
                :disabled="loading"
              />
              <button
                type="button"
                class="password-toggle"
                @click="showPassword = !showPassword"
                :disabled="loading"
              >
                {{ showPassword ? '隐藏' : '显示' }}
              </button>
            </div>
          </div>

          <div class="form-options">
            <label class="checkbox-wrapper">
              <input type="checkbox" v-model="rememberMe" :disabled="loading" />
              <span class="checkbox-label">记住登录状态</span>
            </label>
          </div>

          <!-- Error message -->
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>

          <!-- Submit button -->
          <button type="submit" class="btn btn-primary btn-lg login-btn" :disabled="loading">
            <span v-if="loading">登录中...</span>
            <span v-else>登录到开发者中心</span>
          </button>
        </form>

        <!-- Footer -->
        <div class="login-footer">
          <p class="footer-text">
            需要管理员权限？ 
            <router-link to="/admin/login" class="footer-link">管理员登录</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDeveloperAuthStore } from '@/stores/developerAuth'

const router = useRouter()
const route = useRoute()
const developerAuth = useDeveloperAuthStore()

// 表单数据（预填充测试账号）
const formData = reactive({
  username: 'admin',
  password: '123456'
})

// 状态管理
const loading = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)
const errorMessage = ref('')

// 处理登录
async function handleLogin() {
  loading.value = true
  errorMessage.value = ''

  try {
    await developerAuth.login(formData.username, formData.password)
    
    // 成功后跳转到开发者中心
    const redirect = (route.query.redirect as string) || '/developer'
    router.replace(redirect)
  } catch (error: any) {
    errorMessage.value = error?.message || '登录失败，请检查用户名和密码'
  } finally {
    loading.value = false
  }
}

// 注：键盘快捷键已通过表单的submit事件处理
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-2) 0%, var(--bg-3) 100%);
  padding: var(--spacing-4);
}

.login-container {
  width: 100%;
  max-width: 420px;
}

.login-card {
  padding: var(--spacing-8);
  width: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.logo-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.login-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.login-subtitle {
  font-size: var(--font-size-md);
  color: var(--color-text-3);
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-2);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  height: 48px;
  padding: 0 var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-1);
  color: var(--color-text-1);
  font-size: var(--font-size-md);
  transition: all var(--transition-base);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-3);
  background: none;
  border: none;
  color: var(--color-text-3);
  font-size: var(--font-size-sm);
  cursor: pointer;
  padding: var(--spacing-1);
}

.password-toggle:hover {
  color: var(--color-primary);
}

.form-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
}

.checkbox-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-2);
}

.error-message {
  background: var(--color-danger-light);
  color: var(--color-danger-dark);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  text-align: center;
}

.login-btn {
  width: 100%;
  height: 48px;
}

.login-footer {
  margin-top: var(--spacing-6);
  text-align: center;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-color);
}

.footer-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-3);
  margin: 0;
}

.footer-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.footer-link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login-page {
    padding: var(--spacing-3);
  }
  
  .login-card {
    padding: var(--spacing-6);
  }
  
  .login-title {
    font-size: var(--font-size-2xl);
  }
}
</style>
